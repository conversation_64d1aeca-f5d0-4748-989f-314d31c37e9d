# Systemd service file for Airbnb Clone
# Place this file in /etc/systemd/system/airbnb-clone.service

[Unit]
Description=Airbnb Clone Django Application
After=network.target

[Service]
Type=exec
User=www-data  # Replace with your user
Group=www-data  # Replace with your group
WorkingDirectory=/path/to/your/project  # Replace with your project path
Environment=DJANGO_SETTINGS_MODULE=airbnb_clone.settings_production
Environment=PATH=/path/to/your/venv/bin  # Replace with your virtual environment path
ExecStart=/path/to/your/venv/bin/gunicorn --bind 127.0.0.1:8000 --workers 3 wsgi_production:application
ExecReload=/bin/kill -s HUP $MAINPID
Restart=always
RestartSec=3

# Security settings
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=/path/to/your/project  # Replace with your project path

[Install]
WantedBy=multi-user.target
