<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Welcome to Airbnb - Get Started</title>
    {% load static %}
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'nestria-red': '#FF5A5F',
                        'airbnb-pink': '#E91E63',
                    }
                }
            }
        }
    </script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- Header -->
    <header class="bg-white shadow-sm border-b">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <div class="flex items-center">
                    <a href="{% url 'core:home' %}" class="text-2xl font-bold text-airbnb-pink">
                        <svg width="32" height="32" viewBox="0 0 32 32" class="mr-2">
                            <path fill="currentColor" d="M16 0C7.2 0 0 7.2 0 16s7.2 16 16 16 16-7.2 16-16S24.8 0 16 0zm0 29C8.8 29 3 23.2 3 16S8.8 3 16 3s13 5.8 13 13-5.8 13-13 13z"/>
                            <path fill="currentColor" d="M16 8c-4.4 0-8 3.6-8 8s3.6 8 8 8 8-3.6 8-8-3.6-8-8-8zm0 13c-2.8 0-5-2.2-5-5s2.2-5 5-5 5 2.2 5 5-2.2 5-5 5z"/>
                        </svg>
                        airbnb
                    </a>
                </div>
                <div class="flex items-center space-x-4">
                    <span class="text-sm text-gray-600">Welcome, {{ user.first_name }}!</span>
                    <a href="{% url 'core:home' %}" class="text-sm text-gray-600 hover:text-gray-900">
                        Skip for now
                    </a>
                </div>
            </div>
        </div>
    </header>

    <!-- Onboarding Content -->
    <div class="max-w-4xl mx-auto px-4 py-12">
        <!-- Progress Indicator -->
        <div class="flex justify-center mb-12">
            <div class="flex items-center space-x-4">
                <div class="flex items-center">
                    <div class="w-8 h-8 bg-airbnb-pink text-white rounded-full flex items-center justify-center text-sm font-semibold">1</div>
                    <span class="ml-2 text-sm font-medium text-airbnb-pink">Discover</span>
                </div>
                <div class="w-8 h-0.5 bg-gray-300"></div>
                <div class="flex items-center">
                    <div class="w-8 h-8 bg-gray-300 text-gray-600 rounded-full flex items-center justify-center text-sm font-semibold">2</div>
                    <span class="ml-2 text-sm font-medium text-gray-600">Book</span>
                </div>
                <div class="w-8 h-0.5 bg-gray-300"></div>
                <div class="flex items-center">
                    <div class="w-8 h-8 bg-gray-300 text-gray-600 rounded-full flex items-center justify-center text-sm font-semibold">3</div>
                    <span class="ml-2 text-sm font-medium text-gray-600">Enjoy</span>
                </div>
            </div>
        </div>

        <!-- Onboarding Steps -->
        <div id="onboarding-container" class="relative">
            <!-- Step 1: Discover -->
            <div id="step-1" class="onboarding-step">
                <div class="text-center mb-8">
                    <div class="w-24 h-24 bg-nestria-red rounded-full flex items-center justify-center mx-auto mb-6">
                        <i class="fas fa-search text-white text-3xl"></i>
                    </div>
                    <h1 class="text-3xl font-bold text-gray-900 mb-4">Discover Amazing Places</h1>
                    <p class="text-lg text-gray-600 max-w-2xl mx-auto">
                        Explore thousands of unique accommodations around the world. From cozy apartments to luxury villas, 
                        find the perfect place for your next adventure.
                    </p>
                </div>

                <div class="grid md:grid-cols-3 gap-6 mb-8">
                    <div class="bg-white rounded-lg p-6 shadow-sm border">
                        <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-4">
                            <i class="fas fa-map-marker-alt text-blue-600 text-xl"></i>
                        </div>
                        <h3 class="font-semibold text-gray-900 mb-2">Search by Location</h3>
                        <p class="text-sm text-gray-600">Enter any city, neighborhood, or landmark to find nearby accommodations.</p>
                    </div>

                    <div class="bg-white rounded-lg p-6 shadow-sm border">
                        <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mb-4">
                            <i class="fas fa-filter text-green-600 text-xl"></i>
                        </div>
                        <h3 class="font-semibold text-gray-900 mb-2">Filter Your Search</h3>
                        <p class="text-sm text-gray-600">Use filters for price, amenities, property type, and more to find exactly what you need.</p>
                    </div>

                    <div class="bg-white rounded-lg p-6 shadow-sm border">
                        <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mb-4">
                            <i class="fas fa-heart text-purple-600 text-xl"></i>
                        </div>
                        <h3 class="font-semibold text-gray-900 mb-2">Save Favorites</h3>
                        <p class="text-sm text-gray-600">Create wishlists and save properties you love for easy access later.</p>
                    </div>
                </div>

                <div class="text-center">
                    <button onclick="nextStep()" class="bg-airbnb-pink text-white px-8 py-3 rounded-lg font-semibold hover:bg-pink-600 transition-all">
                        Next: Learn How to Book
                    </button>
                </div>
            </div>

            <!-- Step 2: Book -->
            <div id="step-2" class="onboarding-step hidden">
                <div class="text-center mb-8">
                    <div class="w-24 h-24 bg-nestria-red rounded-full flex items-center justify-center mx-auto mb-6">
                        <i class="fas fa-calendar-check text-white text-3xl"></i>
                    </div>
                    <h1 class="text-3xl font-bold text-gray-900 mb-4">Book with Confidence</h1>
                    <p class="text-lg text-gray-600 max-w-2xl mx-auto">
                        Our secure booking process makes it easy to reserve your perfect stay. 
                        Communicate with hosts and get all the details you need.
                    </p>
                </div>

                <div class="grid md:grid-cols-3 gap-6 mb-8">
                    <div class="bg-white rounded-lg p-6 shadow-sm border">
                        <div class="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center mb-4">
                            <i class="fas fa-shield-alt text-yellow-600 text-xl"></i>
                        </div>
                        <h3 class="font-semibold text-gray-900 mb-2">Secure Payments</h3>
                        <p class="text-sm text-gray-600">Your payment information is protected with bank-level security.</p>
                    </div>

                    <div class="bg-white rounded-lg p-6 shadow-sm border">
                        <div class="w-12 h-12 bg-indigo-100 rounded-lg flex items-center justify-center mb-4">
                            <i class="fas fa-comments text-indigo-600 text-xl"></i>
                        </div>
                        <h3 class="font-semibold text-gray-900 mb-2">Message Hosts</h3>
                        <p class="text-sm text-gray-600">Ask questions and get local tips directly from your host before you arrive.</p>
                    </div>

                    <div class="bg-white rounded-lg p-6 shadow-sm border">
                        <div class="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center mb-4">
                            <i class="fas fa-undo text-red-600 text-xl"></i>
                        </div>
                        <h3 class="font-semibold text-gray-900 mb-2">Flexible Cancellation</h3>
                        <p class="text-sm text-gray-600">Many listings offer flexible cancellation policies for peace of mind.</p>
                    </div>
                </div>

                <div class="text-center space-x-4">
                    <button onclick="prevStep()" class="bg-gray-200 text-gray-700 px-6 py-3 rounded-lg font-semibold hover:bg-gray-300 transition-all">
                        Previous
                    </button>
                    <button onclick="nextStep()" class="bg-airbnb-pink text-white px-8 py-3 rounded-lg font-semibold hover:bg-pink-600 transition-all">
                        Next: Enjoy Your Stay
                    </button>
                </div>
            </div>

            <!-- Step 3: Enjoy -->
            <div id="step-3" class="onboarding-step hidden">
                <div class="text-center mb-8">
                    <div class="w-24 h-24 bg-nestria-red rounded-full flex items-center justify-center mx-auto mb-6">
                        <i class="fas fa-star text-white text-3xl"></i>
                    </div>
                    <h1 class="text-3xl font-bold text-gray-900 mb-4">Enjoy Your Experience</h1>
                    <p class="text-lg text-gray-600 max-w-2xl mx-auto">
                        Make the most of your stay with our support and community features. 
                        Share your experience and help other travelers.
                    </p>
                </div>

                <div class="grid md:grid-cols-3 gap-6 mb-8">
                    <div class="bg-white rounded-lg p-6 shadow-sm border">
                        <div class="w-12 h-12 bg-teal-100 rounded-lg flex items-center justify-center mb-4">
                            <i class="fas fa-headset text-teal-600 text-xl"></i>
                        </div>
                        <h3 class="font-semibold text-gray-900 mb-2">24/7 Support</h3>
                        <p class="text-sm text-gray-600">Our customer support team is available around the clock to help you.</p>
                    </div>

                    <div class="bg-white rounded-lg p-6 shadow-sm border">
                        <div class="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center mb-4">
                            <i class="fas fa-star-half-alt text-orange-600 text-xl"></i>
                        </div>
                        <h3 class="font-semibold text-gray-900 mb-2">Leave Reviews</h3>
                        <p class="text-sm text-gray-600">Share your experience and help other travelers make informed decisions.</p>
                    </div>

                    <div class="bg-white rounded-lg p-6 shadow-sm border">
                        <div class="w-12 h-12 bg-pink-100 rounded-lg flex items-center justify-center mb-4">
                            <i class="fas fa-users text-pink-600 text-xl"></i>
                        </div>
                        <h3 class="font-semibold text-gray-900 mb-2">Join the Community</h3>
                        <p class="text-sm text-gray-600">Connect with fellow travelers and hosts in our global community.</p>
                    </div>
                </div>

                <div class="text-center space-x-4">
                    <button onclick="prevStep()" class="bg-gray-200 text-gray-700 px-6 py-3 rounded-lg font-semibold hover:bg-gray-300 transition-all">
                        Previous
                    </button>
                    <a href="{% url 'core:home' %}" class="inline-block bg-airbnb-pink text-white px-8 py-3 rounded-lg font-semibold hover:bg-pink-600 transition-all">
                        Start Exploring
                    </a>
                </div>
            </div>
        </div>
    </div>

    <script>
        let currentStep = 1;
        const totalSteps = 3;

        function showStep(step) {
            // Hide all steps
            for (let i = 1; i <= totalSteps; i++) {
                document.getElementById(`step-${i}`).classList.add('hidden');
            }
            
            // Show current step
            document.getElementById(`step-${step}`).classList.remove('hidden');
            
            // Update progress indicator
            updateProgressIndicator(step);
        }

        function updateProgressIndicator(step) {
            for (let i = 1; i <= totalSteps; i++) {
                const circle = document.querySelector(`.flex:nth-child(${i * 2 - 1}) .w-8`);
                const text = document.querySelector(`.flex:nth-child(${i * 2 - 1}) span`);
                
                if (i <= step) {
                    circle.classList.remove('bg-gray-300', 'text-gray-600');
                    circle.classList.add('bg-airbnb-pink', 'text-white');
                    text.classList.remove('text-gray-600');
                    text.classList.add('text-airbnb-pink');
                } else {
                    circle.classList.add('bg-gray-300', 'text-gray-600');
                    circle.classList.remove('bg-airbnb-pink', 'text-white');
                    text.classList.add('text-gray-600');
                    text.classList.remove('text-airbnb-pink');
                }
            }
        }

        function nextStep() {
            if (currentStep < totalSteps) {
                currentStep++;
                showStep(currentStep);
            }
        }

        function prevStep() {
            if (currentStep > 1) {
                currentStep--;
                showStep(currentStep);
            }
        }

        // Initialize
        showStep(currentStep);

        // Auto-advance after 10 seconds on each step (optional)
        setInterval(() => {
            if (currentStep < totalSteps) {
                nextStep();
            }
        }, 15000);
    </script>
</body>
</html>
