{% extends 'base.html' %}
{% load static %}

{% block title %}Your Trips - Nestria{% endblock %}

{% block content %}
<div class="min-h-screen bg-gray-50 dark:bg-gray-900 py-8">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-gray-900 dark:text-white mb-2">Your Trips</h1>
            <p class="text-gray-600 dark:text-gray-400">Manage your bookings and travel history</p>
        </div>

        {% if reservations %}
            <!-- Trips List -->
            <div class="space-y-6">
                {% for reservation in reservations %}
                    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm hover:shadow-md transition-shadow overflow-hidden">
                        <div class="md:flex">
                            <!-- Property Image -->
                            <div class="md:w-1/3">
                                {% if reservation.room.main_image %}
                                    <img src="{{ reservation.room.main_image.url }}" 
                                         alt="{{ reservation.room.title }}" 
                                         class="w-full h-48 md:h-full object-cover">
                                {% else %}
                                    <div class="w-full h-48 md:h-full bg-gray-200 dark:bg-gray-700 flex items-center justify-center">
                                        <i class="fas fa-home text-gray-400 text-3xl"></i>
                                    </div>
                                {% endif %}
                            </div>
                            
                            <!-- Trip Details -->
                            <div class="md:w-2/3 p-6">
                                <div class="flex justify-between items-start mb-4">
                                    <div>
                                        <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-1">
                                            {{ reservation.room.title }}
                                        </h3>
                                        <p class="text-gray-600 dark:text-gray-400">
                                            {{ reservation.room.city }}, {{ reservation.room.country }}
                                        </p>
                                    </div>
                                    <div class="text-right">
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                            {% if reservation.status == 'confirmed' %}bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200
                                            {% elif reservation.status == 'pending' %}bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200
                                            {% elif reservation.status == 'cancelled' %}bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200
                                            {% else %}bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200{% endif %}">
                                            {{ reservation.get_status_display }}
                                        </span>
                                    </div>
                                </div>
                                
                                <!-- Trip Dates -->
                                <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                                    <div>
                                        <p class="text-sm text-gray-500 dark:text-gray-400">Check-in</p>
                                        <p class="font-medium text-gray-900 dark:text-white">
                                            {{ reservation.check_in|date:"M d, Y" }}
                                        </p>
                                    </div>
                                    <div>
                                        <p class="text-sm text-gray-500 dark:text-gray-400">Check-out</p>
                                        <p class="font-medium text-gray-900 dark:text-white">
                                            {{ reservation.check_out|date:"M d, Y" }}
                                        </p>
                                    </div>
                                    <div>
                                        <p class="text-sm text-gray-500 dark:text-gray-400">Guests</p>
                                        <p class="font-medium text-gray-900 dark:text-white">
                                            {{ reservation.adults }} adult{{ reservation.adults|pluralize }}
                                            {% if reservation.children %}, {{ reservation.children }} child{{ reservation.children|pluralize }}{% endif %}
                                            {% if reservation.infants %}, {{ reservation.infants }} infant{{ reservation.infants|pluralize }}{% endif %}
                                        </p>
                                    </div>
                                </div>
                                
                                <!-- Total Price -->
                                <div class="mb-4">
                                    <p class="text-sm text-gray-500 dark:text-gray-400">Total Price</p>
                                    <p class="text-2xl font-bold text-gray-900 dark:text-white">
                                        ${{ reservation.total_price|floatformat:2 }}
                                    </p>
                                </div>
                                
                                <!-- Actions -->
                                <div class="flex flex-wrap gap-3">
                                    <a href="{{ reservation.room.get_absolute_url }}" 
                                       class="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium rounded-lg transition-colors">
                                        <i class="fas fa-eye mr-2"></i>
                                        View Property
                                    </a>
                                    
                                    {% if reservation.status == 'confirmed' %}
                                        <a href="{% url 'core:contact_host' reservation.room.id %}" class="inline-flex items-center px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white text-sm font-medium rounded-lg transition-colors">
                                            <i class="fas fa-envelope mr-2"></i>
                                            Contact Host
                                        </a>
                                    {% endif %}
                                    
                                    <div class="text-xs text-gray-500 dark:text-gray-400 self-center">
                                        Booked {{ reservation.created_at|date:"M d, Y" }}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                {% endfor %}
            </div>
        {% else %}
            <!-- Empty State -->
            <div class="text-center py-16">
                <div class="mb-6">
                    <i class="fas fa-suitcase text-gray-300 dark:text-gray-600 text-6xl"></i>
                </div>
                <h2 class="text-2xl font-semibold text-gray-900 dark:text-white mb-4">
                    No trips yet
                </h2>
                <p class="text-gray-600 dark:text-gray-400 mb-8 max-w-md mx-auto">
                    Time to dust off your bags and start planning your next adventure!
                </p>
                <a href="{% url 'core:home' %}" 
                   class="inline-flex items-center px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg transition-colors">
                    <i class="fas fa-search mr-2"></i>
                    Start Planning
                </a>
            </div>
        {% endif %}
    </div>
</div>
{% endblock %}
