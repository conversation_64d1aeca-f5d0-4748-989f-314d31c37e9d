#!/usr/bin/env python
"""
Script pour configurer Facebook OAuth
"""
import os
import django

# Configuration Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'airbnb_clone.settings')
django.setup()

from allauth.socialaccount.models import SocialApp
from django.contrib.sites.models import Site

def configure_facebook_oauth():
    """Configure Facebook OAuth"""
    
    print("🔧 Configuration Facebook OAuth...")
    
    # Demander les clés Facebook
    print("\n📋 Veuillez entrer vos clés Facebook :")
    print("(Vous pouvez les obtenir sur https://developers.facebook.com)")
    
    app_id = input("\n📱 App ID Facebook : ").strip()
    if not app_id:
        app_id = "****************"
        print(f"⚠️  Utilisation de l'ID de démonstration : {app_id}")
    
    app_secret = input("🔐 App Secret Facebook : ").strip()
    if not app_secret:
        app_secret = "abcdef1234567890abcdef1234567890"
        print(f"⚠️  Utilisation du secret de démonstration : {app_secret}")
    
    # Récupérer le site
    site = Site.objects.get(pk=1)
    
    # Configuration Facebook OAuth
    facebook_app, created = SocialApp.objects.get_or_create(
        provider='facebook',
        defaults={
            'name': 'Nestria Facebook OAuth',
            'client_id': app_id,
            'secret': app_secret,
        }
    )
    
    # Mettre à jour si l'app existe déjà
    if not created:
        facebook_app.client_id = app_id
        facebook_app.secret = app_secret
        facebook_app.save()
        print("🔄 Application Facebook OAuth mise à jour")
    else:
        print("✅ Application Facebook OAuth créée")
    
    facebook_app.sites.add(site)
    
    print(f"\n✅ Configuration terminée !")
    print(f"📱 App ID : {facebook_app.client_id}")
    print(f"🔐 Secret : {facebook_app.secret[:10]}...")
    
    print("\n🎯 URLs à configurer dans Facebook Developers :")
    print("📍 URIs de redirection OAuth valides :")
    print("   - http://localhost:8000/accounts/facebook/login/callback/")
    print("   - http://127.0.0.1:8000/accounts/facebook/login/callback/")
    print("\n📍 Domaines d'app autorisés :")
    print("   - localhost")
    print("   - 127.0.0.1")
    
    print("\n⚠️  Important :")
    print("- L'app Facebook doit être en mode 'Live' pour tous les utilisateurs")
    print("- En mode 'Développement', seuls les testeurs peuvent se connecter")
    print("- Autorisations requises : email, public_profile")
    
    print("\n🧪 Test :")
    print("1. http://127.0.0.1:8000/signup/")
    print("2. 'Continue with Facebook'")
    print("3. Autoriser l'application")

if __name__ == '__main__':
    configure_facebook_oauth()
