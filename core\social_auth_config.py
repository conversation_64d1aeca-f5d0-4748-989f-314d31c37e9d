"""
Configuration pour l'authentification sociale
"""

from django.conf import settings

# Configuration Google OAuth
GOOGLE_OAUTH_CONFIG = {
    'client_id': getattr(settings, 'GOOGLE_OAUTH_CLIENT_ID', ''),
    'client_secret': getattr(settings, 'GOOGLE_OAUTH_CLIENT_SECRET', ''),
    'scope': 'openid email profile',
    'redirect_uri': '/auth/google/callback/',
}

# Configuration Facebook Login
FACEBOOK_CONFIG = {
    'app_id': getattr(settings, 'FACEBOOK_APP_ID', ''),
    'app_secret': getattr(settings, 'FACEBOOK_APP_SECRET', ''),
    'scope': 'email,public_profile',
    'redirect_uri': '/auth/facebook/callback/',
}

# Configuration Apple Sign In
APPLE_CONFIG = {
    'client_id': getattr(settings, 'APPLE_CLIENT_ID', ''),
    'team_id': getattr(settings, 'APPLE_TEAM_ID', ''),
    'key_id': getattr(settings, 'APPLE_KEY_ID', ''),
    'private_key': getattr(settings, 'APPLE_PRIVATE_KEY', ''),
    'scope': 'name email',
    'redirect_uri': '/auth/apple/callback/',
}

# URLs des APIs
SOCIAL_AUTH_URLS = {
    'google': {
        'token_url': 'https://oauth2.googleapis.com/token',
        'user_info_url': 'https://www.googleapis.com/oauth2/v2/userinfo',
    },
    'facebook': {
        'token_url': 'https://graph.facebook.com/v18.0/oauth/access_token',
        'user_info_url': 'https://graph.facebook.com/me',
    },
    'apple': {
        'token_url': 'https://appleid.apple.com/auth/token',
        'user_info_url': None,  # Apple ne fournit pas d'endpoint user info
    }
}

# Messages d'erreur
ERROR_MESSAGES = {
    'invalid_token': 'Token d\'authentification invalide',
    'user_creation_failed': 'Impossible de créer le compte utilisateur',
    'email_already_exists': 'Un compte avec cette adresse email existe déjà',
    'provider_error': 'Erreur du fournisseur d\'authentification',
    'network_error': 'Erreur de connexion réseau',
}

# Configuration des champs utilisateur par provider
USER_FIELD_MAPPING = {
    'google': {
        'email': 'email',
        'first_name': 'given_name',
        'last_name': 'family_name',
        'picture': 'picture',
    },
    'facebook': {
        'email': 'email',
        'first_name': 'first_name',
        'last_name': 'last_name',
        'picture': 'picture.data.url',
    },
    'apple': {
        'email': 'email',
        'first_name': 'user.name.firstName',
        'last_name': 'user.name.lastName',
        'picture': None,
    }
}
