"""
Service de vérification par email comme alternative à WhatsApp
Complètement gratuit avec Gmail
"""

import logging
from django.core.mail import send_mail
from django.conf import settings
from django.template.loader import render_to_string

logger = logging.getLogger(__name__)


class EmailVerificationService:
    """Service de vérification par email"""
    
    def send_verification_code(self, email, verification_code, user_name=""):
        """
        Envoie un code de vérification par email
        
        Args:
            email (str): Adresse email
            verification_code (str): Code à 6 chiffres
            user_name (str): Nom de l'utilisateur
            
        Returns:
            dict: Résultat de l'envoi
        """
        
        try:
            # Sujet de l'email
            subject = f"Your Nestria verification code: {verification_code}"
            
            # Message texte simple
            message = f"""
Hello {user_name},

Your Nestria verification code is: {verification_code}

This code will expire in 5 minutes for security reasons.
Do not share this code with anyone.

If you didn't request this code, please ignore this email.

Welcome to Nestria!

Best regards,
The Nestria Team
            """
            
            # Message HTML (optionnel)
            html_message = f"""
            <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
                <div style="background: linear-gradient(135deg, #FF5A5F, #FF385C); padding: 20px; text-align: center;">
                    <h1 style="color: white; margin: 0;">🏠 Nestria</h1>
                </div>
                
                <div style="padding: 30px; background: #f9f9f9;">
                    <h2 style="color: #333;">Hello {user_name},</h2>
                    
                    <p style="font-size: 16px; color: #666;">
                        Your Nestria verification code is:
                    </p>
                    
                    <div style="background: white; padding: 20px; text-align: center; border-radius: 8px; margin: 20px 0;">
                        <span style="font-size: 32px; font-weight: bold; color: #FF5A5F; letter-spacing: 5px;">
                            {verification_code}
                        </span>
                    </div>
                    
                    <p style="font-size: 14px; color: #999;">
                        ⏰ This code will expire in 5 minutes for security reasons.<br>
                        🔒 Do not share this code with anyone.
                    </p>
                    
                    <p style="font-size: 14px; color: #999;">
                        If you didn't request this code, please ignore this email.
                    </p>
                </div>
                
                <div style="padding: 20px; text-align: center; background: #333; color: white;">
                    <p style="margin: 0;">Welcome to Nestria! 🌟</p>
                </div>
            </div>
            """
            
            # Envoyer l'email
            result = send_mail(
                subject=subject,
                message=message,
                from_email=settings.DEFAULT_FROM_EMAIL,
                recipient_list=[email],
                html_message=html_message,
                fail_silently=False,
            )
            
            if result:
                logger.info(f"✅ Verification email sent successfully to {email}")
                return {
                    'success': True,
                    'message_id': f'email_{verification_code}',
                    'status': 'sent'
                }
            else:
                logger.error(f"❌ Failed to send verification email to {email}")
                return {
                    'success': False,
                    'error': 'Failed to send email'
                }
                
        except Exception as e:
            logger.error(f"❌ Unexpected error sending verification email to {email}: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }


# Instance globale
email_verification_service = EmailVerificationService()


def send_email_verification(email, verification_code, user_name=""):
    """
    Fonction helper pour envoyer par email
    """
    result = email_verification_service.send_verification_code(email, verification_code, user_name)
    return result.get('success', False)
