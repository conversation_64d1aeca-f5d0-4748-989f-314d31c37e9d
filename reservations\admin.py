from django.contrib import admin
from .models import Reservation


@admin.register(Reservation)
class ReservationAdmin(admin.ModelAdmin):
    list_display = [
        'guest', 'room', 'check_in', 'check_out', 'nights',
        'total_guests', 'total_price', 'status', 'created_at'
    ]
    list_filter = ['status', 'check_in', 'check_out', 'created_at', 'room__city']
    search_fields = ['guest__username', 'guest__email', 'room__title']
    readonly_fields = ['nights', 'total_price', 'created_at', 'updated_at']
    date_hierarchy = 'check_in'

    fieldsets = (
        ('Booking Information', {
            'fields': ('room', 'guest', 'status')
        }),
        ('Dates', {
            'fields': ('check_in', 'check_out', 'nights')
        }),
        ('Guests', {
            'fields': ('adults', 'children', 'infants')
        }),
        ('Pricing', {
            'fields': ('price_per_night', 'cleaning_fee', 'service_fee', 'total_price')
        }),
        ('Additional Information', {
            'fields': ('special_requests',)
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('guest', 'room')

    def total_guests(self, obj):
        return obj.total_guests
    total_guests.short_description = 'Total Guests'
