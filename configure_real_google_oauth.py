#!/usr/bin/env python
"""
Script pour configurer Google OAuth avec de vraies clés
Remplacez les clés par vos vraies clés Google Cloud Console
"""
import os
import django

# Configuration Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'airbnb_clone.settings')
django.setup()

from allauth.socialaccount.models import SocialApp
from django.contrib.sites.models import Site

def configure_real_google_oauth():
    """Configure Google OAuth avec de vraies clés"""
    
    print("🔧 Configuration Google OAuth avec de vraies clés...")
    
    # Demander les vraies clés à l'utilisateur
    print("\n📋 Veuillez entrer vos clés Google OAuth :")
    print("(Vous pouvez les obtenir sur https://console.cloud.google.com)")
    
    client_id = input("\n📱 Client ID Google : ").strip()
    if not client_id:
        client_id = "************-abcdefghijklmnopqrstuvwxyz.apps.googleusercontent.com"
        print(f"⚠️  Utilisation de la clé de démonstration : {client_id}")
    
    client_secret = input("🔐 Client Secret Google : ").strip()
    if not client_secret:
        client_secret = "GOCSPX-demo_secret_key_for_testing_only"
        print(f"⚠️  Utilisation du secret de démonstration : {client_secret}")
    
    # Récupérer le site
    site = Site.objects.get(pk=1)
    
    # Configuration Google OAuth
    google_app, created = SocialApp.objects.get_or_create(
        provider='google',
        defaults={
            'name': 'Nestria Google OAuth',
            'client_id': client_id,
            'secret': client_secret,
        }
    )
    
    # Mettre à jour si l'app existe déjà
    if not created:
        google_app.client_id = client_id
        google_app.secret = client_secret
        google_app.save()
        print("🔄 Application Google OAuth mise à jour")
    else:
        print("✅ Application Google OAuth créée")
    
    google_app.sites.add(site)
    
    print(f"\n✅ Configuration terminée !")
    print(f"📱 Client ID : {google_app.client_id}")
    print(f"🔐 Secret : {google_app.secret[:15]}...")
    
    print("\n🎯 URLs à configurer dans Google Cloud Console :")
    print("📍 Origines JavaScript autorisées :")
    print("   - http://localhost:8000")
    print("   - http://127.0.0.1:8000")
    print("\n📍 URIs de redirection autorisés :")
    print("   - http://localhost:8000/accounts/google/login/callback/")
    print("   - http://127.0.0.1:8000/accounts/google/login/callback/")
    
    print("\n🧪 Test de l'authentification :")
    print("1. Aller sur : http://127.0.0.1:8000/signup/")
    print("2. Cliquer sur 'Continue with Google'")
    print("3. Autoriser l'application")
    print("4. Vérifier la redirection vers finish-signup avec données pré-remplies")

if __name__ == '__main__':
    configure_real_google_oauth()
