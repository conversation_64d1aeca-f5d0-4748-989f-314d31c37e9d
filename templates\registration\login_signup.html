<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Welcome to Nestria</title>
    {% load static %}
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'nestria-red': '#FF5A5F',
                        'nestria-pink': '#FF385C',
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gray-50 min-h-screen flex items-center justify-center">
    
    <!-- Container principal -->
    <div class="w-full max-w-md">
        
        <!-- Logo et titre -->
        <div class="text-center mb-8">
            <div class="inline-flex items-center justify-center w-16 h-16 bg-nestria-red rounded-2xl mb-4">
                <i class="fas fa-home text-white text-2xl"></i>
            </div>
            <h1 class="text-3xl font-bold text-gray-900 mb-2">Welcome to Nestria</h1>
            <p class="text-gray-600">Your home away from home</p>
        </div>

        <!-- Carte principale -->
        <div class="bg-white rounded-2xl shadow-xl p-8">
            
            <!-- Onglets Login/Signup -->
            <div class="flex mb-8 bg-gray-100 rounded-xl p-1">
                <button id="loginTab" onclick="switchTab('login')" 
                        class="flex-1 py-3 px-4 rounded-lg font-medium transition-all duration-200 bg-white text-nestria-red shadow-sm">
                    Log in
                </button>
                <button id="signupTab" onclick="switchTab('signup')" 
                        class="flex-1 py-3 px-4 rounded-lg font-medium transition-all duration-200 text-gray-600 hover:text-gray-900">
                    Sign up
                </button>
            </div>

            <!-- Formulaire Login -->
            <form id="loginForm" method="post" action="{% url 'core:login_user' %}" class="space-y-6">
                {% csrf_token %}
                <input type="hidden" name="form_type" value="login">
                
                <div>
                    <label for="login_email" class="block text-sm font-medium text-gray-700 mb-2">Email</label>
                    <div class="relative">
                        <input type="email" 
                               id="login_email" 
                               name="email" 
                               placeholder="Enter your email"
                               class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-nestria-red focus:border-transparent transition-all duration-200"
                               required>
                        <i class="fas fa-envelope absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                    </div>
                </div>

                <div>
                    <label for="login_password" class="block text-sm font-medium text-gray-700 mb-2">Password</label>
                    <div class="relative">
                        <input type="password" 
                               id="login_password" 
                               name="password" 
                               placeholder="Enter your password"
                               class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-nestria-red focus:border-transparent transition-all duration-200"
                               required>
                        <i class="fas fa-lock absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                    </div>
                </div>

                <button type="submit" 
                        class="w-full bg-nestria-red text-white py-3 px-4 rounded-xl font-medium hover:bg-red-600 transition-all duration-200 transform hover:scale-[1.02]">
                    Log in
                </button>
            </form>

            <!-- Formulaire Signup -->
            <form id="signupForm" method="post" action="{% url 'core:signup_user' %}" class="space-y-6 hidden">
                {% csrf_token %}
                <input type="hidden" name="form_type" value="signup">
                
                <div>
                    <label for="signup_email" class="block text-sm font-medium text-gray-700 mb-2">Email</label>
                    <div class="relative">
                        <input type="email" 
                               id="signup_email" 
                               name="email" 
                               placeholder="Enter your email"
                               class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-nestria-red focus:border-transparent transition-all duration-200"
                               required>
                        <i class="fas fa-envelope absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                    </div>
                </div>

                <div>
                    <label for="signup_password" class="block text-sm font-medium text-gray-700 mb-2">Password</label>
                    <div class="relative">
                        <input type="password" 
                               id="signup_password" 
                               name="password" 
                               placeholder="Create a password"
                               class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-nestria-red focus:border-transparent transition-all duration-200"
                               required>
                        <i class="fas fa-lock absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                    </div>
                </div>

                <button type="submit" 
                        class="w-full bg-nestria-red text-white py-3 px-4 rounded-xl font-medium hover:bg-red-600 transition-all duration-200 transform hover:scale-[1.02]">
                    Sign up
                </button>
            </form>

            <!-- Divider -->
            <div class="flex items-center my-8">
                <div class="flex-1 border-t border-gray-300"></div>
                <span class="px-4 text-gray-500 text-sm">or</span>
                <div class="flex-1 border-t border-gray-300"></div>
            </div>

            <!-- Boutons OAuth -->
            <div class="space-y-3">
                <!-- Google -->
                <a href="{% url 'socialaccount_login' provider='google' %}"
                   class="w-full flex items-center justify-center px-4 py-3 border border-gray-300 rounded-xl hover:bg-gray-50 transition-all duration-200 transform hover:scale-[1.02]">
                    <img src="https://developers.google.com/identity/images/g-logo.png" alt="Google" class="w-5 h-5 mr-3">
                    <span class="font-medium text-gray-700">Continue with Google</span>
                </a>

                <!-- Apple -->
                <a href="{% url 'socialaccount_login' provider='apple' %}"
                   class="w-full flex items-center justify-center px-4 py-3 border border-gray-300 rounded-xl hover:bg-gray-50 transition-all duration-200 transform hover:scale-[1.02]">
                    <i class="fab fa-apple text-xl mr-3"></i>
                    <span class="font-medium text-gray-700">Continue with Apple</span>
                </a>

                <!-- Facebook -->
                <a href="{% url 'socialaccount_login' provider='facebook' %}"
                   class="w-full flex items-center justify-center px-4 py-3 border border-gray-300 rounded-xl hover:bg-gray-50 transition-all duration-200 transform hover:scale-[1.02]">
                    <i class="fab fa-facebook-f text-blue-600 mr-3"></i>
                    <span class="font-medium text-gray-700">Continue with Facebook</span>
                </a>
            </div>

            <!-- Messages d'erreur/succès -->
            {% if messages %}
                <div class="mt-6">
                    {% for message in messages %}
                        <div class="p-4 rounded-xl {% if message.tags == 'error' %}bg-red-50 text-red-700 border border-red-200{% else %}bg-green-50 text-green-700 border border-green-200{% endif %}">
                            <div class="flex items-center">
                                <i class="fas {% if message.tags == 'error' %}fa-exclamation-circle{% else %}fa-check-circle{% endif %} mr-2"></i>
                                {{ message }}
                            </div>
                        </div>
                    {% endfor %}
                </div>
            {% endif %}

        </div>



    </div>

    <script>
        function switchTab(tab) {
            const loginTab = document.getElementById('loginTab');
            const signupTab = document.getElementById('signupTab');
            const loginForm = document.getElementById('loginForm');
            const signupForm = document.getElementById('signupForm');

            if (tab === 'login') {
                // Activer login
                loginTab.classList.add('bg-white', 'text-nestria-red', 'shadow-sm');
                loginTab.classList.remove('text-gray-600');
                signupTab.classList.remove('bg-white', 'text-nestria-red', 'shadow-sm');
                signupTab.classList.add('text-gray-600');
                
                loginForm.classList.remove('hidden');
                signupForm.classList.add('hidden');
            } else {
                // Activer signup
                signupTab.classList.add('bg-white', 'text-nestria-red', 'shadow-sm');
                signupTab.classList.remove('text-gray-600');
                loginTab.classList.remove('bg-white', 'text-nestria-red', 'shadow-sm');
                loginTab.classList.add('text-gray-600');
                
                signupForm.classList.remove('hidden');
                loginForm.classList.add('hidden');
            }
        }

        // Animation au focus des inputs
        document.querySelectorAll('input').forEach(input => {
            input.addEventListener('focus', function() {
                this.parentElement.classList.add('scale-[1.02]');
            });
            
            input.addEventListener('blur', function() {
                this.parentElement.classList.remove('scale-[1.02]');
            });
        });
    </script>

</body>
</html>
