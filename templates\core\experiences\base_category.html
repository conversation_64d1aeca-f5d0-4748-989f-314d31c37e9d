{% extends 'base.html' %}
{% load static %}

{% block title %}{{ category_title }} - Nestria{% endblock %}

{% block content %}
<div class="min-h-screen bg-gray-50 dark:bg-gray-900">
    <!-- Hero Section -->
    <div class="relative bg-gradient-to-r from-blue-600 to-purple-600 text-white">
        <div class="absolute inset-0 bg-black opacity-20"></div>
        <div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24">
            <div class="text-center">
                <div class="mb-6">
                    {% block category_icon %}
                    <div class="w-20 h-20 bg-white bg-opacity-20 rounded-full flex items-center justify-center mx-auto mb-6">
                        <i class="fas fa-star text-4xl"></i>
                    </div>
                    {% endblock %}
                </div>
                <h1 class="text-4xl md:text-6xl font-bold mb-6">{{ category_title }}</h1>
                <p class="text-xl md:text-2xl mb-8 max-w-3xl mx-auto opacity-90">{{ category_description }}</p>
                <div class="flex flex-col sm:flex-row gap-4 justify-center">
                    <button onclick="scrollToExperiences()" class="bg-white text-blue-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors">
                        Explore Experiences
                    </button>
                    <a href="{% url 'core:experiences' %}" class="border-2 border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white hover:text-blue-600 transition-colors">
                        All Categories
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters Section -->
    <div class="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
            <div class="flex flex-col md:flex-row gap-4 items-center justify-between">
                <div class="flex flex-wrap gap-2">
                    <button onclick="filterByPrice('all')" class="filter-btn active px-4 py-2 rounded-lg text-sm font-medium transition-colors bg-blue-600 text-white">
                        All Prices
                    </button>
                    <button onclick="filterByPrice('budget')" class="filter-btn px-4 py-2 rounded-lg text-sm font-medium transition-colors bg-gray-200 text-gray-700 hover:bg-gray-300">
                        Under $50
                    </button>
                    <button onclick="filterByPrice('mid')" class="filter-btn px-4 py-2 rounded-lg text-sm font-medium transition-colors bg-gray-200 text-gray-700 hover:bg-gray-300">
                        $50 - $100
                    </button>
                    <button onclick="filterByPrice('premium')" class="filter-btn px-4 py-2 rounded-lg text-sm font-medium transition-colors bg-gray-200 text-gray-700 hover:bg-gray-300">
                        $100+
                    </button>
                </div>
                <div class="flex items-center gap-4">
                    <select id="sortSelect" onchange="sortExperiences()" class="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg text-sm dark:bg-gray-700 dark:text-white">
                        <option value="popular">Most Popular</option>
                        <option value="price-low">Price: Low to High</option>
                        <option value="price-high">Price: High to Low</option>
                        <option value="rating">Highest Rated</option>
                    </select>
                    <div class="flex items-center gap-2">
                        <button onclick="toggleView('grid')" class="view-btn p-2 rounded-lg bg-blue-600 text-white">
                            <i class="fas fa-th-large"></i>
                        </button>
                        <button onclick="toggleView('list')" class="view-btn p-2 rounded-lg bg-gray-200 text-gray-700 hover:bg-gray-300">
                            <i class="fas fa-list"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Experiences Grid -->
    <div id="experiencesSection" class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div class="mb-8">
            <h2 class="text-3xl font-bold text-gray-900 dark:text-white mb-4">Available Experiences</h2>
            <p class="text-gray-600 dark:text-gray-400">Discover amazing {{ category_title|lower }} in destinations worldwide</p>
        </div>

        <div id="experiencesGrid" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {% block experiences_content %}
            <!-- Experiences will be loaded here -->
            {% endblock %}
        </div>

        <!-- Load More Button -->
        <div class="text-center mt-12">
            <button onclick="loadMoreExperiences()" class="bg-blue-600 hover:bg-blue-700 text-white px-8 py-3 rounded-lg font-semibold transition-colors">
                Load More Experiences
            </button>
        </div>
    </div>

    <!-- CTA Section -->
    <div class="bg-gradient-to-r from-purple-600 to-blue-600 text-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
            <div class="text-center">
                <h2 class="text-3xl md:text-4xl font-bold mb-6">Ready for Your Next Adventure?</h2>
                <p class="text-xl mb-8 max-w-2xl mx-auto opacity-90">
                    Join thousands of travelers who have discovered amazing {{ category_title|lower }} experiences with Nestria.
                </p>
                <div class="flex flex-col sm:flex-row gap-4 justify-center">
                    <button onclick="scrollToExperiences()" class="bg-white text-purple-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors">
                        Book Now
                    </button>
                    <a href="{% url 'core:experiences' %}" class="border-2 border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white hover:text-purple-600 transition-colors">
                        Explore All Categories
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Scroll to experiences section
function scrollToExperiences() {
    document.getElementById('experiencesSection').scrollIntoView({ 
        behavior: 'smooth' 
    });
}

// Filter by price
function filterByPrice(priceRange) {
    const buttons = document.querySelectorAll('.filter-btn');
    buttons.forEach(btn => {
        btn.classList.remove('bg-blue-600', 'text-white');
        btn.classList.add('bg-gray-200', 'text-gray-700');
    });
    
    event.target.classList.remove('bg-gray-200', 'text-gray-700');
    event.target.classList.add('bg-blue-600', 'text-white');
    
    // Filter logic here
    showNotification(`Filtering by ${priceRange} price range`, 'info');
}

// Sort experiences
function sortExperiences() {
    const sortValue = document.getElementById('sortSelect').value;
    showNotification(`Sorting by ${sortValue}`, 'info');
}

// Toggle view
function toggleView(viewType) {
    const buttons = document.querySelectorAll('.view-btn');
    buttons.forEach(btn => {
        btn.classList.remove('bg-blue-600', 'text-white');
        btn.classList.add('bg-gray-200', 'text-gray-700');
    });
    
    event.target.classList.remove('bg-gray-200', 'text-gray-700');
    event.target.classList.add('bg-blue-600', 'text-white');
    
    const grid = document.getElementById('experiencesGrid');
    if (viewType === 'list') {
        grid.classList.remove('grid-cols-1', 'md:grid-cols-2', 'lg:grid-cols-3');
        grid.classList.add('grid-cols-1');
    } else {
        grid.classList.remove('grid-cols-1');
        grid.classList.add('grid-cols-1', 'md:grid-cols-2', 'lg:grid-cols-3');
    }
}

// Load more experiences
function loadMoreExperiences() {
    showNotification('Loading more experiences...', 'info');
    // Simulate loading
    setTimeout(() => {
        showNotification('More experiences loaded!', 'success');
    }, 1000);
}

// Book experience function
function bookExperience(name, price) {
    if (confirm(`Book "${name}" for ${price}?\n\nThis will redirect you to the booking page.`)) {
        showNotification(`Booking "${name}"...`, 'info');
        setTimeout(() => {
            showNotification('Booking confirmed! Check your email for details.', 'success');
        }, 2000);
    }
}

// Toggle favorite function
function toggleFavorite(button) {
    const icon = button.querySelector('i');
    const propertyId = button.dataset.propertyId;

    // Toggle heart icon
    if (icon.classList.contains('far')) {
        icon.classList.remove('far');
        icon.classList.add('fas');
        icon.classList.add('text-red-500');
        showNotification('Added to favorites!', 'success');
    } else {
        icon.classList.remove('fas', 'text-red-500');
        icon.classList.add('far');
        showNotification('Removed from favorites', 'info');
    }

    // Here you would normally send to backend
    console.log('Toggling favorite for:', propertyId);
}
</script>
{% endblock %}
