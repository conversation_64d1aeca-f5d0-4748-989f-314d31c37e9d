{% extends 'base.html' %}
{% load static %}

{% block title %}Change Password - Nestria{% endblock %}

{% block content %}
<div class="min-h-screen bg-gray-50 dark:bg-gray-900 py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-md mx-auto">
        <div class="bg-white dark:bg-gray-800 shadow-lg rounded-lg p-8">
            <div class="text-center mb-8">
                <div class="mx-auto mb-4">
                    <img src="{% static 'images/nestria-logo.png' %}"
                         alt="Nestria Logo"
                         class="w-16 h-16 object-contain mx-auto">
                </div>
                <h2 class="text-2xl font-bold text-gray-900 dark:text-white">
                    Change Password
                </h2>
                <p class="text-gray-600 dark:text-gray-400 mt-2">
                    Update your account password
                </p>
            </div>

            <form method="post" class="space-y-6">
                {% csrf_token %}
                
                {% if form.errors %}
                    <div class="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
                        <div class="flex">
                            <div class="flex-shrink-0">
                                <i class="fas fa-exclamation-circle text-red-400"></i>
                            </div>
                            <div class="ml-3">
                                <h3 class="text-sm font-medium text-red-800 dark:text-red-200">
                                    Please correct the following errors:
                                </h3>
                                <div class="mt-2 text-sm text-red-700 dark:text-red-300">
                                    <ul class="list-disc list-inside space-y-1">
                                        {% for field, errors in form.errors.items %}
                                            {% for error in errors %}
                                                <li>{{ error }}</li>
                                            {% endfor %}
                                        {% endfor %}
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                {% endif %}

                <div>
                    <label for="{{ form.old_password.id_for_label }}" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Current Password
                    </label>
                    <input type="password" 
                           name="old_password" 
                           id="{{ form.old_password.id_for_label }}"
                           required
                           class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                           placeholder="Enter your current password">
                </div>

                <div>
                    <label for="{{ form.new_password1.id_for_label }}" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        New Password
                    </label>
                    <input type="password" 
                           name="new_password1" 
                           id="{{ form.new_password1.id_for_label }}"
                           required
                           class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                           placeholder="Enter your new password">
                </div>

                <div>
                    <label for="{{ form.new_password2.id_for_label }}" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Confirm New Password
                    </label>
                    <input type="password" 
                           name="new_password2" 
                           id="{{ form.new_password2.id_for_label }}"
                           required
                           class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                           placeholder="Confirm your new password">
                </div>

                <div class="flex space-x-4">
                    <button type="submit" class="flex-1 bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200">
                        <i class="fas fa-key mr-2"></i>
                        Update Password
                    </button>
                    <a href="{% url 'core:settings' %}" class="flex-1 bg-gray-300 hover:bg-gray-400 text-gray-700 font-medium py-2 px-4 rounded-lg transition-colors duration-200 text-center">
                        <i class="fas fa-times mr-2"></i>
                        Cancel
                    </a>
                </div>
            </form>

            <div class="mt-6 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                <h3 class="text-sm font-medium text-blue-800 dark:text-blue-200 mb-2">
                    <i class="fas fa-info-circle mr-2"></i>
                    Password Requirements
                </h3>
                <ul class="text-sm text-blue-700 dark:text-blue-300 space-y-1">
                    <li>• At least 8 characters long</li>
                    <li>• Cannot be too similar to your other personal information</li>
                    <li>• Cannot be a commonly used password</li>
                    <li>• Cannot be entirely numeric</li>
                </ul>
            </div>
        </div>
    </div>
</div>
{% endblock %}
