from .translations import get_all_translations

def translations(request):
    """Context processor to add translations to all templates"""
    # Get user's preferred language
    language = 'en'  # default
    
    if request.user.is_authenticated and hasattr(request.user, 'profile'):
        language = request.user.profile.language or 'en'
    elif 'django_language' in request.session:
        language = request.session['django_language']
    
    return {
        'translations': get_all_translations(language),
        'current_language': language
    }
