from django.shortcuts import render, redirect, get_object_or_404
from django.views.generic import TemplateView, CreateView, UpdateView
from django.contrib.auth.forms import UserCreationForm, PasswordChangeForm, AuthenticationForm
from django.contrib.auth.mixins import LoginRequiredMixin
from django.contrib.auth import login, logout, update_session_auth_hash, authenticate
from django.contrib.auth.decorators import login_required
from django.contrib.auth.models import User
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import JsonResponse
from django.views.decorators.http import require_POST, require_http_methods
from django.views.decorators.csrf import csrf_exempt
from django.utils import translation
from django.conf import settings
from django.utils import timezone
from datetime import datetime, timedelta
import json
import random
import string
import logging

# Logger
logger = logging.getLogger(__name__)
from rooms.models import Room, RoomType, Destination, Favorite
from .models import UserProfile
from .forms import ExtendedUserCreationForm, UserProfileForm
from core.email_service import EmailService
from core.services.callmebot_service import send_whatsapp_verification_callmebot
from core.services.multi_whatsapp_service import send_whatsapp_verification_multi
from core.services.email_verification_service import send_email_verification as send_email_code


class HomeView(TemplateView):
    """Home page view with featured properties"""
    template_name = 'core/home.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Get featured properties (latest 8 active rooms)
        context['featured_rooms'] = Room.objects.filter(is_active=True).select_related('host').prefetch_related('images')[:8]

        # Get featured destinations (limit to 3 for the homepage)
        featured_destinations = Destination.objects.filter(is_featured=True).prefetch_related('images')[:3]

        # If no featured destinations, get the first 3
        if not featured_destinations:
            featured_destinations = Destination.objects.all().prefetch_related('images')[:3]

        context['featured_destinations'] = featured_destinations

        # Get room types for the categories section
        context['room_types'] = RoomType.objects.all()[:6]

        return context


class SignUpView(CreateView):
    """User registration view"""
    form_class = ExtendedUserCreationForm
    template_name = 'core/signup.html'
    success_url = reverse_lazy('core:home')

    def form_valid(self, form):
        response = super().form_valid(form)
        # Log the user in after successful registration
        login(self.request, self.object)

        # Send welcome email
        try:
            EmailService.send_welcome_email(
                user_email=self.object.email,
                user_name=self.object.get_full_name() or self.object.username
            )
        except Exception as e:
            # Don't fail registration if email fails
            messages.warning(self.request, 'Account created successfully, but welcome email could not be sent.')

        messages.success(self.request, f'Welcome to Nestria, {self.object.first_name}!')
        return response


class CustomLoginView(TemplateView):
    """Custom login view with social auth options"""
    template_name = 'registration/login.html'

    def post(self, request, *args, **kwargs):
        phone = request.POST.get('phone')
        if phone:
            # Handle phone login logic here
            messages.info(request, 'Phone verification will be implemented soon.')
            return redirect('core:home')
        return self.get(request, *args, **kwargs)


class CustomSignUpView(TemplateView):
    """Custom signup view with social auth options"""
    template_name = 'registration/signup.html'

    def post(self, request, *args, **kwargs):
        phone = request.POST.get('phone')
        if phone:
            # Handle phone signup logic here
            messages.info(request, 'Phone verification will be implemented soon.')
            return redirect('core:home')
        return self.get(request, *args, **kwargs)


class EmailLoginView(TemplateView):
    """Email-based login view"""
    template_name = 'registration/email_login.html'

    def post(self, request, *args, **kwargs):
        email = request.POST.get('email')
        password = request.POST.get('password')

        if email and password:
            # Try to find user by email
            try:
                user = User.objects.get(email=email)
                user = authenticate(request, username=user.username, password=password)
                if user:
                    login(request, user)
                    messages.success(request, f'Welcome back, {user.first_name}!')
                    return redirect('core:home')
                else:
                    messages.error(request, 'Invalid email or password.')
            except User.DoesNotExist:
                messages.error(request, 'No account found with this email.')

        return self.get(request, *args, **kwargs)


class EmailSignUpView(TemplateView):
    """Email-based signup view"""
    template_name = 'registration/email_signup.html'

    def post(self, request, *args, **kwargs):
        first_name = request.POST.get('first_name')
        last_name = request.POST.get('last_name')
        email = request.POST.get('email')
        password = request.POST.get('password')
        confirm_password = request.POST.get('confirm_password')

        if password != confirm_password:
            messages.error(request, 'Passwords do not match.')
            return self.get(request, *args, **kwargs)

        if User.objects.filter(email=email).exists():
            messages.error(request, 'An account with this email already exists.')
            return self.get(request, *args, **kwargs)

        # Create user
        try:
            user = User.objects.create_user(
                username=email,  # Use email as username
                email=email,
                password=password,
                first_name=first_name,
                last_name=last_name
            )

            # Log the user in
            login(request, user)

            # Send welcome email
            try:
                EmailService.send_welcome_email(
                    user_email=user.email,
                    user_name=user.get_full_name()
                )
            except Exception as e:
                messages.warning(request, 'Account created successfully, but welcome email could not be sent.')

            messages.success(request, f'Welcome to Nestria, {user.first_name}!')
            return redirect('core:home')

        except Exception as e:
            logger.error(f"Error creating user account: {str(e)}")

        return self.get(request, *args, **kwargs)


class ProfileView(LoginRequiredMixin, TemplateView):
    """User profile view"""
    template_name = 'core/profile.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        user = self.request.user
        context['user_reservations'] = user.reservations.all().select_related('room')[:5]
        context['user_favorites'] = user.favorites.all().select_related('room')[:8]
        context['hosted_rooms'] = user.hosted_rooms.filter(is_active=True)[:5]
        return context


class FavoritesView(LoginRequiredMixin, TemplateView):
    """User favorites view"""
    template_name = 'core/favorites.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        user = self.request.user
        context['favorites'] = user.favorites.all().select_related('room').prefetch_related('room__images')
        return context


class YourTripsView(LoginRequiredMixin, TemplateView):
    """User trips/reservations view"""
    template_name = 'core/your_trips.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        user = self.request.user
        context['reservations'] = user.reservations.all().select_related('room').order_by('-created_at')
        return context


class SettingsView(LoginRequiredMixin, UpdateView):
    """User settings view"""
    model = UserProfile
    form_class = UserProfileForm
    template_name = 'core/settings.html'
    success_url = reverse_lazy('core:settings')

    def get_object(self):
        profile, created = UserProfile.objects.get_or_create(user=self.request.user)
        return profile

    def form_valid(self, form):
        messages.success(self.request, 'Your profile has been updated successfully!')
        return super().form_valid(form)


class ExperiencesView(TemplateView):
    """Experiences page"""
    template_name = 'core/experiences.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Get some featured experiences (using destinations for now)
        context['experiences'] = Destination.objects.filter(is_featured=True)[:6]
        return context


class OnlineView(TemplateView):
    """Online experiences page"""
    template_name = 'core/online.html'


class BrowseExperiencesView(TemplateView):
    """Browse all experiences view"""
    template_name = 'core/browse_experiences.html'


class HostExperienceView(TemplateView):
    """Host an experience view"""
    template_name = 'core/host_experience.html'


class BrowseOnlineView(TemplateView):
    """Browse online experiences view"""
    template_name = 'core/browse_online.html'


class HostOnlineView(TemplateView):
    """Host online experience view"""
    template_name = 'core/host_online.html'


@login_required
def logout_view(request):
    """Logout view"""
    logout(request)
    messages.success(request, 'You have been successfully logged out.')
    return redirect('core:home')


@login_required
def change_password_view(request):
    """Change password view"""
    if request.method == 'POST':
        form = PasswordChangeForm(request.user, request.POST)
        if form.is_valid():
            user = form.save()
            update_session_auth_hash(request, user)  # Important!
            messages.success(request, 'Your password was successfully updated!')
            return redirect('core:settings')
        else:
            messages.error(request, 'Please correct the error below.')
    else:
        form = PasswordChangeForm(request.user)

    return render(request, 'core/change_password.html', {'form': form})


@login_required
def delete_account_view(request):
    """Delete account view"""
    if request.method == 'POST':
        password = request.POST.get('password')
        if request.user.check_password(password):
            user = request.user
            logout(request)
            user.delete()
            messages.success(request, 'Your account has been successfully deleted.')
            return redirect('core:home')
        else:
            messages.error(request, 'Incorrect password. Account not deleted.')

    return render(request, 'core/delete_account.html')


@login_required
def contact_host_view(request, room_id):
    """Contact host view"""
    room = get_object_or_404(Room, id=room_id)

    if request.method == 'POST':
        subject = request.POST.get('subject', '')
        message = request.POST.get('message', '')

        if subject and message:
            # Send email to host
            try:
                success = EmailService.send_mail(
                    to=room.host.email,
                    subject=f"Message from {request.user.get_full_name()} about {room.title}",
                    template_name="contact_host",
                    context={
                        'guest_name': request.user.get_full_name() or request.user.username,
                        'guest_email': request.user.email,
                        'room': room,
                        'subject': subject,
                        'message': message,
                        'reply_to': request.user.email,
                    }
                )

                if success:
                    return JsonResponse({
                        'success': True,
                        'message': 'Your message has been sent to the host!'
                    })
                else:
                    return JsonResponse({
                        'success': False,
                        'message': 'Failed to send message. Please try again.'
                    })
            except Exception as e:
                return JsonResponse({
                    'success': False,
                    'message': f'Error sending message: {str(e)}'
                })
        else:
            return JsonResponse({
                'success': False,
                'message': 'Please provide both subject and message.'
            })

    # Handle GET request - show contact form
    return render(request, 'core/contact_host.html', {'room': room})


def email_preview_view(request):
    """Preview email template"""
    return render(request, 'emails/preview.html', {
        'site_name': 'Nestria',
        'site_url': 'http://127.0.0.1:8000',
    })


@login_required
@require_POST
def toggle_favorite_view(request, room_id):
    """Toggle favorite status for a room"""
    try:
        room = get_object_or_404(Room, id=room_id)

        # Check if favorite already exists
        favorite, created = Favorite.objects.get_or_create(
            user=request.user,
            room=room
        )

        if created:
            # Favorite was created (added)
            is_favorited = True
            message = f"Added {room.title} to your favorites!"
        else:
            # Favorite already existed, so remove it
            favorite.delete()
            is_favorited = False
            message = f"Removed {room.title} from your favorites!"

        return JsonResponse({
            'success': True,
            'is_favorited': is_favorited,
            'message': message,
            'favorites_count': Favorite.objects.filter(room=room).count()
        })

    except Room.DoesNotExist:
        return JsonResponse({
            'success': False,
            'message': 'Room not found.'
        }, status=404)
    except Exception as e:
        return JsonResponse({
            'success': False,
            'message': 'An error occurred while updating favorites.'
        }, status=500)


@require_POST
def set_language_view(request):
    """Set user language preference"""
    language = request.POST.get('language')

    if language and language in dict(settings.LANGUAGES):
        # Activate language for current session
        translation.activate(language)
        request.session['django_language'] = language

        # Update user profile if logged in
        if request.user.is_authenticated:
            profile = request.user.profile
            profile.language = language
            profile.save()

        # Get language name for response
        language_names = {
            'en': 'English',
            'fr': 'Français',
            'es': 'Español',
            'de': 'Deutsch',
            'it': 'Italiano',
            'pt': 'Português',
            'ar': 'العربية'
        }

        return JsonResponse({
            'success': True,
            'message': f'Language changed to {language_names.get(language, language)}',
            'language': language,
            'reload_required': True
        })

    return JsonResponse({
        'success': False,
        'message': 'Invalid language selection.'
    }, status=400)


@require_POST
def send_test_email_view(request):
    """Send a test email using the Nestria email system"""
    try:
        from .email_system import NestricaEmailSender, EmailConfig, create_welcome_email_html
        from django.core.mail import send_mail

        # Get recipient from request
        recipient = request.POST.get('email', request.user.email if request.user.is_authenticated else '<EMAIL>')

        # Method 1: Using Django's built-in email system (console backend)
        django_result = send_mail(
            subject='Test Email from Nestria 🚀',
            message='This is a test email from your Nestria platform.',
            from_email='<EMAIL>',
            recipient_list=[recipient],
            fail_silently=False,
        )

        # Method 2: Using our custom email system (for real SMTP)
        # Uncomment and configure for real email sending
        """
        config = EmailConfig(
            host='smtp.gmail.com',
            port=587,
            username='<EMAIL>',
            password='your-app-password',
            use_tls=True,
            from_name='Nestria',
            from_email='<EMAIL>'
        )

        sender = NestricaEmailSender(config)
        custom_result = sender.send_mail(
            to=recipient,
            subject='Welcome to Nestria 🚀',
            text='Thank you for joining our platform!',
            html=create_welcome_email_html(request.user.first_name if request.user.is_authenticated else 'Traveler')
        )
        """

        return JsonResponse({
            'success': True,
            'message': f'Test email sent successfully to {recipient} (check console for development)',
            'method': 'Django console backend'
        })

    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': f'Failed to send email: {str(e)}'
        }, status=500)


def user_favorites_view(request):
    """Get user's favorite rooms"""
    if not request.user.is_authenticated:
        return JsonResponse({'success': False, 'favorites': []})

    try:
        from rooms.models import Room
        favorite_rooms = Room.objects.filter(favorites__user=request.user)
        favorites_list = [room.id for room in favorite_rooms]

        return JsonResponse({
            'success': True,
            'favorites': favorites_list
        })
    except Exception as e:
        return JsonResponse({
            'success': False,
            'favorites': [],
            'error': str(e)
        })


class FoodDrinkExperiencesView(TemplateView):
    """Food & Drink experiences page"""
    template_name = 'core/experiences/food_drink.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['category'] = 'food_drink'
        context['category_title'] = 'Food & Drink Experiences'
        context['category_description'] = 'Discover culinary adventures and local flavors'
        return context


class AdventureExperiencesView(TemplateView):
    """Adventure experiences page"""
    template_name = 'core/experiences/adventure.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['category'] = 'adventure'
        context['category_title'] = 'Adventure Experiences'
        context['category_description'] = 'Thrilling outdoor activities and extreme sports'
        return context


class ArtsCultureExperiencesView(TemplateView):
    """Arts & Culture experiences page"""
    template_name = 'core/experiences/arts_culture.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['category'] = 'arts_culture'
        context['category_title'] = 'Arts & Culture Experiences'
        context['category_description'] = 'Immerse yourself in local art, history, and traditions'
        return context


class PhotographyExperiencesView(TemplateView):
    """Photography experiences page"""
    template_name = 'core/experiences/photography.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['category'] = 'photography'
        context['category_title'] = 'Photography Experiences'
        context['category_description'] = 'Capture stunning moments with professional guidance'
        return context


# Nouvelles vues pour le processus d'inscription avec vérification SMS/WhatsApp

def generate_verification_code():
    """Génère un code de vérification à 6 chiffres"""
    return ''.join(random.choices(string.digits, k=6))


# Fonction supprimée - maintenant dans core/services/whatsapp_service.py


class PhoneVerificationView(TemplateView):
    """Vue pour la vérification du numéro de téléphone"""
    template_name = 'registration/phone_verification.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        verification_method = self.request.session.get('verification_method', 'phone')

        if verification_method == 'email':
            context['verification_email'] = self.request.session.get('verification_email', '')
            context['phone_number'] = None
        else:
            context['phone_number'] = self.request.session.get('phone_number', '')
            context['verification_email'] = None

        context['verification_method'] = verification_method
        context['debug'] = settings.DEBUG
        return context

    def post(self, request, *args, **kwargs):
        verification_code = request.POST.get('verification_code')
        phone_number = request.POST.get('phone_number') or request.session.get('phone_number')

        # Vérifier le code
        stored_code = request.session.get('verification_code')
        code_timestamp = request.session.get('code_timestamp')

        if not stored_code or not code_timestamp:
            messages.error(request, 'Verification code has expired. Please request a new one.')
            return redirect('core:phone_signup')

        # Vérifier si le code n'a pas expiré (5 minutes)
        if timezone.now().timestamp() - code_timestamp > 300:  # 5 minutes
            messages.error(request, 'Verification code has expired. Please request a new one.')
            return redirect('core:phone_signup')

        if verification_code == stored_code:
            # Code correct, passer à l'étape suivante
            request.session['phone_verified'] = True
            return redirect('core:contact_info')
        else:
            messages.error(request, 'Invalid verification code. Please try again.')
            return self.get(request, *args, **kwargs)


class ContactInfoView(TemplateView):
    """Vue pour saisir les informations de contact"""
    template_name = 'registration/contact_info.html'

    def get(self, request, *args, **kwargs):
        # Pour les tests, permettre l'accès direct
        # En production, vérifier que le téléphone a été vérifié
        # if not request.session.get('phone_verified'):
        #     return redirect('core:phone_signup')
        return super().get(request, *args, **kwargs)

    def post(self, request, *args, **kwargs):
        # Nettoyer les anciens messages pour éviter les doublons
        storage = messages.get_messages(request)
        storage.used = True

        email = request.POST.get('email')
        no_marketing = request.POST.get('no_marketing') == 'on'

        if not email:
            return self.get(request, *args, **kwargs)

        # Permettre de continuer même si l'email existe (pour les tests)
        # En production, vous pourriez vouloir vérifier l'unicité

        # Stocker les informations en session
        request.session['signup_email'] = email
        request.session['no_marketing'] = no_marketing

        return redirect('core:finish_signup')


class FinishSignupView(TemplateView):
    """Vue pour finaliser l'inscription"""
    template_name = 'registration/finish_signup.html'

    def get(self, request, *args, **kwargs):
        # Pour les tests, permettre l'accès direct
        # En production, vérifier que les étapes précédentes ont été complétées
        # if not request.session.get('phone_verified') or not request.session.get('signup_email'):
        #     return redirect('core:phone_signup')
        return super().get(request, *args, **kwargs)

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['email'] = self.request.session.get('signup_email', '')
        return context

    def post(self, request, *args, **kwargs):
        first_name = request.POST.get('first_name')
        last_name = request.POST.get('last_name')
        birthdate = request.POST.get('birthdate')
        email = request.POST.get('email')

        if not all([first_name, last_name, birthdate, email]):
            messages.error(request, 'All fields are required.')
            return self.get(request, *args, **kwargs)

        # Vérifier l'âge (18 ans minimum)
        try:
            birth_date = datetime.strptime(birthdate, '%Y-%m-%d').date()
            today = datetime.now().date()
            age = today.year - birth_date.year - ((today.month, today.day) < (birth_date.month, birth_date.day))

            if age < 18:
                messages.error(request, 'You must be 18 or older to sign up.')
                return self.get(request, *args, **kwargs)
        except ValueError:
            messages.error(request, 'Invalid birth date format.')
            return self.get(request, *args, **kwargs)

        # Stocker les informations en session
        request.session['signup_first_name'] = first_name
        request.session['signup_last_name'] = last_name
        request.session['signup_birthdate'] = birthdate

        # Créer l'utilisateur en base de données
        try:
            from django.contrib.auth.models import User

            # Vérifier si l'utilisateur existe déjà
            if User.objects.filter(email=email).exists():
                # Mettre à jour l'utilisateur existant
                user = User.objects.get(email=email)
                user.first_name = first_name
                user.last_name = last_name
                user.save()
            else:
                # Créer un nouvel utilisateur
                user = User.objects.create_user(
                    username=email,
                    email=email,
                    first_name=first_name,
                    last_name=last_name
                )

            # Stocker l'ID utilisateur en session
            request.session['user_id'] = user.id

            messages.success(request, 'Profile completed successfully!')

        except Exception as e:
            logger.error(f"Error creating user: {str(e)}")
            messages.error(request, 'Error creating account. Please try again.')
            return self.get(request, *args, **kwargs)

        return redirect('core:community_commitment')


class CommunityCommitmentView(TemplateView):
    """Vue pour l'engagement communautaire"""
    template_name = 'registration/community_commitment.html'

    def get(self, request, *args, **kwargs):
        # Vérifier que les étapes précédentes ont été complétées
        if not all([
            request.session.get('phone_verified'),
            request.session.get('signup_email'),
            request.session.get('signup_first_name')
        ]):
            return redirect('core:phone_signup')
        return super().get(request, *args, **kwargs)

    def post(self, request, *args, **kwargs):
        action = request.POST.get('action')

        if action == 'decline':
            # Nettoyer la session et rediriger vers l'accueil
            for key in ['phone_number', 'phone_verified', 'signup_email', 'signup_first_name',
                       'signup_last_name', 'signup_birthdate', 'verification_code', 'code_timestamp', 'no_marketing', 'user_id']:
                request.session.pop(key, None)
            messages.info(request, 'Registration cancelled. You can always come back and join our community later!')
            return redirect('core:home')

        elif action == 'agree':
            # Vérifier si l'utilisateur existe déjà
            user_id = request.session.get('user_id')

            if user_id:
                try:
                    user = User.objects.get(id=user_id)
                    # Utilisateur existe, connecter et rediriger vers onboarding
                    from django.contrib.auth import login
                    login(request, user, backend='django.contrib.auth.backends.ModelBackend')
                    messages.success(request, f'Welcome to Nestria, {user.first_name}! Thank you for joining our community.')
                    return redirect('core:onboarding')
                except User.DoesNotExist:
                    # L'utilisateur n'existe plus, créer un nouveau
                    pass

            # Créer un nouvel utilisateur si nécessaire
            try:
                email = request.session.get('signup_email')
                first_name = request.session.get('signup_first_name')
                last_name = request.session.get('signup_last_name', '')
                phone_number = request.session.get('phone_number')

                if not email or not first_name:
                    messages.error(request, 'Missing registration information. Please start the registration process again.')
                    return redirect('core:phone_signup')

                # Vérifier si un utilisateur avec cet email existe déjà
                if User.objects.filter(email=email).exists():
                    existing_user = User.objects.get(email=email)
                    from django.contrib.auth import login
                    login(request, existing_user, backend='django.contrib.auth.backends.ModelBackend')
                    messages.success(request, f'Welcome back, {existing_user.first_name}! Thank you for joining our community.')
                    return redirect('core:onboarding')

                # Créer un nouveau utilisateur
                user = User.objects.create_user(
                    username=email,
                    email=email,
                    first_name=first_name,
                    last_name=last_name,
                    password=User.objects.make_random_password()  # Mot de passe temporaire
                )

                # Créer le profil utilisateur
                from core.models import UserProfile
                UserProfile.objects.create(
                    user=user,
                    phone_number=phone_number,
                    phone_verified=True
                )

                # Connecter l'utilisateur
                from django.contrib.auth import login
                login(request, user, backend='django.contrib.auth.backends.ModelBackend')

                # Nettoyer la session
                for key in ['phone_number', 'phone_verified', 'signup_email', 'signup_first_name',
                           'signup_last_name', 'signup_birthdate', 'verification_code', 'code_timestamp', 'no_marketing', 'user_id']:
                    request.session.pop(key, None)

                messages.success(request, f'🎉 Welcome to Nestria, {user.first_name}! Your account has been successfully created. Thank you for joining our community!')
                return redirect('core:onboarding')

            except Exception as e:
                logger.error(f"Error creating user in community commitment: {str(e)}")
                messages.error(request, 'There was an error creating your account. Please try again or contact support.')
                return redirect('core:phone_signup')

        return self.get(request, *args, **kwargs)


@csrf_exempt
@require_http_methods(["POST"])
def resend_verification_code(request):
    """Renvoyer le code de vérification"""
    try:
        data = json.loads(request.body)
        phone_number = data.get('phone_number')

        if not phone_number:
            return JsonResponse({'success': False, 'error': 'Phone number is required'})

        # Générer un nouveau code
        verification_code = generate_verification_code()

        # Stocker en session
        request.session['verification_code'] = verification_code
        request.session['code_timestamp'] = timezone.now().timestamp()

        # Envoyer le code (simulation)
        success = send_whatsapp_verification(phone_number, verification_code)

        if success:
            return JsonResponse({
                'success': True,
                'message': 'Verification code sent successfully'
            })
        else:
            return JsonResponse({'success': False, 'error': 'Failed to send verification code'})

    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)})


@csrf_exempt
@require_http_methods(["POST"])
def create_wishlist(request):
    """Créer une nouvelle wishlist"""
    if not request.user.is_authenticated:
        return JsonResponse({'success': False, 'error': 'Authentication required'})

    try:
        data = json.loads(request.body)
        name = data.get('name', '').strip()

        if not name:
            return JsonResponse({'success': False, 'error': 'Wishlist name is required'})

        if len(name) > 50:
            return JsonResponse({'success': False, 'error': 'Wishlist name must be 50 characters or less'})

        # Pour l'instant, nous simulons la création d'une wishlist
        # Dans un vrai projet, vous auriez un modèle Wishlist

        return JsonResponse({
            'success': True,
            'message': 'Wishlist created successfully',
            'wishlist': {
                'id': random.randint(1, 1000),
                'name': name,
                'created_at': timezone.now().isoformat()
            }
        })

    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)})


# Mise à jour de la vue CustomSignUpView pour gérer la vérification par téléphone
class PhoneSignUpView(TemplateView):
    """Vue d'inscription par téléphone avec vérification WhatsApp"""
    template_name = 'registration/signup.html'

    def post(self, request, *args, **kwargs):
        phone = request.POST.get('phone')

        if not phone:
            return self.get(request, *args, **kwargs)

        # Nettoyer et valider le numéro de téléphone
        phone = phone.strip().replace(' ', '').replace('-', '')

        if not phone.startswith('+'):
            phone = '+' + phone

        # Vérifier si le numéro existe déjà
        if UserProfile.objects.filter(phone_number=phone).exists():
            return self.get(request, *args, **kwargs)

        # Nettoyer la session des anciennes données de vérification
        for key in ['verification_method', 'verification_email', 'verification_code', 'code_timestamp']:
            request.session.pop(key, None)

        # Générer et envoyer le code de vérification
        verification_code = generate_verification_code()

        # Stocker en session
        request.session['phone_number'] = phone
        request.session['verification_code'] = verification_code
        request.session['code_timestamp'] = timezone.now().timestamp()

        # Envoyer le code via service multi-méthodes
        try:
            from .services.multi_whatsapp_service import MultiWhatsAppService
            whatsapp_service = MultiWhatsAppService()
            result = whatsapp_service.send_verification_code(phone, verification_code)
            success = result.get('success', False)

            if success:
                # Définir la méthode de vérification comme WhatsApp
                request.session['verification_method'] = 'whatsapp'
                return redirect('core:phone_verification')
            else:
                logger.error(f"❌ Verification service returned False for {phone}")
                return self.get(request, *args, **kwargs)

        except Exception as e:
            logger.error(f"❌ Exception in verification service: {str(e)}")
            return self.get(request, *args, **kwargs)


class OnboardingView(LoginRequiredMixin, TemplateView):
    """Vue d'onboarding pour les nouveaux utilisateurs"""
    template_name = 'core/onboarding.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['user'] = self.request.user
        return context


class OnboardingStep1View(TemplateView):
    """Étape 1: Comprendre Nestria"""
    template_name = 'core/onboarding_step1.html'


class OnboardingStep2View(TemplateView):
    """Étape 2: Comment rechercher"""
    template_name = 'core/onboarding_step2.html'


class OnboardingStep3View(TemplateView):
    """Étape 3: Créer des wishlists"""
    template_name = 'core/onboarding_step3.html'


class SignupDemoView(TemplateView):
    """Vue de démonstration du processus d'inscription"""
    template_name = 'core/signup_demo.html'


class TestSignupView(TemplateView):
    """Vue de test rapide du processus d'inscription"""
    template_name = 'core/test_signup.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['debug'] = settings.DEBUG
        return context


@csrf_exempt
@require_http_methods(["POST"])
def clear_session(request):
    """Nettoyer la session pour les tests"""
    try:
        # Nettoyer toutes les données de session liées à l'inscription
        session_keys = [
            'phone_number', 'phone_verified', 'signup_email', 'signup_first_name',
            'signup_last_name', 'signup_birthdate', 'verification_code',
            'code_timestamp', 'no_marketing'
        ]

        for key in session_keys:
            request.session.pop(key, None)

        return JsonResponse({'success': True, 'message': 'Session cleared successfully'})

    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)})


class EmailAuthView(TemplateView):
    """Vue pour l'authentification par email"""

    def get(self, request, *args, **kwargs):
        """Redirection vers la page d'authentification par email"""
        from django.shortcuts import redirect
        from django.contrib import messages
        messages.success(request, 'Email authentication will be implemented')
        # Simuler une connexion réussie en redirigeant vers contact-info
        request.session['social_auth_provider'] = 'email'
        return redirect('core:contact_info')


class GoogleAuthView(TemplateView):
    """Vue pour l'authentification Google - Démo"""

    def get(self, request, *args, **kwargs):
        """Simuler l'authentification Google avec des données réelles"""
        from django.shortcuts import redirect
        from django.contrib import messages

        # Vérifier si l'utilisateur existe déjà avec cet email
        demo_email = '<EMAIL>'

        try:
            existing_user = User.objects.get(email=demo_email)
            # Utilisateur existe, le connecter
            login(request, existing_user)
            messages.success(request, f'✅ Welcome back, {existing_user.first_name}!')
            return redirect('core:home')
        except User.DoesNotExist:
            # Créer un nouvel utilisateur
            user = User.objects.create_user(
                username=demo_email,
                email=demo_email,
                first_name='Auba',
                last_name='Me'
            )

            # Créer le profil utilisateur
            from core.models import UserProfile
            UserProfile.objects.create(user=user)

            # Connecter l'utilisateur
            login(request, user)

            messages.success(request, f'✅ Welcome to Nestria, {user.first_name}!')
            return redirect('core:home')


class FacebookAuthView(TemplateView):
    """Vue pour l'authentification Facebook - Démo"""

    def get(self, request, *args, **kwargs):
        """Simuler l'authentification Facebook"""
        from django.shortcuts import redirect
        from django.contrib import messages

        # Vérifier si l'utilisateur existe déjà avec cet email
        demo_email = '<EMAIL>'

        try:
            existing_user = User.objects.get(email=demo_email)
            # Utilisateur existe, le connecter
            login(request, existing_user)
            messages.success(request, f'✅ Welcome back, {existing_user.first_name}!')
            return redirect('core:home')
        except User.DoesNotExist:
            # Créer un nouvel utilisateur
            user = User.objects.create_user(
                username=demo_email,
                email=demo_email,
                first_name='Facebook',
                last_name='User'
            )

            # Créer le profil utilisateur
            from core.models import UserProfile
            UserProfile.objects.create(user=user)

            # Connecter l'utilisateur
            login(request, user)

            messages.success(request, f'✅ Welcome to Nestria, {user.first_name}!')
            return redirect('core:home')


class AppleAuthView(TemplateView):
    """Vue pour l'authentification Apple - Démo"""

    def get(self, request, *args, **kwargs):
        """Simuler l'authentification Apple"""
        from django.shortcuts import redirect
        from django.contrib import messages

        # Vérifier si l'utilisateur existe déjà avec cet email
        demo_email = '<EMAIL>'

        try:
            existing_user = User.objects.get(email=demo_email)
            # Utilisateur existe, le connecter
            login(request, existing_user)
            messages.success(request, f'✅ Welcome back, {existing_user.first_name}!')
            return redirect('core:home')
        except User.DoesNotExist:
            # Créer un nouvel utilisateur
            user = User.objects.create_user(
                username=demo_email,
                email=demo_email,
                first_name='Apple',
                last_name='User'
            )

            # Créer le profil utilisateur
            from core.models import UserProfile
            UserProfile.objects.create(user=user)

            # Connecter l'utilisateur
            login(request, user)

            messages.success(request, f'✅ Welcome to Nestria, {user.first_name}!')
            return redirect('core:home')


class SignupFlowDemoView(TemplateView):
    """Vue pour la démonstration du flux d'inscription"""
    template_name = 'core/signup_flow_demo.html'


# Signal handler pour allauth
from allauth.socialaccount.signals import pre_social_login
from django.dispatch import receiver

@receiver(pre_social_login)
def populate_profile(sender, request, sociallogin, **kwargs):
    """
    Signal handler pour pré-remplir les informations utilisateur
    depuis les données des réseaux sociaux
    """
    user = sociallogin.user
    if sociallogin.account.provider == 'google':
        extra_data = sociallogin.account.extra_data

        # Stocker les informations en session pour finish-signup
        request.session['social_auth_provider'] = 'google'
        request.session['social_auth_data'] = extra_data
        request.session['signup_email'] = user.email
        request.session['signup_first_name'] = user.first_name
        request.session['signup_last_name'] = user.last_name

        # Ajouter des informations supplémentaires si disponibles
        if 'picture' in extra_data:
            request.session['user_picture'] = extra_data['picture']

    elif sociallogin.account.provider == 'facebook':
        extra_data = sociallogin.account.extra_data

        request.session['social_auth_provider'] = 'facebook'
        request.session['social_auth_data'] = extra_data
        request.session['signup_email'] = user.email
        request.session['signup_first_name'] = user.first_name
        request.session['signup_last_name'] = user.last_name

        if 'picture' in extra_data:
            request.session['user_picture'] = extra_data['picture']['data']['url']

    elif sociallogin.account.provider == 'apple':
        extra_data = sociallogin.account.extra_data

        request.session['social_auth_provider'] = 'apple'
        request.session['social_auth_data'] = extra_data
        request.session['signup_email'] = user.email
        request.session['signup_first_name'] = user.first_name
        request.session['signup_last_name'] = user.last_name


@csrf_exempt
def resend_verification_code(request):
    """Vue pour renvoyer un code de vérification"""
    if request.method != 'POST':
        return JsonResponse({'success': False, 'error': 'Method not allowed'})

    try:
        import json
        data = json.loads(request.body)
        phone_number = data.get('phone_number') or request.session.get('phone_number')

        if not phone_number:
            return JsonResponse({'success': False, 'error': 'Phone number not found'})

        # Générer un nouveau code
        verification_code = generate_verification_code()

        # Stocker en session
        request.session['verification_code'] = verification_code
        request.session['code_timestamp'] = timezone.now().timestamp()

        # Mettre à jour le numéro en session si un nouveau numéro est fourni
        if data.get('phone_number'):
            request.session['phone_number'] = phone_number

        # Envoyer le code via le service WhatsApp multi-méthodes
        from .services.multi_whatsapp_service import MultiWhatsAppService
        whatsapp_service = MultiWhatsAppService()
        result = whatsapp_service.send_verification_code(phone_number, verification_code)
        success = result.get('success', False)

        if success:
            # Nettoyer les anciens messages
            storage = messages.get_messages(request)
            storage.used = True

            # En mode développement, afficher le code


            return JsonResponse({
                'success': True,
                'message': 'New verification code sent successfully'
            })
        else:
            return JsonResponse({'success': False, 'error': 'Failed to send verification code'})

    except Exception as e:
        logger.error(f"Error resending verification code: {str(e)}")
        return JsonResponse({'success': False, 'error': str(e)})


@csrf_exempt
def change_phone_number(request):
    """Vue pour changer de numéro de téléphone"""
    if request.method != 'POST':
        return JsonResponse({'success': False, 'error': 'Method not allowed'})

    try:
        import json
        data = json.loads(request.body)
        new_phone_number = data.get('phone_number')

        if not new_phone_number:
            return JsonResponse({'success': False, 'error': 'Phone number is required'})

        # Nettoyer et valider le nouveau numéro
        new_phone_number = new_phone_number.strip().replace(' ', '').replace('-', '')
        if not new_phone_number.startswith('+'):
            new_phone_number = '+' + new_phone_number

        # Vérifier si le numéro existe déjà
        from core.models import UserProfile
        if UserProfile.objects.filter(phone_number=new_phone_number).exists():
            return JsonResponse({'success': False, 'error': 'An account with this phone number already exists'})

        # Générer un nouveau code
        verification_code = generate_verification_code()

        # Mettre à jour la session avec le nouveau numéro
        request.session['phone_number'] = new_phone_number
        request.session['verification_code'] = verification_code
        request.session['code_timestamp'] = timezone.now().timestamp()

        # Envoyer le code au nouveau numéro via service multi-méthodes
        try:
            from .services.multi_whatsapp_service import MultiWhatsAppService
            whatsapp_service = MultiWhatsAppService()
            result = whatsapp_service.send_verification_code(new_phone_number, verification_code)
            success = result.get('success', False)
        except Exception as e:
            logger.error(f"Error sending WhatsApp to new number: {str(e)}")
            success = False

        if success:
            return JsonResponse({
                'success': True,
                'message': f'Verification code sent to {new_phone_number}',
                'new_phone_number': new_phone_number
            })
        else:
            return JsonResponse({'success': False, 'error': 'Failed to send verification code to new number'})

    except Exception as e:
        logger.error(f"Error changing phone number: {str(e)}")
        return JsonResponse({'success': False, 'error': str(e)})


@csrf_exempt
def send_email_verification(request):
    """Vue pour envoyer un code de vérification par email"""
    if request.method != 'POST':
        return JsonResponse({'success': False, 'error': 'Method not allowed'})

    try:
        import json
        data = json.loads(request.body)
        email = data.get('email')

        if not email:
            return JsonResponse({'success': False, 'error': 'Email is required'})

        # Valider l'email
        if '@' not in email or '.' not in email:
            return JsonResponse({'success': False, 'error': 'Invalid email format'})

        # Vérifier si l'email existe dans la base de données
        from django.contrib.auth.models import User
        if not User.objects.filter(email=email).exists():
            return JsonResponse({'success': False, 'error': 'Email does not exist. Please create an account first.'})

        # Générer un nouveau code
        verification_code = generate_verification_code()

        # Stocker en session (remplacer la méthode de vérification)
        request.session['verification_method'] = 'email'
        request.session['verification_email'] = email
        request.session['verification_code'] = verification_code
        request.session['code_timestamp'] = timezone.now().timestamp()

        # Envoyer le code par email
        try:
            success = send_email_code(email, verification_code, "User")
        except Exception as e:
            logger.error(f"Error sending email verification: {str(e)}")
            success = False

        if success:
            return JsonResponse({
                'success': True,
                'message': f'Verification code sent to {email}',
                'email': email
            })
        else:
            return JsonResponse({'success': False, 'error': 'Failed to send verification email'})

    except Exception as e:
        logger.error(f"Error sending email verification: {str(e)}")
        return JsonResponse({'success': False, 'error': str(e)})


class LoginSignupView(TemplateView):
    """Vue pour la page de login/signup combinée"""
    template_name = 'registration/login_signup.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['debug'] = settings.DEBUG
        return context

    def post(self, request, *args, **kwargs):
        form_type = request.POST.get('form_type')

        if form_type == 'login':
            return self.handle_login(request)
        elif form_type == 'signup':
            return self.handle_signup(request)
        else:
            messages.error(request, 'Invalid form submission.')
            return self.get(request, *args, **kwargs)

    def handle_login(self, request):
        """Gérer la connexion utilisateur"""
        email = request.POST.get('email', '').strip().lower()
        password = request.POST.get('password', '')

        if not email or not password:
            messages.error(request, 'Email and password are required.')
            return self.get(request)

        # Authentifier l'utilisateur
        from django.contrib.auth import authenticate, login
        user = authenticate(request, username=email, password=password)

        if user is not None:
            if user.is_active:
                login(request, user, backend='django.contrib.auth.backends.ModelBackend')
                messages.success(request, f'Welcome back, {user.first_name or user.email}!')

                # Rediriger vers la page d'accueil ou la page demandée
                next_url = request.GET.get('next', '/')
                return redirect(next_url)
            else:
                messages.error(request, 'Your account is disabled.')
        else:
            # Vérifier si l'utilisateur existe
            from django.contrib.auth.models import User
            if User.objects.filter(email=email).exists():
                messages.error(request, 'Invalid password. Please try again.')
            else:
                messages.error(request, 'No account found with this email. Please sign up first.')

        return self.get(request)

    def handle_signup(self, request):
        """Gérer l'inscription utilisateur"""
        email = request.POST.get('email', '').strip().lower()
        password = request.POST.get('password', '')

        if not email or not password:
            messages.error(request, 'Email and password are required.')
            return self.get(request)

        # Valider l'email
        from django.core.validators import validate_email
        from django.core.exceptions import ValidationError
        try:
            validate_email(email)
        except ValidationError:
            messages.error(request, 'Please enter a valid email address.')
            return self.get(request)

        # Valider le mot de passe
        if len(password) < 6:
            messages.error(request, 'Password must be at least 6 characters long.')
            return self.get(request)

        # Vérifier si l'utilisateur existe déjà
        from django.contrib.auth.models import User
        if User.objects.filter(email=email).exists():
            messages.error(request, 'An account with this email already exists. Please log in instead.')
            return self.get(request)

        # Créer l'utilisateur
        try:
            user = User.objects.create_user(
                username=email,
                email=email,
                password=password
            )

            # Créer le profil utilisateur
            from core.models import UserProfile
            UserProfile.objects.create(
                user=user
            )

            # Connecter automatiquement l'utilisateur
            from django.contrib.auth import login
            login(request, user)

            messages.success(request, f'Welcome to Nestria! Your account has been created successfully.')
            return redirect('/')

        except Exception as e:
            logger.error(f"Error creating user: {str(e)}")
            return redirect('core:phone_signup')


class ModernLoginView(TemplateView):
    """Vue moderne de login/signup avec design amélioré"""
    template_name = 'registration/modern_login.html'

    def post(self, request, *args, **kwargs):
        """Gérer les soumissions de formulaire"""
        signup = request.POST.get('signup')

        if signup:
            return self.handle_signup(request)
        else:
            return self.handle_login(request)

    def handle_login(self, request):
        """Gérer la connexion utilisateur"""
        email = request.POST.get('email', '').strip().lower()
        password = request.POST.get('password', '')

        if not email or not password:
            messages.error(request, 'Email and password are required.')
            return self.get(request)

        # Authentifier l'utilisateur
        user = authenticate(request, username=email, password=password)

        if user is not None:
            if user.is_active:
                login(request, user, backend='django.contrib.auth.backends.ModelBackend')
                messages.success(request, f'Welcome back, {user.first_name or user.email}!')
                return redirect('core:home')
            else:
                messages.error(request, 'Your account is disabled.')
        else:
            # Vérifier si l'utilisateur existe
            if User.objects.filter(email=email).exists():
                messages.error(request, 'Invalid password. Please try again.')
            else:
                messages.error(request, 'No account found with this email. Please sign up first.')

        return self.get(request)

    def handle_signup(self, request):
        """Gérer l'inscription utilisateur"""
        first_name = request.POST.get('first_name', '').strip()
        last_name = request.POST.get('last_name', '').strip()
        email = request.POST.get('email', '').strip().lower()
        password1 = request.POST.get('password1', '')
        password2 = request.POST.get('password2', '')

        # Validation
        if not all([first_name, last_name, email, password1, password2]):
            messages.error(request, 'All fields are required.')
            return self.get(request)

        if password1 != password2:
            messages.error(request, 'Passwords do not match.')
            return self.get(request)

        if len(password1) < 6:
            messages.error(request, 'Password must be at least 6 characters long.')
            return self.get(request)

        # Vérifier si l'utilisateur existe déjà
        if User.objects.filter(email=email).exists():
            messages.error(request, 'An account with this email already exists. Please log in instead.')
            return self.get(request)

        # Créer l'utilisateur
        try:
            user = User.objects.create_user(
                username=email,
                email=email,
                password=password1,
                first_name=first_name,
                last_name=last_name
            )

            # Créer le profil utilisateur
            from core.models import UserProfile
            UserProfile.objects.get_or_create(user=user)

            # Connecter automatiquement l'utilisateur
            login(request, user, backend='django.contrib.auth.backends.ModelBackend')

            # Envoyer l'email de bienvenue
            try:
                from core.email_service import EmailService
                EmailService.send_welcome_email(
                    user_email=user.email,
                    user_name=user.get_full_name()
                )
            except Exception as e:
                messages.warning(request, 'Account created successfully, but welcome email could not be sent.')

            messages.success(request, f'Welcome to Nestria, {user.first_name}!')
            return redirect('core:home')

        except Exception as e:
            logger.error(f"Error creating user account: {str(e)}")
            return redirect('core:phone_signup')


def logout_user(request):
    """Vue pour déconnecter l'utilisateur"""
    from django.contrib.auth import logout
    logout(request)
    messages.success(request, 'You have been logged out successfully.')
    return redirect('core:login_signup')


# Vues pour l'authentification sociale
class GoogleSelectView(TemplateView):
    """Vue pour la sélection de compte Google"""
    template_name = 'auth/google_select.html'


class FacebookLoginView(TemplateView):
    """Vue pour la connexion Facebook"""
    template_name = 'auth/facebook_login.html'


class AppleLoginView(TemplateView):
    """Vue pour la connexion Apple"""
    template_name = 'auth/apple_login.html'


class ForgotPasswordView(TemplateView):
    """Vue pour la réinitialisation de mot de passe"""
    template_name = 'registration/forgot_password.html'

    def post(self, request, *args, **kwargs):
        """Gérer la demande de réinitialisation de mot de passe"""
        email = request.POST.get('email', '').strip().lower()

        if not email:
            messages.error(request, 'Email address is required.')
            return self.get(request, *args, **kwargs)

        # Vérifier si l'utilisateur existe
        try:
            user = User.objects.get(email=email)

            # Générer un mot de passe temporaire
            import random
            import string
            temp_password = ''.join(random.choices(string.ascii_letters + string.digits, k=8))

            # Mettre à jour le mot de passe de l'utilisateur
            user.set_password(temp_password)
            user.save()

            # Envoyer l'email avec le mot de passe temporaire
            try:
                from core.email_service import EmailService
                success = EmailService.send_mail(
                    to=user.email,
                    subject='Your temporary password - Nestria',
                    template_name='password_reset',
                    context={
                        'user_name': user.get_full_name() or user.email,
                        'temp_password': temp_password,
                        'login_url': request.build_absolute_uri('/login/'),
                    }
                )

                if success:
                    messages.success(request, f'A temporary password has been sent to {email}. Please check your email.')
                else:
                    messages.error(request, 'Failed to send email. Please try again later.')

            except Exception as e:
                # En mode développement, afficher le mot de passe même si l'email échoue
                from django.conf import settings
                if settings.DEBUG:
                    messages.success(request, f'Temporary password: {temp_password} (Email service not configured)')
                else:
                    messages.error(request, 'Failed to send email. Please try again later.')
                    logger.error(f"Error sending password reset email: {str(e)}")

        except User.DoesNotExist:
            # Pour des raisons de sécurité, ne pas révéler si l'email existe ou non
            messages.success(request, f'If an account with {email} exists, a temporary password has been sent.')

        return self.get(request, *args, **kwargs)
