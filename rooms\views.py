from django.shortcuts import render, get_object_or_404
from django.views.generic import DetailView, TemplateView, ListView
from django.contrib.auth.mixins import LoginRequiredMixin
from .models import Room, Destination
from django.http import JsonResponse


class RoomDetailView(DetailView):
    """Room detail view"""
    model = Room
    template_name = 'rooms/detail.html'
    context_object_name = 'room'

    def get_queryset(self):
        return Room.objects.filter(is_active=True).select_related('host', 'room_type').prefetch_related('amenities', 'images', 'reviews__user')


class BookingView(LoginRequiredMixin, TemplateView):
    """Booking view for a room"""
    template_name = 'rooms/booking.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        room = get_object_or_404(Room, pk=kwargs['pk'], is_active=True)
        context['room'] = room
        return context


class DestinationListView(ListView):
    """List view for destinations"""
    model = Destination
    template_name = 'rooms/destinations.html'
    context_object_name = 'destinations'
    paginate_by = 12

    def get_queryset(self):
        return Destination.objects.filter(is_featured=True).prefetch_related('images')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Room count is already calculated in the model's @property
        return context


class DestinationDetailView(DetailView):
    """Detail view for a destination"""
    model = Destination
    template_name = 'rooms/destination_detail.html'
    context_object_name = 'destination'
    slug_field = 'slug'
    slug_url_kwarg = 'slug'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        destination = self.get_object()
        # Get rooms in this destination
        context['rooms'] = Room.objects.filter(
            city__icontains=destination.name,
            is_active=True
        ).select_related('host', 'room_type').prefetch_related('images')[:8]
        return context


class CityHotelsView(ListView):
    """View to display hotels by city with map"""
    model = Room
    template_name = 'rooms/city_hotels.html'
    context_object_name = 'rooms'
    paginate_by = 12

    def get_queryset(self):
        city = self.kwargs.get('city')
        return Room.objects.filter(
            city__icontains=city,
            is_active=True
        ).select_related('host', 'room_type').prefetch_related('images')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        city = self.kwargs.get('city')
        context['city'] = city

        # Try to get destination info for this city
        try:
            destination = Destination.objects.filter(name__icontains=city).first()
            context['destination'] = destination
        except Destination.DoesNotExist:
            context['destination'] = None

        # Get city coordinates for map center
        rooms_with_coords = self.get_queryset().exclude(latitude__isnull=True, longitude__isnull=True)
        if rooms_with_coords.exists():
            # Calculate center coordinates
            latitudes = [room.latitude for room in rooms_with_coords if room.latitude]
            longitudes = [room.longitude for room in rooms_with_coords if room.longitude]
            if latitudes and longitudes:
                context['center_lat'] = sum(latitudes) / len(latitudes)
                context['center_lng'] = sum(longitudes) / len(longitudes)
            else:
                context['center_lat'] = 48.8566  # Default to Paris
                context['center_lng'] = 2.3522
        else:
            context['center_lat'] = 48.8566  # Default to Paris
            context['center_lng'] = 2.3522

        return context


def get_cities_api(request):
    """API endpoint to get all cities with rooms"""
    cities = Room.objects.filter(is_active=True).values_list('city', flat=True).distinct()
    cities_list = [city for city in cities if city]  # Remove empty cities
    return JsonResponse({'cities': sorted(cities_list)})
