# 📧 Guide de Configuration Email - Airbnb Clone

## 🎯 Vue d'ensemble

Ce guide vous explique comment configurer l'envoi d'emails de confirmation de réservation dans votre application Airbnb Clone.

## 🚀 Configuration Rapide (Développement)

Pour tester rapidement sans configuration SMTP, utilisez le backend console :

```python
# Dans airbnb_clone/settings.py
EMAIL_BACKEND = 'django.core.mail.backends.console.EmailBackend'
```

Les emails s'afficheront dans la console au lieu d'être envoyés.

## 📧 Configuration SMTP (Production)

### Option 1: Gmail (Recommandé pour les tests)

1. **Activez l'authentification à 2 facteurs** sur votre compte Gmail
2. **Gén<PERSON>rez un mot de passe d'application** :
   - Allez dans Paramètres Google → Sécurité
   - Authentification à 2 facteurs → Mots de passe des applications
   - Sélectionnez "Autre" et nommez-le "Airbnb Clone"
   - Copiez le mot de passe généré (16 caractères)

3. **Mettez à jour settings.py** :
```python
EMAIL_BACKEND = 'django.core.mail.backends.smtp.EmailBackend'
EMAIL_HOST = 'smtp.gmail.com'
EMAIL_PORT = 587
EMAIL_USE_TLS = True
EMAIL_HOST_USER = '<EMAIL>'  # Votre email Gmail
EMAIL_HOST_PASSWORD = 'abcd efgh ijkl mnop'  # Mot de passe d'application
DEFAULT_FROM_EMAIL = 'Airbnb Clone <<EMAIL>>'
```

### Option 2: Autres Fournisseurs

#### Outlook/Hotmail
```python
EMAIL_HOST = 'smtp-mail.outlook.com'
EMAIL_PORT = 587
EMAIL_USE_TLS = True
```

#### Yahoo
```python
EMAIL_HOST = 'smtp.mail.yahoo.com'
EMAIL_PORT = 587
EMAIL_USE_TLS = True
```

#### SendGrid (Service professionnel)
```python
EMAIL_HOST = 'smtp.sendgrid.net'
EMAIL_PORT = 587
EMAIL_USE_TLS = True
EMAIL_HOST_USER = 'apikey'
EMAIL_HOST_PASSWORD = 'votre-api-key-sendgrid'
```

## 🧪 Test de Configuration

Exécutez le script de test pour vérifier votre configuration :

```bash
python test_email.py
```

Ce script va :
- ✅ Tester la configuration SMTP
- 📋 Créer une réservation de test
- 📧 Générer et envoyer un email de confirmation
- 📊 Afficher les résultats

## 🎨 Personnalisation des Templates

### Templates d'Email

Les templates se trouvent dans `templates/emails/` :

- `booking_confirmation.html` - Version HTML (design professionnel)
- `booking_confirmation.txt` - Version texte (fallback)

### Variables Disponibles

Dans les templates, vous avez accès à :

```django
{{ reservation.confirmation_number }}  # Numéro de confirmation
{{ guest_name }}                       # Nom complet du client
{{ guest_email }}                      # Email du client
{{ room.title }}                       # Nom de la propriété
{{ room.city }}, {{ room.country }}    # Localisation
{{ reservation.check_in|date:"l, F j, Y" }}  # Date d'arrivée
{{ reservation.check_out|date:"l, F j, Y" }} # Date de départ
{{ reservation.total_guests }}         # Nombre d'invités
{{ reservation.nights }}               # Nombre de nuits
{{ reservation.total_price }}          # Prix total
{{ property_url }}                     # Lien vers la propriété
```

## 🔧 Fonctionnalités Avancées

### Emails Conditionnels

Vous pouvez personnaliser les emails selon le type de réservation :

```python
# Dans reservations/views.py
if reservation.total_price > 1000:
    template = 'emails/booking_confirmation_premium.html'
else:
    template = 'emails/booking_confirmation.html'
```

### Emails Multilingues

Ajoutez la détection de langue :

```python
from django.utils.translation import get_language

language = get_language()
template = f'emails/booking_confirmation_{language}.html'
```

## 🛡️ Sécurité et Bonnes Pratiques

### Variables d'Environnement

Pour la production, utilisez des variables d'environnement :

```python
import os

EMAIL_HOST_USER = os.environ.get('EMAIL_HOST_USER')
EMAIL_HOST_PASSWORD = os.environ.get('EMAIL_HOST_PASSWORD')
```

### Fichier .env

Créez un fichier `.env` :

```env
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=votre-mot-de-passe-app
```

Et ajoutez dans settings.py :

```python
from dotenv import load_dotenv
load_dotenv()
```

### Gestion des Erreurs

Le système gère automatiquement les erreurs d'email :

```python
try:
    send_mail(...)
    messages.success(request, 'Email de confirmation envoyé!')
except Exception as e:
    # La réservation est créée même si l'email échoue
    messages.warning(request, 'Réservation confirmée, mais email non envoyé.')
```

## 📱 Test Complet

### 1. Testez la Page de Checkout

1. Allez sur http://127.0.0.1:8000/
2. Sélectionnez une propriété
3. Choisissez vos dates et invités
4. Cliquez sur "Reserve"
5. Remplissez le formulaire de checkout
6. Cliquez sur "Confirm and pay"

### 2. Vérifiez l'Email

- **Console Backend** : Vérifiez la console du serveur
- **SMTP Backend** : Vérifiez votre boîte email

## 🎉 Fonctionnalités Email Incluses

### ✨ Design Professionnel
- 🎨 Design responsive (mobile/desktop)
- 🌈 Couleurs de marque Airbnb
- 📱 Compatible tous clients email
- 🖼️ Images de propriété incluses

### 📋 Informations Complètes
- 🎫 Numéro de confirmation unique
- 📅 Dates et durée du séjour
- 👥 Nombre d'invités
- 💰 Détail des prix
- 🏠 Informations de la propriété
- 📍 Localisation et évaluations

### 🔗 Actions Rapides
- 🌐 Lien vers la propriété
- 📞 Informations de contact
- 📋 Instructions importantes
- ❌ Politique d'annulation

## 🆘 Dépannage

### Erreur "Username and Password not accepted"
- ✅ Vérifiez que l'authentification 2FA est activée
- ✅ Utilisez un mot de passe d'application, pas votre mot de passe normal
- ✅ Vérifiez l'email et le mot de passe dans settings.py

### Email non reçu
- ✅ Vérifiez les spams/courrier indésirable
- ✅ Testez avec le backend console d'abord
- ✅ Vérifiez les logs du serveur pour les erreurs

### Template non trouvé
- ✅ Vérifiez que les fichiers sont dans `templates/emails/`
- ✅ Redémarrez le serveur Django
- ✅ Vérifiez les chemins dans TEMPLATES settings

## 📞 Support

Si vous rencontrez des problèmes :

1. 🧪 Exécutez `python test_email.py` pour diagnostiquer
2. 📊 Vérifiez les logs du serveur Django
3. 🔍 Consultez la documentation Django sur les emails
4. 💬 Demandez de l'aide sur les forums Django

---

🎉 **Félicitations !** Votre système d'email de confirmation est maintenant configuré et prêt à impressionner vos utilisateurs !
