from django.core.management.base import BaseCommand
from rooms.models import Room
from decimal import Decimal
import random


class Command(BaseCommand):
    help = 'Add geographic coordinates to existing properties'

    def handle(self, *args, **options):
        self.stdout.write('Adding coordinates to properties...')
        
        # City coordinates with some variation for realistic property locations
        city_coordinates = {
            'chicago': {'lat': 41.8781, 'lng': -87.6298, 'radius': 0.05},
            'boston': {'lat': 42.3601, 'lng': -71.0589, 'radius': 0.03},
            'los_angeles': {'lat': 34.0522, 'lng': -118.2437, 'radius': 0.08},
            'aspen': {'lat': 39.1911, 'lng': -106.8175, 'radius': 0.02},
            'miami': {'lat': 25.7617, 'lng': -80.1918, 'radius': 0.04},
            'new_york': {'lat': 40.7128, 'lng': -74.0060, 'radius': 0.06},
        }
        
        # Property location mapping based on titles
        property_locations = {
            'Luxury Penthouse': 'chicago',
            'Historic Brownstone': 'boston', 
            'Modern Loft in Arts District': 'los_angeles',
            'Mountain Cabin Retreat': 'aspen',
            'Beachfront Villa': 'miami',
            'Cozy Downtown Apartment': 'new_york',
        }
        
        rooms = Room.objects.all()
        
        for room in rooms:
            # Skip if room already has coordinates
            if room.latitude and room.longitude:
                self.stdout.write(f'Room "{room.title}" already has coordinates, skipping...')
                continue
            
            # Determine location based on title
            location_key = None
            for title_key, city_key in property_locations.items():
                if title_key in room.title:
                    location_key = city_key
                    break
            
            # Default to New York if no match
            if not location_key:
                location_key = 'new_york'
            
            city_data = city_coordinates[location_key]
            
            # Add some random variation to make properties appear in different locations
            lat_variation = (random.random() - 0.5) * city_data['radius']
            lng_variation = (random.random() - 0.5) * city_data['radius']
            
            room.latitude = Decimal(str(city_data['lat'] + lat_variation))
            room.longitude = Decimal(str(city_data['lng'] + lng_variation))
            
            # Add address information
            addresses = {
                'chicago': [
                    '123 N Michigan Ave, Chicago, IL',
                    '456 W Lake St, Chicago, IL', 
                    '789 S State St, Chicago, IL',
                    '321 E Wacker Dr, Chicago, IL'
                ],
                'boston': [
                    '100 Beacon St, Boston, MA',
                    '200 Newbury St, Boston, MA',
                    '300 Commonwealth Ave, Boston, MA',
                    '400 Boylston St, Boston, MA'
                ],
                'los_angeles': [
                    '123 S Grand Ave, Los Angeles, CA',
                    '456 W 3rd St, Los Angeles, CA',
                    '789 N Highland Ave, Los Angeles, CA',
                    '321 E 1st St, Los Angeles, CA'
                ],
                'aspen': [
                    '123 Main St, Aspen, CO',
                    '456 Mill St, Aspen, CO',
                    '789 Hopkins Ave, Aspen, CO',
                    '321 Galena St, Aspen, CO'
                ],
                'miami': [
                    '123 Ocean Dr, Miami Beach, FL',
                    '456 Collins Ave, Miami Beach, FL',
                    '789 Washington Ave, Miami Beach, FL',
                    '321 Lincoln Rd, Miami Beach, FL'
                ],
                'new_york': [
                    '123 5th Ave, New York, NY',
                    '456 Broadway, New York, NY',
                    '789 Park Ave, New York, NY',
                    '321 Madison Ave, New York, NY'
                ]
            }
            
            if not room.address:
                room.address = random.choice(addresses[location_key])
            
            room.save()
            
            self.stdout.write(f'✅ Updated "{room.title}":')
            self.stdout.write(f'   Location: {room.latitude}, {room.longitude}')
            self.stdout.write(f'   Address: {room.address}')
        
        self.stdout.write(self.style.SUCCESS('\n🎉 Property coordinates added successfully!'))
        
        # Statistics
        total_rooms = Room.objects.count()
        rooms_with_coords = Room.objects.filter(latitude__isnull=False, longitude__isnull=False).count()
        
        self.stdout.write(f'📊 Statistics:')
        self.stdout.write(f'  Total rooms: {total_rooms}')
        self.stdout.write(f'  Rooms with coordinates: {rooms_with_coords}')
        
        self.stdout.write('\nProperties now have geographic data for maps!')
