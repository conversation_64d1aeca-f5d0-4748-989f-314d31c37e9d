// Main JavaScript file for StayVibe

// Dark mode functionality
const darkMode = {
    init: () => {
        // Initialize dark mode based on localStorage or system preference
        const savedTheme = localStorage.getItem('darkMode');
        const systemPrefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;

        if (savedTheme === null) {
            // First time visitor, use system preference
            localStorage.setItem('darkMode', systemPrefersDark.toString());
        }

        // Listen for system theme changes
        window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', (e) => {
            if (localStorage.getItem('darkMode') === null) {
                localStorage.setItem('darkMode', e.matches.toString());
                location.reload(); // Refresh to apply changes
            }
        });
    },

    toggle: () => {
        const currentMode = localStorage.getItem('darkMode') === 'true';
        localStorage.setItem('darkMode', (!currentMode).toString());

        // Add smooth transition effect
        document.documentElement.style.transition = 'all 0.3s ease-in-out';
        setTimeout(() => {
            document.documentElement.style.transition = '';
        }, 300);
    }
};

// Utility functions
const utils = {
    // Format currency
    formatCurrency: (amount, currency = 'USD') => {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: currency
        }).format(amount);
    },

    // Format date
    formatDate: (date) => {
        return new Intl.DateTimeFormat('en-US', {
            year: 'numeric',
            month: 'short',
            day: 'numeric'
        }).format(new Date(date));
    },

    // Debounce function
    debounce: (func, wait) => {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    },

    // Show loading spinner
    showLoading: (element) => {
        element.innerHTML = '<div class="spinner mx-auto"></div>';
    },

    // Hide loading spinner
    hideLoading: (element, originalContent) => {
        element.innerHTML = originalContent;
    }
};

// Search functionality
const search = {
    init: () => {
        const searchForm = document.getElementById('search-form');
        if (searchForm) {
            searchForm.addEventListener('submit', search.handleSubmit);
        }

        // Auto-complete for location search
        const locationInput = document.getElementById('location-input');
        if (locationInput) {
            locationInput.addEventListener('input', utils.debounce(search.handleLocationSearch, 300));
        }
    },

    handleSubmit: (e) => {
        e.preventDefault();
        const formData = new FormData(e.target);
        const searchParams = new URLSearchParams(formData);
        window.location.href = `/search/?${searchParams.toString()}`;
    },

    handleLocationSearch: (e) => {
        const query = e.target.value;
        if (query.length < 2) return;

        // Here you would typically make an API call to get location suggestions
        console.log('Searching for locations:', query);
    }
};

// Property interactions
const property = {
    init: () => {
        // Initialize favorite buttons
        document.querySelectorAll('.favorite-btn').forEach(btn => {
            btn.addEventListener('click', property.toggleFavorite);
        });

        // Initialize image galleries
        document.querySelectorAll('.image-gallery').forEach(gallery => {
            property.initImageGallery(gallery);
        });
    },

    toggleFavorite: async (e) => {
        e.preventDefault();
        e.stopPropagation();
        
        const btn = e.currentTarget;
        const propertyId = btn.dataset.propertyId;
        const icon = btn.querySelector('i');
        
        // Check if user is authenticated
        if (!document.body.dataset.userAuthenticated) {
            AirbnbApp.showLoginModal();
            return;
        }

        try {
            const response = await fetch(`/api/properties/${propertyId}/toggle-favorite/`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
                }
            });

            if (response.status === 401 || response.status === 403) {
                AirbnbApp.showLoginModal();
                return;
            }

            if (response.ok) {
                const data = await response.json();
                if (data.is_favorite) {
                    icon.classList.remove('far');
                    icon.classList.add('fas', 'liked');
                    AirbnbApp.showNotification('Added to favorites ❤️', 'success');
                } else {
                    icon.classList.remove('fas', 'liked');
                    icon.classList.add('far');
                    AirbnbApp.showNotification('Removed from favorites', 'info');
                }
            }
        } catch (error) {
            console.error('Error toggling favorite:', error);
            AirbnbApp.showNotification('Error updating favorites', 'error');
        }
    },

    // Show login modal for unauthenticated users
    showLoginModal: () => {
        const modal = document.createElement('div');
        modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
        modal.innerHTML = `
            <div class="bg-white rounded-lg p-8 max-w-md w-full mx-4 transform transition-all duration-300 scale-95">
                <div class="text-center">
                    <i class="fas fa-heart text-red-500 text-4xl mb-4"></i>
                    <h3 class="text-xl font-semibold text-gray-900 mb-2">Login Required</h3>
                    <p class="text-gray-600 mb-6">You need to be logged in to save favorites and access all features.</p>
                    <div class="flex space-x-4">
                        <a href="/login/" class="flex-1 bg-red-500 text-white px-4 py-2 rounded-lg hover:bg-red-600 transition-colors">
                            Login
                        </a>
                        <button onclick="this.closest('.fixed').remove()" class="flex-1 bg-gray-200 text-gray-800 px-4 py-2 rounded-lg hover:bg-gray-300 transition-colors">
                            Cancel
                        </button>
                    </div>
                    <div class="mt-4 text-sm text-gray-500">
                        Don't have an account? <a href="/signup/" class="text-red-500 hover:text-red-600">Sign up</a>
                    </div>
                </div>
            </div>
        `;
        document.body.appendChild(modal);

        // Animate in
        setTimeout(() => {
            modal.querySelector('.transform').classList.remove('scale-95');
            modal.querySelector('.transform').classList.add('scale-100');
        }, 10);

        // Close modal when clicking outside
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                modal.remove();
            }
        });
    },

    // Show notification
    showNotification: (message, type = 'info') => {
        const notification = document.createElement('div');
        const bgColor = type === 'success' ? 'bg-green-500' : type === 'error' ? 'bg-red-500' : 'bg-blue-500';
        const icon = type === 'success' ? 'check' : type === 'error' ? 'exclamation-triangle' : 'info-circle';

        notification.className = `fixed top-4 right-4 ${bgColor} text-white px-6 py-3 rounded-lg shadow-lg z-50 transform translate-x-full transition-transform duration-300`;
        notification.innerHTML = `
            <div class="flex items-center">
                <i class="fas fa-${icon} mr-2"></i>
                <span>${message}</span>
            </div>
        `;

        document.body.appendChild(notification);

        // Slide in
        setTimeout(() => {
            notification.classList.remove('translate-x-full');
        }, 100);

        // Slide out and remove
        setTimeout(() => {
            notification.classList.add('translate-x-full');
            setTimeout(() => notification.remove(), 300);
        }, 3000);
    },

    initImageGallery: (gallery) => {
        const images = gallery.querySelectorAll('img');
        const dots = gallery.querySelectorAll('.gallery-dot');
        let currentIndex = 0;

        // Navigation buttons
        const prevBtn = gallery.querySelector('.gallery-prev');
        const nextBtn = gallery.querySelector('.gallery-next');

        if (prevBtn) {
            prevBtn.addEventListener('click', (e) => {
                e.preventDefault();
                e.stopPropagation();
                currentIndex = currentIndex > 0 ? currentIndex - 1 : images.length - 1;
                property.showImage(gallery, currentIndex);
            });
        }

        if (nextBtn) {
            nextBtn.addEventListener('click', (e) => {
                e.preventDefault();
                e.stopPropagation();
                currentIndex = currentIndex < images.length - 1 ? currentIndex + 1 : 0;
                property.showImage(gallery, currentIndex);
            });
        }

        // Dot navigation
        dots.forEach((dot, index) => {
            dot.addEventListener('click', (e) => {
                e.preventDefault();
                e.stopPropagation();
                currentIndex = index;
                property.showImage(gallery, currentIndex);
            });
        });
    },

    showImage: (gallery, index) => {
        const images = gallery.querySelectorAll('img');
        const dots = gallery.querySelectorAll('.gallery-dot');

        images.forEach((img, i) => {
            img.style.display = i === index ? 'block' : 'none';
        });

        dots.forEach((dot, i) => {
            dot.classList.toggle('active', i === index);
        });
    }
};

// Booking functionality
const booking = {
    init: () => {
        const bookingForm = document.getElementById('booking-form');
        if (bookingForm) {
            bookingForm.addEventListener('submit', booking.handleSubmit);
        }

        // Date picker initialization
        const checkInInput = document.getElementById('check-in');
        const checkOutInput = document.getElementById('check-out');
        
        if (checkInInput && checkOutInput) {
            booking.initDatePickers(checkInInput, checkOutInput);
        }

        // Guest counter
        const guestButtons = document.querySelectorAll('.guest-counter button');
        guestButtons.forEach(btn => {
            btn.addEventListener('click', booking.handleGuestCounter);
        });
    },

    handleSubmit: async (e) => {
        e.preventDefault();
        const form = e.target;
        const submitBtn = form.querySelector('button[type="submit"]');
        const originalText = submitBtn.textContent;

        submitBtn.disabled = true;
        submitBtn.textContent = 'Processing...';

        try {
            const formData = new FormData(form);
            const response = await fetch(form.action, {
                method: 'POST',
                body: formData,
                headers: {
                    'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
                }
            });

            if (response.ok) {
                const data = await response.json();
                if (data.redirect_url) {
                    window.location.href = data.redirect_url;
                }
            } else {
                throw new Error('Booking failed');
            }
        } catch (error) {
            console.error('Booking error:', error);
            alert('There was an error processing your booking. Please try again.');
        } finally {
            submitBtn.disabled = false;
            submitBtn.textContent = originalText;
        }
    },

    initDatePickers: (checkInInput, checkOutInput) => {
        const today = new Date().toISOString().split('T')[0];
        checkInInput.min = today;
        
        checkInInput.addEventListener('change', () => {
            const checkInDate = new Date(checkInInput.value);
            const nextDay = new Date(checkInDate);
            nextDay.setDate(nextDay.getDate() + 1);
            checkOutInput.min = nextDay.toISOString().split('T')[0];
            
            if (checkOutInput.value && new Date(checkOutInput.value) <= checkInDate) {
                checkOutInput.value = '';
            }
        });
    },

    handleGuestCounter: (e) => {
        const btn = e.target;
        const input = btn.parentElement.querySelector('input');
        const isIncrement = btn.textContent === '+';
        const currentValue = parseInt(input.value);
        const min = parseInt(input.min) || 1;
        const max = parseInt(input.max) || 16;

        if (isIncrement && currentValue < max) {
            input.value = currentValue + 1;
        } else if (!isIncrement && currentValue > min) {
            input.value = currentValue - 1;
        }

        // Update total guests display
        booking.updateTotalGuests();
    },

    updateTotalGuests: () => {
        const adults = parseInt(document.getElementById('adults')?.value || 0);
        const children = parseInt(document.getElementById('children')?.value || 0);
        const infants = parseInt(document.getElementById('infants')?.value || 0);
        const total = adults + children + infants;
        
        const guestDisplay = document.getElementById('guest-display');
        if (guestDisplay) {
            guestDisplay.textContent = `${total} guest${total !== 1 ? 's' : ''}`;
        }
    }
};

// Filters functionality
const filters = {
    init: () => {
        document.querySelectorAll('.filter-chip').forEach(chip => {
            chip.addEventListener('click', filters.toggleFilter);
        });

        const priceRange = document.getElementById('price-range');
        if (priceRange) {
            priceRange.addEventListener('input', utils.debounce(filters.updatePriceDisplay, 300));
        }
    },

    toggleFilter: (e) => {
        const chip = e.target;
        chip.classList.toggle('active');
        filters.applyFilters();
    },

    updatePriceDisplay: (e) => {
        const value = e.target.value;
        const display = document.getElementById('price-display');
        if (display) {
            display.textContent = utils.formatCurrency(value);
        }
    },

    applyFilters: utils.debounce(() => {
        const activeFilters = Array.from(document.querySelectorAll('.filter-chip.active'))
            .map(chip => chip.dataset.filter);
        
        // Here you would typically make an API call or update the URL
        console.log('Active filters:', activeFilters);
    }, 500)
};

// Modern animations and effects
const animations = {
    init: () => {
        // Intersection Observer for scroll animations
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('animate-fade-in');
                }
            });
        }, observerOptions);

        // Observe all elements with animation classes
        document.querySelectorAll('.animate-on-scroll').forEach(el => {
            observer.observe(el);
        });
    },

    // Smooth scroll to element
    scrollTo: (element, offset = 0) => {
        const targetPosition = element.offsetTop - offset;
        window.scrollTo({
            top: targetPosition,
            behavior: 'smooth'
        });
    },

    // Add loading animation to buttons
    addLoadingToButton: (button, text = 'Loading...') => {
        const originalText = button.innerHTML;
        button.innerHTML = `
            <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white inline" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            ${text}
        `;
        button.disabled = true;

        return () => {
            button.innerHTML = originalText;
            button.disabled = false;
        };
    }
};

// Initialize everything when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    darkMode.init();
    animations.init();
    search.init();
    property.init();
    booking.init();
    filters.init();

    // Add smooth scrolling to all anchor links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                animations.scrollTo(target, 80);
            }
        });
    });
});

// Export for use in other files
window.StayVibe = {
    darkMode,
    animations,
    utils,
    search,
    property,
    booking,
    filters
};
