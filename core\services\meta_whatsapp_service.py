"""
Service WhatsApp Business API Meta (Gratuit)
Alternative à CallMeBot
"""

import requests
import logging
import os
from django.conf import settings

logger = logging.getLogger(__name__)


class MetaWhatsAppService:
    """Service WhatsApp utilisant l'API Meta/Facebook"""
    
    def __init__(self):
        self.access_token = os.getenv('META_WHATSAPP_ACCESS_TOKEN', '')
        self.phone_number_id = os.getenv('META_WHATSAPP_PHONE_NUMBER_ID', '')
        self.base_url = "https://graph.facebook.com/v18.0"
        
    def send_verification_code(self, phone_number, verification_code):
        """
        Envoie un code de vérification via WhatsApp Business API Meta
        
        Args:
            phone_number (str): Numéro de téléphone (+212603999557)
            verification_code (str): Code à 6 chiffres
            
        Returns:
            dict: Résultat de l'envoi
        """
        
        if not self.access_token or not self.phone_number_id:
            # Mode simulation si pas configuré
            return self._simulate_whatsapp_send(phone_number, verification_code)
        
        # Nettoyer le numéro
        clean_phone = self._clean_phone_number(phone_number)
        
        # Préparer le message
        message_data = {
            "messaging_product": "whatsapp",
            "to": clean_phone,
            "type": "text",
            "text": {
                "body": f"🏠 *Nestria Verification*\n\nYour verification code is: *{verification_code}*\n\nThis code will expire in 5 minutes.\nDo not share this code with anyone.\n\nWelcome to Nestria! 🌟"
            }
        }
        
        headers = {
            "Authorization": f"Bearer {self.access_token}",
            "Content-Type": "application/json"
        }
        
        try:
            url = f"{self.base_url}/{self.phone_number_id}/messages"
            response = requests.post(url, json=message_data, headers=headers)
            
            if response.status_code == 200:
                result = response.json()
                logger.info(f"✅ WhatsApp Meta message sent successfully to {clean_phone}")
                
                return {
                    'success': True,
                    'message_id': result.get('messages', [{}])[0].get('id'),
                    'status': 'sent',
                    'method': 'meta'
                }
            else:
                error_data = response.json()
                logger.error(f"❌ WhatsApp Meta error for {clean_phone}: {error_data}")
                # Si erreur, basculer en simulation
                return self._simulate_whatsapp_send(phone_number, verification_code)
                
        except Exception as e:
            logger.error(f"❌ Unexpected error sending WhatsApp Meta to {clean_phone}: {str(e)}")
            # Si erreur, basculer en simulation
            return self._simulate_whatsapp_send(phone_number, verification_code)
    
    def _simulate_whatsapp_send(self, phone_number, verification_code):
        """Simulation si pas configuré"""
        
        print("\n" + "🔵" * 50)
        print("📱 WHATSAPP META SIMULATION")
        print("🔵" * 50)
        print(f"📞 TO: {phone_number}")
        print(f"🔐 CODE: {verification_code}")
        print(f"💬 MESSAGE:")
        print(f"   🏠 Nestria Verification")
        print(f"   ")
        print(f"   Your verification code is: {verification_code}")
        print(f"   ")
        print(f"   This code will expire in 5 minutes.")
        print(f"   Do not share this code with anyone.")
        print(f"   ")
        print(f"   Welcome to Nestria! 🌟")
        print("🔵" * 50)
        print("⚠️  POUR RECEVOIR SUR WHATSAPP RÉEL (META):")
        print("1. Créez une app Facebook Developer")
        print("2. Activez WhatsApp Business API")
        print("3. Configurez META_WHATSAPP_ACCESS_TOKEN et META_WHATSAPP_PHONE_NUMBER_ID")
        print("🔵" * 50 + "\n")
        
        return {
            'success': True,
            'message_id': f'meta_simulation_{verification_code}',
            'status': 'simulated',
            'method': 'meta_simulation'
        }
    
    def _clean_phone_number(self, phone_number):
        """Nettoie le numéro de téléphone"""
        if not phone_number:
            return None
            
        clean = phone_number.strip().replace(' ', '').replace('-', '').replace('(', '').replace(')', '')
        
        if not clean.startswith('+'):
            clean = '+' + clean
            
        return clean


# Instance globale
meta_whatsapp_service = MetaWhatsAppService()


def send_whatsapp_verification_meta(phone_number, verification_code):
    """
    Fonction helper pour envoyer via Meta WhatsApp API
    """
    result = meta_whatsapp_service.send_verification_code(phone_number, verification_code)
    return result.get('success', False)
