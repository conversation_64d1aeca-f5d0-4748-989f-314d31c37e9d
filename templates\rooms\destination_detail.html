{% extends 'base.html' %}
{% load static %}

{% block title %}{{ destination.name }}, {{ destination.country }} - Airbnb Clone{% endblock %}

{% block content %}
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Breadcrumb -->
    <nav class="breadcrumb mb-6">
        <a href="{% url 'core:home' %}">Home</a>
        <span class="mx-2">/</span>
        <a href="{% url 'rooms:destinations' %}">Destinations</a>
        <span class="mx-2">/</span>
        <span>{{ destination.name }}</span>
    </nav>

    <!-- Hero Section -->
    <div class="mb-8">
        <div class="relative h-96 rounded-lg overflow-hidden">
            {% if destination.main_image %}
                <img src="{{ destination.main_image.url }}" 
                     alt="{{ destination.name }}" 
                     class="w-full h-full object-cover">
            {% else %}
                <div class="w-full h-full bg-gradient-to-br from-airbnb-red to-pink-500 flex items-center justify-center">
                    <i class="fas fa-map-marker-alt text-white text-6xl"></i>
                </div>
            {% endif %}
            
            <!-- Overlay with title -->
            <div class="absolute inset-0 bg-black bg-opacity-40 flex items-end">
                <div class="p-8 text-white">
                    <h1 class="text-4xl md:text-5xl font-bold mb-2">{{ destination.name }}</h1>
                    <p class="text-xl opacity-90">{{ destination.country }}</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Content Grid -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <!-- Main Content -->
        <div class="lg:col-span-2">
            <!-- Description -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8">
                <h2 class="text-2xl font-bold text-gray-900 mb-4">About {{ destination.name }}</h2>
                <p class="text-gray-700 leading-relaxed mb-6">{{ destination.description }}</p>
                
                <!-- Historical Information -->
                <div class="border-t border-gray-200 pt-6">
                    <h3 class="text-xl font-semibold text-gray-900 mb-4 flex items-center">
                        <i class="fas fa-book text-airbnb-red mr-2"></i>
                        Historical Background
                    </h3>
                    <div class="text-gray-700 leading-relaxed">
                        {{ destination.historical_info|linebreaks }}
                    </div>
                </div>
            </div>

            <!-- Professional Photo Gallery -->
            {% if destination.images.all %}
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8" x-data="photoGallery()">
                    <div class="flex items-center justify-between mb-6">
                        <h3 class="text-xl font-semibold text-gray-900">Professional Gallery</h3>
                        <span class="text-sm text-gray-500">{{ destination.images.count }} photos</span>
                    </div>

                    <!-- Main Featured Image -->
                    <div class="mb-6">
                        <div class="relative group cursor-pointer rounded-lg overflow-hidden" @click="openGallery(0)">
                            <img src="{{ destination.images.first.image.url }}"
                                 alt="{{ destination.images.first.caption|default:destination.name }}"
                                 class="w-full h-96 object-cover group-hover:scale-105 transition-transform duration-300">
                            <div class="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-300"></div>
                            <div class="absolute bottom-4 left-4 text-white">
                                <p class="text-lg font-semibold">{{ destination.images.first.caption }}</p>
                                <p class="text-sm opacity-90">Click to view gallery</p>
                            </div>
                            <div class="absolute top-4 right-4 bg-black bg-opacity-50 text-white px-3 py-1 rounded-full text-sm">
                                <i class="fas fa-images mr-1"></i>
                                {{ destination.images.count }} photos
                            </div>
                        </div>
                    </div>

                    <!-- Thumbnail Grid -->
                    <div class="grid grid-cols-4 md:grid-cols-6 gap-3">
                        {% for image in destination.images.all|slice:"1:" %}
                            <div class="relative group cursor-pointer rounded-lg overflow-hidden" @click="openGallery({{ forloop.counter }})">
                                <img src="{{ image.image.url }}"
                                     alt="{{ image.caption|default:destination.name }}"
                                     class="w-full h-20 object-cover group-hover:opacity-80 transition-opacity">
                                <div class="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all duration-300 flex items-center justify-center">
                                    <i class="fas fa-search-plus text-white opacity-0 group-hover:opacity-100 transition-opacity"></i>
                                </div>
                            </div>
                        {% endfor %}
                    </div>

                    <!-- Advanced Gallery Modal -->
                    <div x-show="galleryOpen"
                         @click.away="closeGallery()"
                         x-transition
                         class="fixed inset-0 bg-black bg-opacity-90 z-50 flex items-center justify-center">

                        <!-- Gallery Container -->
                        <div class="relative w-full h-full flex items-center justify-center p-4">
                            <!-- Close Button -->
                            <button @click="closeGallery()"
                                    class="absolute top-6 right-6 text-white text-3xl z-20 hover:text-gray-300 transition-colors">
                                <i class="fas fa-times"></i>
                            </button>

                            <!-- Image Counter -->
                            <div class="absolute top-6 left-6 text-white z-20 bg-black bg-opacity-50 px-3 py-1 rounded-full">
                                <span x-text="currentIndex + 1"></span> / <span x-text="images.length"></span>
                            </div>

                            <!-- Previous Button -->
                            <button @click="previousImage()"
                                    class="absolute left-6 top-1/2 transform -translate-y-1/2 text-white text-3xl z-20 hover:text-gray-300 transition-colors">
                                <i class="fas fa-chevron-left"></i>
                            </button>

                            <!-- Next Button -->
                            <button @click="nextImage()"
                                    class="absolute right-6 top-1/2 transform -translate-y-1/2 text-white text-3xl z-20 hover:text-gray-300 transition-colors">
                                <i class="fas fa-chevron-right"></i>
                            </button>

                            <!-- Main Image -->
                            <div class="max-w-6xl max-h-full flex items-center justify-center">
                                <img :src="currentImage.url"
                                     :alt="currentImage.caption"
                                     class="max-w-full max-h-full object-contain">
                            </div>

                            <!-- Image Caption -->
                            <div class="absolute bottom-6 left-6 right-6 text-center">
                                <div class="bg-black bg-opacity-50 text-white px-6 py-3 rounded-lg inline-block">
                                    <p class="text-lg font-semibold" x-text="currentImage.caption"></p>
                                </div>
                            </div>

                            <!-- Thumbnail Navigation -->
                            <div class="absolute bottom-20 left-1/2 transform -translate-x-1/2">
                                <div class="flex space-x-2 bg-black bg-opacity-50 p-2 rounded-lg">
                                    <template x-for="(image, index) in images" :key="index">
                                        <button @click="currentIndex = index"
                                                :class="currentIndex === index ? 'ring-2 ring-white' : ''"
                                                class="w-12 h-12 rounded overflow-hidden">
                                            <img :src="image.url" :alt="image.caption" class="w-full h-full object-cover">
                                        </button>
                                    </template>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            {% endif %}

            <!-- Properties in this Destination -->
            {% if rooms %}
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <h3 class="text-xl font-semibold text-gray-900 mb-6">
                        Places to stay in {{ destination.name }}
                        <span class="text-sm font-normal text-gray-500">({{ destination.room_count }} properties)</span>
                    </h3>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        {% for room in rooms %}
                            <div class="property-card bg-white rounded-lg overflow-hidden border border-gray-200 hover:shadow-md transition-shadow">
                                <a href="{{ room.get_absolute_url }}" class="block">
                                    <div class="relative">
                                        {% if room.main_image %}
                                            <img src="{{ room.main_image.url }}" alt="{{ room.title }}" class="w-full h-40 object-cover">
                                        {% else %}
                                            <div class="w-full h-40 bg-gray-200 flex items-center justify-center">
                                                <i class="fas fa-home text-gray-400 text-2xl"></i>
                                            </div>
                                        {% endif %}
                                        
                                        <button class="absolute top-3 right-3 favorite-btn heart-icon" data-property-id="{{ room.id }}">
                                            <i class="far fa-heart text-white text-lg"></i>
                                        </button>
                                    </div>
                                    
                                    <div class="p-4">
                                        <h4 class="font-semibold text-gray-900 truncate mb-2">{{ room.title }}</h4>
                                        <p class="text-gray-600 text-sm mb-2">{{ room.room_type.name }}</p>
                                        <p class="text-gray-500 text-sm mb-3">{{ room.max_guests }} guests · {{ room.bedrooms }} bedrooms</p>
                                        
                                        <div class="flex items-center justify-between">
                                            <div>
                                                <span class="font-semibold text-gray-900">${{ room.price_per_night|floatformat:0 }}</span>
                                                <span class="text-gray-600 text-sm">/ night</span>
                                            </div>
                                            {% if room.average_rating > 0 %}
                                                <div class="flex items-center">
                                                    <i class="fas fa-star text-airbnb-red text-sm"></i>
                                                    <span class="text-sm text-gray-600 ml-1">{{ room.average_rating|floatformat:1 }}</span>
                                                </div>
                                            {% endif %}
                                        </div>
                                    </div>
                                </a>
                            </div>
                        {% endfor %}
                    </div>
                    
                    <div class="mt-6 text-center">
                        <a href="{% url 'search:results' %}?location={{ destination.name }}" 
                           class="inline-block bg-airbnb-red text-white px-6 py-3 rounded-lg font-medium hover:bg-red-600 transition-colors btn-primary">
                            View all properties in {{ destination.name }}
                        </a>
                    </div>
                </div>
            {% endif %}
        </div>

        <!-- Sidebar -->
        <div class="lg:col-span-1">
            <!-- Quick Facts -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6 sticky top-8">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Quick Facts</h3>
                
                <div class="space-y-4">
                    <div class="flex items-center">
                        <i class="fas fa-map-marker-alt text-airbnb-red w-5"></i>
                        <span class="ml-3 text-gray-700">{{ destination.country }}</span>
                    </div>
                    
                    <div class="flex items-center">
                        <i class="fas fa-home text-airbnb-red w-5"></i>
                        <span class="ml-3 text-gray-700">{{ destination.room_count }} properties available</span>
                    </div>
                    
                    {% if destination.latitude and destination.longitude %}
                        <div class="flex items-center">
                            <i class="fas fa-globe text-airbnb-red w-5"></i>
                            <span class="ml-3 text-gray-700">{{ destination.latitude|floatformat:2 }}°, {{ destination.longitude|floatformat:2 }}°</span>
                        </div>
                    {% endif %}
                </div>
                
                <!-- Search Button -->
                <div class="mt-6">
                    <a href="{% url 'search:results' %}?location={{ destination.name }}" 
                       class="w-full bg-airbnb-red text-white py-3 rounded-lg font-medium hover:bg-red-600 transition-colors btn-primary text-center block">
                        Search stays in {{ destination.name }}
                    </a>
                </div>
            </div>

            <!-- Interactive Map -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Location</h3>
                <div id="destination-map" class="w-full h-64 rounded-lg"></div>
            </div>

            <!-- Weather Info (Placeholder) -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Best Time to Visit</h3>
                <div class="space-y-3 text-sm text-gray-600">
                    <div class="flex justify-between">
                        <span>Spring (Mar-May)</span>
                        <span class="text-green-600">Excellent</span>
                    </div>
                    <div class="flex justify-between">
                        <span>Summer (Jun-Aug)</span>
                        <span class="text-green-600">Excellent</span>
                    </div>
                    <div class="flex justify-between">
                        <span>Fall (Sep-Nov)</span>
                        <span class="text-yellow-600">Good</span>
                    </div>
                    <div class="flex justify-between">
                        <span>Winter (Dec-Feb)</span>
                        <span class="text-yellow-600">Good</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Photo Gallery Component
function photoGallery() {
    return {
        galleryOpen: false,
        currentIndex: 0,
        images: [
            {% for image in destination.images.all %}
                {
                    url: '{{ image.image.url }}',
                    caption: '{{ image.caption|default:destination.name }}'
                }{% if not forloop.last %},{% endif %}
            {% endfor %}
        ],

        get currentImage() {
            return this.images[this.currentIndex] || { url: '', caption: '' };
        },

        openGallery(index) {
            this.currentIndex = index;
            this.galleryOpen = true;
            document.body.style.overflow = 'hidden';
        },

        closeGallery() {
            this.galleryOpen = false;
            document.body.style.overflow = 'auto';
        },

        nextImage() {
            this.currentIndex = (this.currentIndex + 1) % this.images.length;
        },

        previousImage() {
            this.currentIndex = this.currentIndex > 0 ? this.currentIndex - 1 : this.images.length - 1;
        }
    }
}

document.addEventListener('DOMContentLoaded', function() {
    // Keyboard navigation for gallery
    document.addEventListener('keydown', function(e) {
        const gallery = document.querySelector('[x-data*="photoGallery"]');
        if (gallery && gallery.__x && gallery.__x.$data.galleryOpen) {
            switch(e.key) {
                case 'Escape':
                    gallery.__x.$data.closeGallery();
                    break;
                case 'ArrowLeft':
                    gallery.__x.$data.previousImage();
                    break;
                case 'ArrowRight':
                    gallery.__x.$data.nextImage();
                    break;
            }
        }
    });
    // Initialize map for destination
    {% if destination.latitude and destination.longitude %}
        const lat = {{ destination.latitude }};
        const lng = {{ destination.longitude }};

        // Create map
        const map = L.map('destination-map').setView([lat, lng], 12);

        // Add tile layer
        L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
            attribution: '© <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors',
            maxZoom: 18
        }).addTo(map);

        // Custom marker icon
        const customIcon = L.divIcon({
            html: '<div class="bg-airbnb-red text-white rounded-full w-8 h-8 flex items-center justify-center shadow-lg"><i class="fas fa-map-marker-alt"></i></div>',
            className: 'custom-marker',
            iconSize: [32, 32],
            iconAnchor: [16, 32]
        });

        // Add marker
        const marker = L.marker([lat, lng], { icon: customIcon }).addTo(map);

        // Add popup
        marker.bindPopup(`
            <div class="text-center p-2">
                <h3 class="font-bold text-lg">{{ destination.name }}</h3>
                <p class="text-gray-600">{{ destination.country }}</p>
                <p class="text-sm mt-2">{{ destination.room_count }} properties available</p>
            </div>
        `);

        // Add circle to show general area
        L.circle([lat, lng], {
            color: '#FF5A5F',
            fillColor: '#FF5A5F',
            fillOpacity: 0.1,
            radius: 5000
        }).addTo(map);

        // Add points of interest (sample data)
        const pointsOfInterest = getPointsOfInterest('{{ destination.slug }}');
        pointsOfInterest.forEach(poi => {
            const poiIcon = L.divIcon({
                html: `<div class="bg-blue-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs shadow"><i class="${poi.icon}"></i></div>`,
                className: 'poi-marker',
                iconSize: [24, 24],
                iconAnchor: [12, 24]
            });

            L.marker([poi.lat, poi.lng], { icon: poiIcon })
                .addTo(map)
                .bindPopup(`<div class="text-center p-1"><strong>${poi.name}</strong><br><small>${poi.type}</small></div>`);
        });

    {% else %}
        // Show message if no coordinates
        document.getElementById('destination-map').innerHTML =
            '<div class="flex items-center justify-center h-full bg-gray-100 rounded-lg">' +
            '<div class="text-center text-gray-500">' +
            '<i class="fas fa-map-marker-alt text-3xl mb-2"></i>' +
            '<p>Map coordinates not available</p>' +
            '</div></div>';
    {% endif %}
});

// Points of interest data for each destination
function getPointsOfInterest(destinationSlug) {
    const poisData = {
        'paris-france': [
            { name: 'Louvre Museum', lat: 48.8606, lng: 2.3376, icon: 'fas fa-university', type: 'Museum' },
            { name: 'Notre-Dame', lat: 48.8530, lng: 2.3499, icon: 'fas fa-church', type: 'Cathedral' },
            { name: 'Arc de Triomphe', lat: 48.8738, lng: 2.2950, icon: 'fas fa-monument', type: 'Monument' },
            { name: 'Sacré-Cœur', lat: 48.8867, lng: 2.3431, icon: 'fas fa-church', type: 'Basilica' }
        ],
        'tokyo-japan': [
            { name: 'Sensoji Temple', lat: 35.7148, lng: 139.7967, icon: 'fas fa-torii-gate', type: 'Temple' },
            { name: 'Tokyo Tower', lat: 35.6586, lng: 139.7454, icon: 'fas fa-broadcast-tower', type: 'Tower' },
            { name: 'Imperial Palace', lat: 35.6852, lng: 139.7528, icon: 'fas fa-crown', type: 'Palace' },
            { name: 'Shibuya Crossing', lat: 35.6598, lng: 139.7006, icon: 'fas fa-traffic-light', type: 'Landmark' }
        ],
        'new-york-usa': [
            { name: 'Statue of Liberty', lat: 40.6892, lng: -74.0445, icon: 'fas fa-female', type: 'Monument' },
            { name: 'Central Park', lat: 40.7829, lng: -73.9654, icon: 'fas fa-tree', type: 'Park' },
            { name: 'Empire State Building', lat: 40.7484, lng: -73.9857, icon: 'fas fa-building', type: 'Skyscraper' },
            { name: 'Brooklyn Bridge', lat: 40.7061, lng: -73.9969, icon: 'fas fa-bridge', type: 'Bridge' }
        ],
        'rome-italy': [
            { name: 'Vatican City', lat: 41.9029, lng: 12.4534, icon: 'fas fa-church', type: 'Vatican' },
            { name: 'Trevi Fountain', lat: 41.9009, lng: 12.4833, icon: 'fas fa-fountain', type: 'Fountain' },
            { name: 'Roman Forum', lat: 41.8925, lng: 12.4853, icon: 'fas fa-columns', type: 'Ruins' },
            { name: 'Pantheon', lat: 41.8986, lng: 12.4769, icon: 'fas fa-university', type: 'Temple' }
        ],
        'barcelona-spain': [
            { name: 'Park Güell', lat: 41.4145, lng: 2.1527, icon: 'fas fa-palette', type: 'Park' },
            { name: 'Casa Batlló', lat: 41.3916, lng: 2.1649, icon: 'fas fa-home', type: 'Architecture' },
            { name: 'Gothic Quarter', lat: 41.3833, lng: 2.1761, icon: 'fas fa-chess-rook', type: 'District' },
            { name: 'Barceloneta Beach', lat: 41.3755, lng: 2.1909, icon: 'fas fa-umbrella-beach', type: 'Beach' }
        ],
        'london-uk': [
            { name: 'Tower of London', lat: 51.5081, lng: -0.0759, icon: 'fas fa-chess-rook', type: 'Castle' },
            { name: 'Buckingham Palace', lat: 51.5014, lng: -0.1419, icon: 'fas fa-crown', type: 'Palace' },
            { name: 'London Eye', lat: 51.5033, lng: -0.1196, icon: 'fas fa-circle', type: 'Attraction' },
            { name: 'Hyde Park', lat: 51.5073, lng: -0.1657, icon: 'fas fa-tree', type: 'Park' }
        ]
    };

    return poisData[destinationSlug] || [];
}
</script>

<!-- Favorites Script -->
<script src="{% static 'js/favorites.js' %}"></script>
<script>
// Initialize favorites manager
document.addEventListener('DOMContentLoaded', function() {
    new FavoritesManager();
});
</script>
{% endblock %}
