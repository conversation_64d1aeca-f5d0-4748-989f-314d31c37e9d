"""
ASGI config for airbnb_clone project in production.

It exposes the ASGI callable as a module-level variable named ``application``.

For more information on this file, see
https://docs.djangoproject.com/en/5.2/howto/deployment/asgi/
"""

import os
from django.core.asgi import get_asgi_application

# Set the settings module for production
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'airbnb_clone.settings_production')

application = get_asgi_application()
