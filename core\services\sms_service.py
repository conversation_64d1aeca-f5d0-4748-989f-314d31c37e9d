"""
Service SMS gratuit comme alternative à WhatsApp
Utilise des APIs SMS gratuites
"""

import requests
import logging
from django.conf import settings

logger = logging.getLogger(__name__)


class FreeSMSService:
    """Service SMS utilisant des APIs gratuites"""
    
    def __init__(self):
        # TextBelt API (gratuit avec limite)
        self.textbelt_key = getattr(settings, 'TEXTBELT_API_KEY', 'textbelt')  # 'textbelt' pour 1 SMS gratuit par jour
        
    def send_verification_code(self, phone_number, verification_code):
        """
        Envoie un code de vérification via SMS
        
        Args:
            phone_number (str): Numéro de téléphone
            verification_code (str): Code à 6 chiffres
            
        Returns:
            dict: Résultat de l'envoi
        """
        
        # Nettoyer le numéro
        clean_phone = self._clean_phone_number(phone_number)
        
        # Message SMS
        message = f"Your Nestria verification code is: {verification_code}. This code will expire in 5 minutes."
        
        try:
            # TextBelt API (1 SMS gratuit par jour)
            response = requests.post('https://textbelt.com/text', {
                'phone': clean_phone,
                'message': message,
                'key': self.textbelt_key,
            })
            
            result = response.json()
            
            if result.get('success'):
                logger.info(f"✅ SMS sent successfully to {clean_phone}")
                logger.info(f"📱 TextID: {result.get('textId')}")
                
                return {
                    'success': True,
                    'message_id': result.get('textId'),
                    'status': 'sent',
                    'quota_remaining': result.get('quotaRemaining')
                }
            else:
                logger.error(f"❌ SMS error for {clean_phone}: {result.get('error')}")
                return {
                    'success': False,
                    'error': result.get('error', 'Unknown error')
                }
                
        except Exception as e:
            logger.error(f"❌ Unexpected error sending SMS to {clean_phone}: {str(e)}")
            return {
                'success': False,
                'error': 'Unexpected error occurred'
            }
    
    def _clean_phone_number(self, phone_number):
        """Nettoie le numéro de téléphone"""
        if not phone_number:
            return None
            
        clean = phone_number.strip().replace(' ', '').replace('-', '').replace('(', '').replace(')', '')
        
        # TextBelt accepte les numéros avec ou sans +
        return clean


# Instance globale
sms_service = FreeSMSService()


def send_sms_verification(phone_number, verification_code):
    """
    Fonction helper pour envoyer via SMS
    """
    result = sms_service.send_verification_code(phone_number, verification_code)
    return result.get('success', False)
