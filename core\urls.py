from django.urls import path
from django.contrib.auth import views as auth_views
from . import views
from .social_auth_views import GoogleAuthView, FacebookAuthView, AppleAuthView

app_name = 'core'

urlpatterns = [
    path('', views.HomeView.as_view(), name='home'),
    path('login/', views.PhoneSignUpView.as_view(), name='login'),
    path('logout/', views.logout_view, name='logout'),
    # Signup redirects to phone signup
    path('signup/', views.PhoneSignUpView.as_view(), name='signup'),
    path('email-login/', views.EmailLoginView.as_view(), name='email_login'),
    path('profile/', views.ProfileView.as_view(), name='profile'),
    path('favorites/', views.FavoritesView.as_view(), name='favorites'),
    path('your-trips/', views.YourTripsView.as_view(), name='your_trips'),
    path('settings/', views.SettingsView.as_view(), name='settings'),
    path('change-password/', views.change_password_view, name='change_password'),
    path('delete-account/', views.delete_account_view, name='delete_account'),
    path('contact-host/<int:room_id>/', views.contact_host_view, name='contact_host'),
    path('toggle-favorite/<int:room_id>/', views.toggle_favorite_view, name='toggle_favorite'),
    path('set-language/', views.set_language_view, name='set_language'),
    path('send-test-email/', views.send_test_email_view, name='send_test_email'),
    path('user-favorites/', views.user_favorites_view, name='user_favorites'),

    # Phone-based registration process
    path('phone-signup/', views.PhoneSignUpView.as_view(), name='phone_signup'),
    path('phone-verification/', views.PhoneVerificationView.as_view(), name='phone_verification'),
    path('contact-info/', views.ContactInfoView.as_view(), name='contact_info'),
    path('finish-signup/', views.FinishSignupView.as_view(), name='finish_signup'),
    path('community-commitment/', views.CommunityCommitmentView.as_view(), name='community_commitment'),

    # Social Authentication Pages
    path('auth/google/', views.GoogleSelectView.as_view(), name='google_auth'),
    path('auth/facebook/', views.FacebookLoginView.as_view(), name='facebook_auth'),
    path('auth/apple/', views.AppleLoginView.as_view(), name='apple_auth'),
    path('auth/email/', views.EmailAuthView.as_view(), name='email_auth'),

    # AJAX endpoints
    path('resend-verification-code/', views.resend_verification_code, name='resend_verification_code'),
    path('change-phone-number/', views.change_phone_number, name='change_phone_number'),
    path('send-email-verification/', views.send_email_verification, name='send_email_verification'),
    path('create-wishlist/', views.create_wishlist, name='create_wishlist'),

    # Onboarding
    path('onboarding/', views.OnboardingView.as_view(), name='onboarding'),
    path('onboarding/step1/', views.OnboardingStep1View.as_view(), name='onboarding_step1'),

    # Demo
    path('signup-flow-demo/', views.SignupFlowDemoView.as_view(), name='signup_flow_demo'),

    # Resend verification code
    path('resend-verification-code/', views.resend_verification_code, name='resend_verification_code'),

    # Change phone number
    path('change-phone-number/', views.change_phone_number, name='change_phone_number'),

    # Login/Signup simple
    path('login/', views.LoginSignupView.as_view(), name='login_signup'),
    path('modern-login/', views.ModernLoginView.as_view(), name='modern_login'),
    path('forgot-password/', views.ForgotPasswordView.as_view(), name='forgot_password'),
    path('login-user/', views.LoginSignupView.as_view(), name='login_user'),
    path('signup-user/', views.LoginSignupView.as_view(), name='signup_user'),
    path('logout/', views.logout_user, name='logout'),
    path('onboarding/step2/', views.OnboardingStep2View.as_view(), name='onboarding_step2'),
    path('onboarding/step3/', views.OnboardingStep3View.as_view(), name='onboarding_step3'),
    # Demo and test pages - DISABLED
    # path('signup-demo/', views.SignupDemoView.as_view(), name='signup_demo'),
    # path('test-signup/', views.TestSignupView.as_view(), name='test_signup'),
    # path('clear-session/', views.clear_session, name='clear_session'),

    # Social Authentication - Demo
    path('auth/google/', views.GoogleAuthView.as_view(), name='google_auth'),
    path('auth/facebook/', views.FacebookAuthView.as_view(), name='facebook_auth'),
    path('auth/apple/', views.AppleAuthView.as_view(), name='apple_auth'),

    # Experience categories
    path('experiences/food-drink/', views.FoodDrinkExperiencesView.as_view(), name='food_drink_experiences'),
    path('experiences/adventure/', views.AdventureExperiencesView.as_view(), name='adventure_experiences'),
    path('experiences/arts-culture/', views.ArtsCultureExperiencesView.as_view(), name='arts_culture_experiences'),
    path('experiences/photography/', views.PhotographyExperiencesView.as_view(), name='photography_experiences'),
    path('experiences/', views.ExperiencesView.as_view(), name='experiences'),
    path('experiences/browse/', views.BrowseExperiencesView.as_view(), name='browse_experiences'),
    path('experiences/host/', views.HostExperienceView.as_view(), name='host_experience'),
    path('online/', views.OnlineView.as_view(), name='online'),
    path('online/browse/', views.BrowseOnlineView.as_view(), name='browse_online'),
    path('online/host/', views.HostOnlineView.as_view(), name='host_online'),
]
