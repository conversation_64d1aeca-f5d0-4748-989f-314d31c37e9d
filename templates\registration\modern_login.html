{% extends 'base.html' %}
{% load static %}

{% block title %}Log in - Nestria{% endblock %}

{% block extra_css %}
<style>
    .login-container {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
    }
    
    .login-card {
        backdrop-filter: blur(10px);
        background: rgba(255, 255, 255, 0.95);
        border: 1px solid rgba(255, 255, 255, 0.2);
    }
    
    .social-btn {
        transition: all 0.3s ease;
        border: 2px solid #e5e7eb;
    }
    
    .social-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        border-color: #3b82f6;
    }
    
    .input-field {
        transition: all 0.3s ease;
        border: 2px solid #e5e7eb;
    }
    
    .input-field:focus {
        border-color: #3b82f6;
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    }
    
    .nestria-logo {
        animation: float 3s ease-in-out infinite;
    }
    
    @keyframes float {
        0%, 100% { transform: translateY(0px); }
        50% { transform: translateY(-10px); }
    }
    
    .form-toggle {
        background: #f8fafc;
        border-radius: 12px;
        padding: 4px;
    }
    
    .toggle-btn {
        transition: all 0.3s ease;
        border-radius: 8px;
        padding: 8px 16px;
        font-weight: 500;
    }
    
    .toggle-btn.active {
        background: white;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        color: #1f2937;
    }
</style>
{% endblock %}

{% block content %}
<div class="login-container flex items-center justify-center p-4">
    <div class="w-full max-w-md">
        <!-- Logo et titre -->
        <div class="text-center mb-8">
            <div class="nestria-logo mb-4">
                <img src="{% static 'images/nestria-logo-transparent.png' %}" 
                     alt="Nestria" 
                     class="w-20 h-20 mx-auto">
            </div>
            <h1 class="text-3xl font-bold text-white mb-2">Welcome to Nestria</h1>
            <p class="text-white/80">Travel to survive and discover amazing places</p>
        </div>

        <!-- Carte de connexion -->
        <div class="login-card rounded-2xl p-8 shadow-2xl" x-data="authForm()">
            <!-- Toggle Login/Signup -->
            <div class="form-toggle flex mb-6">
                <button @click="isLogin = true" 
                        :class="isLogin ? 'toggle-btn active' : 'toggle-btn'"
                        class="flex-1 text-center">
                    Log in
                </button>
                <a href="{% url 'core:signup' %}"
                   class="toggle-btn flex-1 text-center inline-block">
                    Sign up
                </a>
            </div>

            <!-- Boutons OAuth -->
            <div class="space-y-3 mb-6">
                <!-- Google -->
                <a href="{% url 'core:google_auth' %}"
                   class="social-btn w-full flex items-center justify-center px-4 py-3 rounded-xl hover:bg-gray-50">
                    <img src="https://developers.google.com/identity/images/g-logo.png"
                         alt="Google" class="w-5 h-5 mr-3">
                    <span class="font-medium text-gray-700">Continue with Google</span>
                </a>

                <!-- Facebook -->
                <a href="{% url 'core:facebook_auth' %}"
                   class="social-btn w-full flex items-center justify-center px-4 py-3 rounded-xl hover:bg-gray-50">
                    <i class="fab fa-facebook-f text-blue-600 mr-3"></i>
                    <span class="font-medium text-gray-700">Continue with Facebook</span>
                </a>

                <!-- Apple -->
                <a href="{% url 'core:apple_auth' %}"
                   class="social-btn w-full flex items-center justify-center px-4 py-3 rounded-xl hover:bg-gray-50">
                    <i class="fab fa-apple text-xl mr-3"></i>
                    <span class="font-medium text-gray-700">Continue with Apple</span>
                </a>
            </div>

            <!-- Séparateur -->
            <div class="flex items-center mb-6">
                <div class="flex-1 border-t border-gray-300"></div>
                <span class="px-4 text-gray-500 text-sm">or</span>
                <div class="flex-1 border-t border-gray-300"></div>
            </div>

            <!-- Formulaire Email/Password -->
            <form method="post" x-show="isLogin" x-transition>
                {% csrf_token %}
                <div class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Email</label>
                        <input type="email" 
                               name="email" 
                               required
                               class="input-field w-full px-4 py-3 rounded-xl focus:outline-none"
                               placeholder="Enter your email">
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Password</label>
                        <input type="password" 
                               name="password" 
                               required
                               class="input-field w-full px-4 py-3 rounded-xl focus:outline-none"
                               placeholder="Enter your password">
                    </div>
                    
                    <button type="submit" 
                            class="w-full bg-gradient-to-r from-blue-500 to-purple-600 text-white py-3 rounded-xl font-medium hover:from-blue-600 hover:to-purple-700 transition-all duration-200 transform hover:scale-105">
                        Log in
                    </button>
                </div>
            </form>



            <!-- Messages d'erreur/succès -->
            {% if messages %}
                <div class="mt-4">
                    {% for message in messages %}
                        <div class="p-3 rounded-lg {% if message.tags == 'error' %}bg-red-100 text-red-700{% else %}bg-green-100 text-green-700{% endif %}">
                            {{ message }}
                        </div>
                    {% endfor %}
                </div>
            {% endif %}

            <!-- Lien mot de passe oublié -->
            <div class="text-center mt-6" x-show="isLogin">
                <a href="{% url 'core:forgot_password' %}" class="text-sm text-blue-600 hover:text-blue-800">Forgot your password?</a>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function authForm() {
    return {
        isLogin: true
    }
}
</script>
{% endblock %}
