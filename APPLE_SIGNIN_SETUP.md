# Configuration Apple Sign In pour Nestria

## 🎯 Objectif
Configurer Apple Sign In pour permettre aux utilisateurs de se connecter avec leur Apple ID et récupérer leurs informations de base.

## 📋 Étapes de Configuration

### 1. Prérequis Apple Developer

⚠️ **Attention** : Apple Sign In nécessite :
- Un compte Apple Developer (99$/an)
- Un App ID configuré
- Des certificats et clés privées

### 2. Créer un App ID

1. Aller sur [Apple Developer Portal](https://developer.apple.com/)
2. **Certificates, Identifiers & Profiles** → **Identifiers**
3. Cliquer sur **+** → **App IDs**
4. **Bundle ID** : `com.nestria.web`
5. **Capabilities** : Cocher **Sign In with Apple**

### 3. Créer un Service ID

1. **Identifiers** → **+** → **Services IDs**
2. **Identifier** : `com.nestria.web.signin`
3. **Description** : `Nestria Web Sign In`
4. Cocher **Sign In with Apple**
5. **Configure** :
   - **Primary App ID** : `com.nestria.web`
   - **Domains and Subdomains** : `localhost`
   - **Return URLs** : 
     ```
     http://localhost:8000/accounts/apple/login/callback/
     ```

### 4. Créer une Clé Privée

1. **Keys** → **+**
2. **Key Name** : `Nestria Apple Sign In Key`
3. Cocher **Sign In with Apple**
4. **Configure** → Sélectionner votre App ID
5. **Continue** → **Register**
6. **Télécharger** la clé `.p8` (⚠️ Une seule fois !)
7. Noter le **Key ID** (ex: `ABC123DEF4`)

### 5. Récupérer les Informations

Vous aurez besoin de :
- **Team ID** : Dans Account → Membership (ex: `DEF456GHI7`)
- **Service ID** : `com.nestria.web.signin`
- **Key ID** : `ABC123DEF4`
- **Private Key** : Contenu du fichier `.p8`

## 🔧 Configuration Django

### Mettre à jour settings.py

```python
SOCIALACCOUNT_PROVIDERS = {
    'apple': {
        'APP': {
            'client_id': 'com.nestria.web.signin',
            'secret': 'DEF456GHI7',  # Team ID
            'key': 'ABC123DEF4',     # Key ID
            'certificate_key': '''-----BEGIN PRIVATE KEY-----
MIGTAgEAMBMGByqGSM49AgEGCCqGSM49AwEHBHkwdwIBAQQg...
-----END PRIVATE KEY-----''',
        }
    }
}
```

### Script de Configuration

```bash
python configure_apple_signin.py
```

## 🧪 Test

1. `http://127.0.0.1:8000/signup/`
2. **Continue with Apple**
3. Saisir Apple ID et mot de passe
4. Autoriser l'application
5. Redirection vers `finish-signup`

## 📊 Données Récupérées

Apple Sign In est très restrictif :
- `email` : Email (peut être masqué par Apple)
- `first_name` : Prénom (seulement la première fois)
- `last_name` : Nom (seulement la première fois)
- `user_id` : Identifiant unique Apple

## ⚠️ Limitations Apple

- **Données limitées** : Apple privilégie la confidentialité
- **Email masqué** : Apple peut créer un email de relais
- **Informations une seule fois** : Nom/prénom seulement au premier login
- **HTTPS requis** en production
- **Domaines vérifiés** requis en production

## 🚨 Alternative Recommandée

Pour le développement local, Apple Sign In est complexe. Recommandations :
1. **Google OAuth** : Plus simple, plus de données
2. **Facebook Login** : Bon compromis
3. **Apple Sign In** : Pour la production iOS/macOS

## 🔧 Configuration Simplifiée (Développement)

Pour le développement, vous pouvez simuler Apple Sign In avec des données fictives en modifiant la vue `AppleAuthView` dans `core/views.py`.
