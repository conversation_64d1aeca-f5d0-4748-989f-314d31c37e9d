# Configuration de l'Authentification Sociale - Nestria

Ce guide explique comment configurer l'authentification sociale avec Google, Facebook et Apple pour votre application Nestria.

## 🚀 Fonctionnalités Implémentées

- ✅ Pages de login/signup avec le nouveau logo Nestria
- ✅ Boutons d'authentification sociale fonctionnels
- ✅ Intégration Google OAuth
- ✅ Intégration Facebook Login
- ✅ Intégration Apple Sign In
- ✅ Authentification par email
- ✅ Logo Nestria dans les PDFs
- ✅ Logo Nestria dans les emails

## 📋 Configuration Requise

### 1. Variables d'Environnement

Ajoutez ces variables à votre fichier `.env` ou `settings.py` :

```python
# Google OAuth
GOOGLE_OAUTH_CLIENT_ID = 'votre-google-client-id'
GOOGLE_OAUTH_CLIENT_SECRET = 'votre-google-client-secret'

# Facebook Login
FACEBOOK_APP_ID = 'votre-facebook-app-id'
FACEBOOK_APP_SECRET = 'votre-facebook-app-secret'

# Apple Sign In
APPLE_CLIENT_ID = 'votre-apple-client-id'
APPLE_TEAM_ID = 'votre-apple-team-id'
APPLE_KEY_ID = 'votre-apple-key-id'
APPLE_PRIVATE_KEY = 'votre-apple-private-key'
```

### 2. Configuration Google OAuth

1. Allez sur [Google Cloud Console](https://console.cloud.google.com/)
2. Créez un nouveau projet ou sélectionnez un projet existant
3. Activez l'API Google+ et l'API OAuth2
4. Créez des identifiants OAuth 2.0
5. Ajoutez vos domaines autorisés :
   - `http://localhost:8000` (développement)
   - `https://votre-domaine.com` (production)
6. Ajoutez les URIs de redirection :
   - `http://localhost:8000/auth/google/`
   - `https://votre-domaine.com/auth/google/`

### 3. Configuration Facebook Login

1. Allez sur [Facebook Developers](https://developers.facebook.com/)
2. Créez une nouvelle application
3. Ajoutez le produit "Facebook Login"
4. Configurez les URIs de redirection valides :
   - `http://localhost:8000/auth/facebook/`
   - `https://votre-domaine.com/auth/facebook/`
5. Ajoutez vos domaines dans les paramètres de l'application

### 4. Configuration Apple Sign In

1. Allez sur [Apple Developer](https://developer.apple.com/)
2. Créez un App ID avec Sign In with Apple activé
3. Créez un Service ID pour votre application web
4. Configurez les domaines et sous-domaines autorisés
5. Créez une clé privée pour Sign In with Apple
6. Téléchargez le fichier de clé privée

## 🎨 Logo Nestria

Le nouveau logo Nestria a été intégré dans :

### Pages d'Authentification
- Page de login (`/login/`)
- Page de signup (`/signup/`)
- Page de login par email (`/email-login/`)
- Page de signup par email (`/email-signup/`)

### Emails
- Template de base des emails (`templates/emails/base_email.html`)
- Email de bienvenue
- Email de confirmation de réservation
- Tous les autres emails

### PDFs
- Confirmations de réservation
- Factures
- Tous les documents générés

## 🔧 Utilisation

### Accès aux Pages

- **Login principal** : `/login/`
- **Signup principal** : `/signup/`
- **Login par email** : `/email-login/`
- **Signup par email** : `/email-signup/`

### Boutons Sociaux

Tous les boutons d'authentification sociale sont fonctionnels :

- **Google** : Utilise Google OAuth 2.0
- **Apple** : Utilise Apple Sign In
- **Facebook** : Utilise Facebook Login SDK
- **Email** : Redirection vers les formulaires email

### APIs d'Authentification

- `POST /auth/google/` - Authentification Google
- `POST /auth/facebook/` - Authentification Facebook  
- `POST /auth/apple/` - Authentification Apple

## 📱 Responsive Design

Toutes les pages sont optimisées pour :
- Desktop
- Tablette
- Mobile

## 🎯 Prochaines Étapes

1. **Remplacer le logo** : Remplacez le fichier `static/images/nestria-logo-new.png` par votre logo réel
2. **Configurer les providers** : Ajoutez vos clés API dans les variables d'environnement
3. **Tester l'authentification** : Testez chaque provider d'authentification
4. **Personnaliser les styles** : Modifiez `static/css/social-auth.css` si nécessaire

## 🐛 Dépannage

### Erreurs Communes

1. **Logo non affiché** : Vérifiez que le fichier `static/images/nestria-logo-new.png` existe
2. **Erreur Google OAuth** : Vérifiez vos URIs de redirection
3. **Erreur Facebook** : Vérifiez que votre domaine est autorisé
4. **Erreur Apple** : Vérifiez votre configuration de clé privée

### Logs

Les erreurs d'authentification sont loggées dans la console Django. Vérifiez les logs pour diagnostiquer les problèmes.

## 📞 Support

Pour toute question ou problème, consultez la documentation Django ou contactez l'équipe de développement.

---

**Note** : N'oubliez pas de remplacer le fichier logo placeholder par votre logo réel !
