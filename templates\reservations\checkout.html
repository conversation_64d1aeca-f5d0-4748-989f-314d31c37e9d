{% extends 'base.html' %}
{% load static %}

{% block title %}Checkout - {{ room.title }} - Airbnb Clone{% endblock %}

{% block extra_css %}
<style>
.payment-card {
    transition: all 0.3s ease;
    border: 2px solid transparent;
}
.payment-card:hover {
    border-color: #FF5A5F;
    box-shadow: 0 4px 12px rgba(255, 90, 95, 0.15);
}
.payment-card.selected {
    border-color: #FF5A5F;
    background-color: #fef7f7;
}
.card-icons {
    display: flex;
    gap: 8px;
    align-items: center;
}
.card-icon {
    width: 32px;
    height: 20px;
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
}
.visa { background-image: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCA0MCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjQwIiBoZWlnaHQ9IjI0IiByeD0iNCIgZmlsbD0iIzAwNTFBNSIvPgo8cGF0aCBkPSJNMTYuNzUgMTcuNUgxNC4yNUwxNS43NSA2LjVIMTguMjVMMTYuNzUgMTcuNVoiIGZpbGw9IndoaXRlIi8+CjxwYXRoIGQ9Ik0yNS41IDYuNUMyNC43NSA2LjI1IDIzLjUgNiAyMiA2QzE5LjI1IDYgMTcuMjUgNy41IDE3LjI1IDkuNzVDMTcuMjUgMTEuNSAxOC43NSAxMi41IDIwIDEzQzIxLjI1IDEzLjUgMjEuNzUgMTQgMjEuNzUgMTQuNzVDMjEuNzUgMTUuNzUgMjAuNSAxNi4yNSAxOS4yNSAxNi4yNUMxNy43NSAxNi4yNSAxNi43NSAxNS43NSAxNiAxNS4yNUwxNS4yNSAxNy43NUMxNiAxOC4yNSAxNy4yNSAxOC41IDE4LjUgMTguNUMyMS41IDE4LjUgMjMuNSAxNyAyMy41IDE0LjVDMjMuNSAxMi43NSAyMiAxMS43NSAyMC43NSAxMS4yNUMxOS43NSAxMC43NSAxOS4yNSAxMC4yNSAxOS4yNSA5LjVDMTkuMjUgOC43NSAyMCA4LjI1IDIxIDguMjVDMjIgOC4yNSAyMi43NSA4LjUgMjMuMjUgOC43NUwyNCAxMC4yNUwyNS41IDYuNVoiIGZpbGw9IndoaXRlIi8+Cjwvc3ZnPgo='); }
.mastercard { background-image: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCA0MCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjQwIiBoZWlnaHQ9IjI0IiByeD0iNCIgZmlsbD0iI0VCMDAxQiIvPgo8Y2lyY2xlIGN4PSIxNSIgY3k9IjEyIiByPSI3IiBmaWxsPSIjRkY1RjAwIi8+CjxjaXJjbGUgY3g9IjI1IiBjeT0iMTIiIHI9IjciIGZpbGw9IiNGRkY1RjAiLz4KPC9zdmc+Cg=='); }
.amex { background-image: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCA0MCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjQwIiBoZWlnaHQ9IjI0IiByeD0iNCIgZmlsbD0iIzAwNkZDRiIvPgo8cGF0aCBkPSJNMTAgOEgxNEwxNiAxMkwxOCA4SDIyTDI0IDEyTDI2IDhIMzBMMjggMTZIMjRMMjIgMTJMMjAgMTZIMTZMMTQgMTJMMTIgMTZIOEwxMCA4WiIgZmlsbD0id2hpdGUiLz4KPC9zdmc+Cg=='); }
.discover { background-image: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCA0MCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjQwIiBoZWlnaHQ9IjI0IiByeD0iNCIgZmlsbD0iI0ZGNjAwMCIvPgo8cGF0aCBkPSJNOCAxMEgxMlYxNEg4VjEwWk0xNiAxMEgyMFYxNEgxNlYxMFpNMjQgMTBIMjhWMTRIMjRWMTBaIiBmaWxsPSJ3aGl0ZSIvPgo8L3N2Zz4K'); }
.secure-badge {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    display: inline-flex;
    align-items: center;
    gap: 6px;
}
</style>
{% endblock %}

{% block content %}
<div class="bg-gray-50 min-h-screen">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Header -->
        <div class="text-center mb-8">
            <h1 class="text-3xl font-bold text-gray-900 mb-2">Confirm and pay</h1>
            <p class="text-gray-600">You're just one step away from your amazing stay!</p>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- Left Column - Booking Form -->
            <div class="lg:col-span-2 space-y-6">
                <form method="POST" x-data="checkoutForm()" class="space-y-6">
                    {% csrf_token %}

                    <!-- Trip Details -->
                    <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6 min-h-[200px]">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">
                            <i class="fas fa-calendar-alt text-airbnb-red mr-2"></i>
                            Your trip
                        </h3>

                        <div class="space-y-4">
                            <div class="flex justify-between items-center p-4 bg-gray-50 rounded-lg border border-gray-100 hover:border-airbnb-red transition-colors">
                                <div class="flex-1">
                                    <div class="font-medium text-gray-900 mb-1 flex items-center">
                                        <i class="fas fa-calendar text-airbnb-red mr-2"></i>
                                        Dates
                                    </div>
                                    <div class="text-sm text-gray-600" x-text="dateRange"></div>
                                </div>
                                <button type="button"
                                        onclick="editDates()"
                                        class="text-airbnb-red hover:text-red-600 text-sm font-medium px-4 py-2 rounded-md hover:bg-red-50 transition-all border border-airbnb-red hover:border-red-600">
                                    <i class="fas fa-edit mr-1"></i>Edit
                                </button>
                            </div>

                            <div class="flex justify-between items-center p-4 bg-gray-50 rounded-lg border border-gray-100 hover:border-airbnb-red transition-colors">
                                <div class="flex-1">
                                    <div class="font-medium text-gray-900 mb-1 flex items-center">
                                        <i class="fas fa-users text-airbnb-red mr-2"></i>
                                        Guests
                                    </div>
                                    <div class="text-sm text-gray-600" x-text="guestDisplay"></div>
                                </div>
                                <button type="button"
                                        onclick="editGuests()"
                                        class="text-airbnb-red hover:text-red-600 text-sm font-medium px-4 py-2 rounded-md hover:bg-red-50 transition-all border border-airbnb-red hover:border-red-600">
                                    <i class="fas fa-edit mr-1"></i>Edit
                                </button>
                            </div>
                        </div>

                        <!-- Hidden form fields -->
                        <input type="hidden" name="check_in" :value="checkIn">
                        <input type="hidden" name="check_out" :value="checkOut">
                        <input type="hidden" name="adults" :value="adults">
                        <input type="hidden" name="children" :value="children">
                        <input type="hidden" name="infants" :value="infants">
                    </div>

                    <!-- Guest Information -->
                    <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                        <div class="flex items-center justify-between mb-6">
                            <h2 class="text-xl font-semibold text-gray-900 flex items-center">
                                <i class="fas fa-user text-airbnb-red mr-3"></i>
                                Guest information
                            </h2>
                        </div>

                        <div class="space-y-4">
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">
                                        <i class="fas fa-user-circle mr-1"></i>First name
                                    </label>
                                    <input type="text" name="guest_first_name" value="{{ user.first_name }}" required
                                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-airbnb-red focus:border-airbnb-red transition-all">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">
                                        <i class="fas fa-user-circle mr-1"></i>Last name
                                    </label>
                                    <input type="text" name="guest_last_name" value="{{ user.last_name }}" required
                                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-airbnb-red focus:border-airbnb-red transition-all">
                                </div>
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">
                                    <i class="fas fa-envelope mr-1"></i>Email address
                                </label>
                                <input type="email" name="guest_email" value="{{ user.email }}" required
                                       class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-airbnb-red focus:border-airbnb-red transition-all">
                                <p class="text-xs text-gray-500 mt-1">We'll send your confirmation here</p>
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">
                                    <i class="fas fa-phone mr-1"></i>Phone number
                                </label>
                                <input type="tel" name="guest_phone" placeholder="+****************"
                                       class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-airbnb-red focus:border-airbnb-red transition-all">
                                <p class="text-xs text-gray-500 mt-1">For booking updates and host communication</p>
                            </div>
                        </div>
                    </div>

                    <!-- Special Requests -->
                    <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                        <div class="flex items-center justify-between mb-6">
                            <h2 class="text-xl font-semibold text-gray-900 flex items-center">
                                <i class="fas fa-comment-dots text-airbnb-red mr-3"></i>
                                Special requests
                            </h2>
                        </div>
                        <textarea name="special_requests" rows="4" placeholder="Any special requests for your stay? (Optional)"
                                  class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-airbnb-red focus:border-airbnb-red transition-all resize-none"></textarea>
                        <p class="text-sm text-gray-500 mt-2">
                            <i class="fas fa-info-circle mr-1"></i>
                            Special requests can't be guaranteed, but the host will do their best to accommodate them.
                        </p>
                    </div>

                    <!-- Payment Method -->
                    <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6" x-data="{ selectedPayment: 'card' }">
                        <div class="flex items-center justify-between mb-6">
                            <h2 class="text-xl font-semibold text-gray-900 flex items-center">
                                <i class="fas fa-credit-card text-airbnb-red mr-3"></i>
                                Payment method
                            </h2>
                            <div class="secure-badge">
                                <i class="fas fa-shield-alt"></i>
                                Secure Payment
                            </div>
                        </div>

                        <div class="space-y-4">
                            <!-- Credit Card Option -->
                            <div class="payment-card rounded-lg p-4 cursor-pointer"
                                 :class="selectedPayment === 'card' ? 'selected' : ''"
                                 @click="selectedPayment = 'card'">
                                <div class="flex items-center justify-between mb-4">
                                    <div class="flex items-center">
                                        <input type="radio" name="payment_method" value="card" x-model="selectedPayment" class="text-airbnb-red focus:ring-airbnb-red">
                                        <label class="ml-3 font-medium text-gray-900">Credit or debit card</label>
                                    </div>
                                    <div class="card-icons">
                                        <div class="card-icon visa"></div>
                                        <div class="card-icon mastercard"></div>
                                        <div class="card-icon amex"></div>
                                        <div class="card-icon discover"></div>
                                    </div>
                                </div>

                                <div x-show="selectedPayment === 'card'" x-transition class="space-y-4">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">Card number</label>
                                        <input type="text" placeholder="1234 5678 9012 3456" maxlength="19"
                                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-airbnb-red focus:border-airbnb-red transition-all"
                                               x-mask="9999 9999 9999 9999">
                                    </div>
                                    <div class="grid grid-cols-2 gap-4">
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 mb-2">Expiry date</label>
                                            <input type="text" placeholder="MM/YY" maxlength="5"
                                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-airbnb-red focus:border-airbnb-red transition-all"
                                                   x-mask="99/99">
                                        </div>
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 mb-2">CVV</label>
                                            <input type="text" placeholder="123" maxlength="4"
                                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-airbnb-red focus:border-airbnb-red transition-all"
                                                   x-mask="9999">
                                        </div>
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">Cardholder name</label>
                                        <input type="text" placeholder="John Doe"
                                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-airbnb-red focus:border-airbnb-red transition-all">
                                    </div>
                                </div>
                            </div>

                            <!-- PayPal Option -->
                            <div class="payment-card rounded-lg p-4 cursor-pointer"
                                 :class="selectedPayment === 'paypal' ? 'selected' : ''"
                                 @click="selectedPayment = 'paypal'">
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center">
                                        <input type="radio" name="payment_method" value="paypal" x-model="selectedPayment" class="text-airbnb-red focus:ring-airbnb-red">
                                        <label class="ml-3 font-medium text-gray-900">PayPal</label>
                                    </div>
                                    <div class="flex items-center">
                                        <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCA0MCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjQwIiBoZWlnaHQ9IjI0IiByeD0iNCIgZmlsbD0iIzAwMzA4NyIvPgo8cGF0aCBkPSJNMTAgOEgxNEMxNiA4IDE3IDkgMTcgMTFDMTcgMTMgMTYgMTQgMTQgMTRIMTJMMTEgMTZIOUwxMCA4WiIgZmlsbD0iIzAwOUNERSIvPgo8cGF0aCBkPSJNMTggOEgyMkMyNCA4IDI1IDkgMjUgMTFDMjUgMTMgMjQgMTQgMjIgMTRIMjBMMTkgMTZIMTdMMTggOFoiIGZpbGw9IiMwMDlDREUiLz4KPC9zdmc+Cg==" alt="PayPal" class="h-6">
                                    </div>
                                </div>
                                <div x-show="selectedPayment === 'paypal'" x-transition class="mt-3">
                                    <p class="text-sm text-gray-600">You'll be redirected to PayPal to complete your payment securely.</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Terms and Conditions -->
                    <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6 min-h-[200px]">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">
                            <i class="fas fa-file-contract text-airbnb-red mr-2"></i>
                            Terms and conditions
                        </h3>
                        <div class="space-y-4">
                            <div class="bg-red-50 border-2 border-red-200 rounded-lg p-4">
                                <label class="flex items-start cursor-pointer group">
                                    <input type="checkbox"
                                           required
                                           id="termsCheckbox"
                                           class="mt-1 rounded border-gray-300 text-airbnb-red focus:ring-airbnb-red focus:ring-2 w-5 h-5">
                                    <span class="ml-3 text-sm text-gray-700 group-hover:text-gray-900 transition-colors">
                                        <strong class="text-red-700">Required:</strong> I agree to the
                                        <a href="#" class="text-airbnb-red hover:text-red-600 underline font-medium">Terms of Service</a>,
                                        <a href="#" class="text-airbnb-red hover:text-red-600 underline font-medium">Privacy Policy</a>, and
                                        <a href="#" class="text-airbnb-red hover:text-red-600 underline font-medium">Cancellation Policy</a>
                                    </span>
                                </label>
                            </div>

                            <label class="flex items-start cursor-pointer group">
                                <input type="checkbox" class="mt-1 rounded border-gray-300 text-airbnb-red focus:ring-airbnb-red w-5 h-5">
                                <span class="ml-3 text-sm text-gray-700 group-hover:text-gray-900 transition-colors">
                                    <i class="fas fa-envelope text-blue-500 mr-1"></i>
                                    Send me promotional emails and special offers from Nestria
                                </span>
                            </label>
                        </div>
                    </div>

                    <!-- Submit Button -->
                    <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                        <button type="submit"
                                id="submitButton"
                                class="w-full bg-gradient-to-r from-airbnb-red to-red-600 text-white py-4 rounded-lg font-semibold text-lg transition-all transform shadow-lg hover:from-red-600 hover:to-red-700 hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
                                style="display: block !important; visibility: visible !important;">
                            <i class="fas fa-lock mr-2"></i>
                            <span id="buttonText">Confirm and pay</span>
                        </button>

                        <!-- Terms reminder (shown when button is disabled) -->
                        <div id="termsReminder" class="hidden mt-3 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                            <p class="text-sm text-yellow-800 text-center">
                                <i class="fas fa-info-circle mr-1"></i>
                                Please accept the Terms of Service to continue
                            </p>
                        </div>

                        <p class="text-center text-xs text-gray-500 mt-3">
                            <i class="fas fa-shield-alt mr-1"></i>
                            Your payment information is encrypted and secure
                        </p>
                    </div>
                </form>
            </div>

            <!-- Right Column - Booking Summary -->
            <div class="lg:col-span-1">
                <div class="bg-white rounded-xl shadow-lg border border-gray-200 p-6 sticky top-8">
                    <!-- Property Info -->
                    <div class="mb-6">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">Booking summary</h3>
                        <div class="flex space-x-4">
                            <div class="flex-shrink-0">
                                {% if room.main_image %}
                                    <img src="{{ room.main_image.url }}" alt="{{ room.title }}" class="w-24 h-24 object-cover rounded-xl shadow-md">
                                {% else %}
                                    <div class="w-24 h-24 bg-gradient-to-br from-gray-200 to-gray-300 rounded-xl flex items-center justify-center shadow-md">
                                        <i class="fas fa-home text-gray-400 text-xl"></i>
                                    </div>
                                {% endif %}
                            </div>
                            <div class="flex-1">
                                <h4 class="font-semibold text-gray-900 mb-1 text-lg">{{ room.title }}</h4>
                                <p class="text-sm text-gray-600 mb-2">{{ room.room_type.name }}</p>
                                {% if room.average_rating > 0 %}
                                    <div class="flex items-center text-sm">
                                        <i class="fas fa-star text-yellow-400"></i>
                                        <span class="ml-1 font-medium">{{ room.average_rating|floatformat:1 }}</span>
                                        <span class="ml-1 text-gray-600">({{ room.review_count }} reviews)</span>
                                    </div>
                                {% endif %}
                                <div class="mt-2 flex items-center text-sm text-gray-600">
                                    <i class="fas fa-map-marker-alt mr-1"></i>
                                    <span>{{ room.city }}, {{ room.country }}</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Price Breakdown -->
                    <div class="border-t border-gray-200 pt-6" x-data="priceCalculator()">
                        <h4 class="font-semibold text-gray-900 mb-4">Price details</h4>
                        <div class="space-y-3 mb-6">
                            <div class="flex justify-between items-center">
                                <span class="text-gray-700">${{ room.price_per_night|floatformat:0 }} x <span x-text="nights"></span> nights</span>
                                <span class="font-medium" x-text="'$' + basePrice.toFixed(0)"></span>
                            </div>

                            {% if room.cleaning_fee > 0 %}
                                <div class="flex justify-between items-center">
                                    <span class="text-gray-700">Cleaning fee</span>
                                    <span class="font-medium">${{ room.cleaning_fee|floatformat:0 }}</span>
                                </div>
                            {% endif %}

                            {% if room.service_fee > 0 %}
                                <div class="flex justify-between items-center">
                                    <span class="text-gray-700">Service fee</span>
                                    <span class="font-medium">${{ room.service_fee|floatformat:0 }}</span>
                                </div>
                            {% endif %}

                            <div class="flex justify-between items-center text-sm text-green-600">
                                <span>Discount (First booking)</span>
                                <span>-$25</span>
                            </div>
                        </div>

                        <div class="border-t border-gray-200 pt-4">
                            <div class="flex justify-between items-center">
                                <span class="text-xl font-bold text-gray-900">Total (USD)</span>
                                <span class="text-xl font-bold text-airbnb-red" x-text="'$' + (totalPrice - 25).toFixed(0)"></span>
                            </div>
                        </div>
                    </div>

                    <!-- Cancellation Policy -->
                    <div class="border-t border-gray-200 pt-6">
                        <h4 class="font-semibold text-gray-900 mb-3 flex items-center">
                            <i class="fas fa-calendar-times text-airbnb-red mr-2"></i>
                            Cancellation policy
                        </h4>
                        <div class="bg-green-50 border border-green-200 rounded-lg p-4">
                            <div class="flex items-start">
                                <i class="fas fa-check-circle text-green-500 mt-1 mr-3"></i>
                                <div>
                                    <p class="text-sm text-green-800 font-medium mb-1">Free cancellation for 48 hours</p>
                                    <p class="text-xs text-green-700">After that, cancel before check-in and get a 50% refund, minus service fees.</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Trust & Safety -->
                    <div class="border-t border-gray-200 pt-6">
                        <div class="flex items-center justify-center space-x-6 text-xs text-gray-500">
                            <div class="flex items-center">
                                <i class="fas fa-shield-alt text-green-500 mr-1"></i>
                                <span>Secure payment</span>
                            </div>
                            <div class="flex items-center">
                                <i class="fas fa-headset text-blue-500 mr-1"></i>
                                <span>24/7 support</span>
                            </div>
                            <div class="flex items-center">
                                <i class="fas fa-medal text-yellow-500 mr-1"></i>
                                <span>Best price guarantee</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- Alpine.js Mask Plugin -->
<script src="https://unpkg.com/@alpinejs/mask@3.x.x/dist/cdn.min.js"></script>

<script>
function checkoutForm() {
    const urlParams = new URLSearchParams(window.location.search);

    return {
        checkIn: urlParams.get('check_in') || '',
        checkOut: urlParams.get('check_out') || '',
        adults: parseInt(urlParams.get('adults')) || 1,
        children: parseInt(urlParams.get('children')) || 0,
        infants: parseInt(urlParams.get('infants')) || 0,

        get dateRange() {
            if (this.checkIn && this.checkOut) {
                const start = new Date(this.checkIn).toLocaleDateString('en-US', {
                    weekday: 'short',
                    month: 'short',
                    day: 'numeric'
                });
                const end = new Date(this.checkOut).toLocaleDateString('en-US', {
                    weekday: 'short',
                    month: 'short',
                    day: 'numeric',
                    year: 'numeric'
                });
                return `${start} - ${end}`;
            }
            return 'Select dates';
        },

        get guestDisplay() {
            const total = this.adults + this.children + this.infants;
            let display = '';
            if (this.adults > 0) display += `${this.adults} adult${this.adults > 1 ? 's' : ''}`;
            if (this.children > 0) display += `${display ? ', ' : ''}${this.children} child${this.children > 1 ? 'ren' : ''}`;
            if (this.infants > 0) display += `${display ? ', ' : ''}${this.infants} infant${this.infants > 1 ? 's' : ''}`;
            return display || '1 guest';
        }
    }
}

function priceCalculator() {
    const urlParams = new URLSearchParams(window.location.search);
    const checkIn = urlParams.get('check_in');
    const checkOut = urlParams.get('check_out');
    
    return {
        get nights() {
            if (checkIn && checkOut) {
                const start = new Date(checkIn);
                const end = new Date(checkOut);
                return Math.max(0, Math.ceil((end - start) / (1000 * 60 * 60 * 24)));
            }
            return 1;
        },
        
        get basePrice() {
            return {{ room.price_per_night }} * this.nights;
        },
        
        get totalPrice() {
            return this.basePrice + {{ room.cleaning_fee }} + {{ room.service_fee }};
        }
    }
}

// Enhanced button state management
document.addEventListener('DOMContentLoaded', function() {
    const submitButton = document.getElementById('submitButton');
    const buttonText = document.getElementById('buttonText');
    const termsReminder = document.getElementById('termsReminder');
    const termsCheckbox = document.querySelector('input[type="checkbox"][required]');
    const form = document.querySelector('form');

    function updateButtonState() {
        const isTermsAccepted = termsCheckbox.checked;

        if (isTermsAccepted) {
            submitButton.disabled = false;
            submitButton.classList.remove('opacity-50', 'cursor-not-allowed');
            submitButton.classList.add('hover:from-red-600', 'hover:to-red-700', 'hover:scale-105');
            termsReminder.classList.add('hidden');
            buttonText.innerHTML = '<i class="fas fa-lock mr-2"></i>Confirm and pay';
        } else {
            submitButton.disabled = true;
            submitButton.classList.add('opacity-50', 'cursor-not-allowed');
            submitButton.classList.remove('hover:from-red-600', 'hover:to-red-700', 'hover:scale-105');
            termsReminder.classList.remove('hidden');
            buttonText.innerHTML = '<i class="fas fa-exclamation-triangle mr-2"></i>Accept terms to continue';
        }
    }

    // Initial state
    updateButtonState();

    // Listen for checkbox changes
    termsCheckbox.addEventListener('change', updateButtonState);

    // Enhanced form submission
    form.addEventListener('submit', function(e) {
        if (!termsCheckbox.checked) {
            e.preventDefault();
            termsCheckbox.focus();
            termsCheckbox.parentElement.classList.add('animate-pulse');
            setTimeout(() => {
                termsCheckbox.parentElement.classList.remove('animate-pulse');
            }, 1000);
            return false;
        }

        // Show loading state
        submitButton.disabled = true;
        buttonText.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Processing payment...';
        submitButton.classList.add('opacity-75');
    });

    // Make button always visible but with clear state indication
    submitButton.style.display = 'block';
    submitButton.style.visibility = 'visible';
});

// Edit functions for trip details
function editDates() {
    const urlParams = new URLSearchParams(window.location.search);
    const roomId = window.location.pathname.split('/')[3];
    const adults = urlParams.get('adults') || '1';
    const children = urlParams.get('children') || '0';
    const infants = urlParams.get('infants') || '0';

    // Redirect back to room detail with current guest info
    window.location.href = `/rooms/${roomId}/?adults=${adults}&children=${children}&infants=${infants}`;
}

function editGuests() {
    const urlParams = new URLSearchParams(window.location.search);
    const roomId = window.location.pathname.split('/')[3];
    const checkIn = urlParams.get('check_in') || '';
    const checkOut = urlParams.get('check_out') || '';

    // Redirect back to room detail with current date info
    window.location.href = `/rooms/${roomId}/?check_in=${checkIn}&check_out=${checkOut}`;
}
</script>
{% endblock %}
