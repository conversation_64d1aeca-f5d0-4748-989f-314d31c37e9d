{% extends 'base.html' %}
{% load static %}

{% block title %}Profile - Airbnb Clone{% endblock %}

{% block content %}
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Profile Header -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8">
        <div class="flex items-center space-x-6">
            <div class="w-20 h-20 bg-gray-300 rounded-full flex items-center justify-center">
                <i class="fas fa-user text-gray-600 text-2xl"></i>
            </div>
            <div>
                <h1 class="text-2xl font-bold text-gray-900">
                    {% if user.first_name %}
                        {{ user.first_name }} {{ user.last_name }}
                    {% else %}
                        {{ user.username }}
                    {% endif %}
                </h1>
                <p class="text-gray-600">{{ user.email }}</p>
                <p class="text-sm text-gray-500">Member since {{ user.date_joined|date:"F Y" }}</p>
            </div>
        </div>
    </div>

    <!-- Profile Navigation -->
    <div class="mb-8" x-data="{ activeTab: 'trips' }">
        <nav class="flex space-x-8 border-b border-gray-200">
            <button @click="activeTab = 'trips'" :class="activeTab === 'trips' ? 'border-airbnb-red text-airbnb-red' : 'border-transparent text-gray-500 hover:text-gray-700'" class="py-2 px-1 border-b-2 font-medium text-sm">
                Your trips
            </button>
            <button @click="activeTab = 'favorites'" :class="activeTab === 'favorites' ? 'border-airbnb-red text-airbnb-red' : 'border-transparent text-gray-500 hover:text-gray-700'" class="py-2 px-1 border-b-2 font-medium text-sm">
                Favorites
            </button>
            {% if hosted_rooms %}
                <button @click="activeTab = 'hosting'" :class="activeTab === 'hosting' ? 'border-airbnb-red text-airbnb-red' : 'border-transparent text-gray-500 hover:text-gray-700'" class="py-2 px-1 border-b-2 font-medium text-sm">
                    Your listings
                </button>
            {% endif %}
            <button @click="activeTab = 'account'" :class="activeTab === 'account' ? 'border-airbnb-red text-airbnb-red' : 'border-transparent text-gray-500 hover:text-gray-700'" class="py-2 px-1 border-b-2 font-medium text-sm">
                Account
            </button>
        </nav>

        <!-- Your Trips -->
        <div x-show="activeTab === 'trips'" class="mt-8">
            <h2 class="text-xl font-semibold text-gray-900 mb-6">Your trips</h2>
            {% if user_reservations %}
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    {% for reservation in user_reservations %}
                        <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
                            <div class="relative">
                                {% if reservation.room.main_image %}
                                    <img src="{{ reservation.room.main_image.url }}" alt="{{ reservation.room.title }}" class="w-full h-48 object-cover">
                                {% else %}
                                    <div class="w-full h-48 bg-gray-200 flex items-center justify-center">
                                        <i class="fas fa-home text-gray-400 text-3xl"></i>
                                    </div>
                                {% endif %}
                                <div class="absolute top-3 left-3">
                                    <span class="px-2 py-1 text-xs font-medium rounded-full
                                        {% if reservation.status == 'confirmed' %}bg-green-100 text-green-800
                                        {% elif reservation.status == 'pending' %}bg-yellow-100 text-yellow-800
                                        {% elif reservation.status == 'cancelled' %}bg-red-100 text-red-800
                                        {% else %}bg-gray-100 text-gray-800{% endif %}">
                                        {{ reservation.get_status_display }}
                                    </span>
                                </div>
                            </div>
                            <div class="p-4">
                                <h3 class="font-semibold text-gray-900 mb-2">{{ reservation.room.title }}</h3>
                                <p class="text-sm text-gray-600 mb-2">{{ reservation.room.city }}, {{ reservation.room.country }}</p>
                                <p class="text-sm text-gray-500 mb-3">
                                    {{ reservation.check_in|date:"M d" }} - {{ reservation.check_out|date:"M d, Y" }}
                                </p>
                                <div class="flex items-center justify-between">
                                    <span class="font-semibold text-gray-900">${{ reservation.total_price|floatformat:0 }}</span>
                                    <a href="{{ reservation.room.get_absolute_url }}" class="text-airbnb-red hover:text-red-600 text-sm font-medium">
                                        View details
                                    </a>
                                </div>
                            </div>
                        </div>
                    {% endfor %}
                </div>
                <div class="mt-6">
                    <a href="{% url 'reservations:my_trips' %}" class="text-airbnb-red hover:text-red-600 font-medium">
                        View all trips →
                    </a>
                </div>
            {% else %}
                <div class="text-center py-12">
                    <i class="fas fa-suitcase text-gray-300 text-6xl mb-4"></i>
                    <h3 class="text-xl font-semibold text-gray-600 mb-2">No trips yet</h3>
                    <p class="text-gray-500 mb-6">Time to dust off your bags and start planning your next adventure</p>
                    <a href="{% url 'core:home' %}" class="inline-block bg-airbnb-red text-white px-6 py-3 rounded-lg font-medium hover:bg-red-600 transition-colors btn-primary">
                        Start searching
                    </a>
                </div>
            {% endif %}
        </div>

        <!-- Favorites -->
        <div x-show="activeTab === 'favorites'" class="mt-8">
            <h2 class="text-xl font-semibold text-gray-900 mb-6">Your favorites</h2>
            {% if user_favorites %}
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                    {% for favorite in user_favorites %}
                        <div class="property-card bg-white rounded-lg overflow-hidden shadow-sm hover:shadow-lg transition-shadow">
                            <a href="{{ favorite.room.get_absolute_url }}" class="block">
                                <div class="relative">
                                    {% if favorite.room.main_image %}
                                        <img src="{{ favorite.room.main_image.url }}" alt="{{ favorite.room.title }}" class="w-full h-48 object-cover">
                                    {% else %}
                                        <div class="w-full h-48 bg-gray-200 flex items-center justify-center">
                                            <i class="fas fa-home text-gray-400 text-3xl"></i>
                                        </div>
                                    {% endif %}
                                    <button class="absolute top-3 right-3 favorite-btn heart-icon" data-property-id="{{ favorite.room.id }}">
                                        <i class="fas fa-heart text-airbnb-red text-xl liked"></i>
                                    </button>
                                </div>
                                <div class="p-4">
                                    <h3 class="font-semibold text-gray-900 truncate mb-2">{{ favorite.room.title }}</h3>
                                    <p class="text-gray-600 text-sm mb-2">{{ favorite.room.city }}, {{ favorite.room.country }}</p>
                                    <div class="flex items-center justify-between">
                                        <div>
                                            <span class="font-semibold text-gray-900 price-display">${{ favorite.room.price_per_night|floatformat:0 }}</span>
                                            <span class="text-gray-600 text-sm">/ night</span>
                                        </div>
                                        {% if favorite.room.average_rating > 0 %}
                                            <div class="flex items-center">
                                                <i class="fas fa-star text-airbnb-red text-sm"></i>
                                                <span class="text-sm text-gray-600 ml-1">{{ favorite.room.average_rating|floatformat:1 }}</span>
                                            </div>
                                        {% endif %}
                                    </div>
                                </div>
                            </a>
                        </div>
                    {% endfor %}
                </div>
            {% else %}
                <div class="text-center py-12">
                    <i class="fas fa-heart text-gray-300 text-6xl mb-4"></i>
                    <h3 class="text-xl font-semibold text-gray-600 mb-2">No favorites yet</h3>
                    <p class="text-gray-500 mb-6">Start exploring and save your favorite places to stay</p>
                    <a href="{% url 'core:home' %}" class="inline-block bg-airbnb-red text-white px-6 py-3 rounded-lg font-medium hover:bg-red-600 transition-colors btn-primary">
                        Start exploring
                    </a>
                </div>
            {% endif %}
        </div>

        <!-- Your Listings -->
        {% if hosted_rooms %}
            <div x-show="activeTab === 'hosting'" class="mt-8">
                <h2 class="text-xl font-semibold text-gray-900 mb-6">Your listings</h2>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    {% for room in hosted_rooms %}
                        <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
                            <div class="relative">
                                {% if room.main_image %}
                                    <img src="{{ room.main_image.url }}" alt="{{ room.title }}" class="w-full h-48 object-cover">
                                {% else %}
                                    <div class="w-full h-48 bg-gray-200 flex items-center justify-center">
                                        <i class="fas fa-home text-gray-400 text-3xl"></i>
                                    </div>
                                {% endif %}
                            </div>
                            <div class="p-4">
                                <h3 class="font-semibold text-gray-900 mb-2">{{ room.title }}</h3>
                                <p class="text-sm text-gray-600 mb-2">{{ room.city }}, {{ room.country }}</p>
                                <div class="flex items-center justify-between">
                                    <span class="font-semibold text-gray-900">${{ room.price_per_night|floatformat:0 }}/night</span>
                                    <a href="{{ room.get_absolute_url }}" class="text-airbnb-red hover:text-red-600 text-sm font-medium">
                                        View listing
                                    </a>
                                </div>
                            </div>
                        </div>
                    {% endfor %}
                </div>
            </div>
        {% endif %}

        <!-- Account Settings -->
        <div x-show="activeTab === 'account'" class="mt-8">
            <h2 class="text-xl font-semibold text-gray-900 mb-6">Account settings</h2>
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <div class="space-y-6">
                    <div>
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Personal information</h3>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Username</label>
                                <p class="text-gray-900">{{ user.username }}</p>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Email</label>
                                <p class="text-gray-900">{{ user.email }}</p>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">First name</label>
                                <p class="text-gray-900">{{ user.first_name|default:"Not provided" }}</p>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Last name</label>
                                <p class="text-gray-900">{{ user.last_name|default:"Not provided" }}</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="pt-6 border-t border-gray-200">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Account actions</h3>
                        <div class="space-y-3">
                            <button class="text-airbnb-red hover:text-red-600 font-medium">
                                Edit profile
                            </button>
                            <br>
                            <button class="text-airbnb-red hover:text-red-600 font-medium">
                                Change password
                            </button>
                            <br>
                            <a href="{% url 'core:logout' %}" class="text-gray-600 hover:text-gray-800 font-medium">
                                Sign out
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
