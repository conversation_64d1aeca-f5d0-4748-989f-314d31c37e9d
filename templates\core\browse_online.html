{% extends 'base.html' %}
{% load static %}

{% block title %}Browse Online Experiences - Nestria{% endblock %}

{% block content %}
<div class="min-h-screen bg-gray-50 dark:bg-gray-900 py-8">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="text-center mb-12">
            <h1 class="text-4xl font-bold text-gray-900 dark:text-white mb-4">
                Online Experiences
            </h1>
            <p class="text-xl text-gray-600 dark:text-gray-400 max-w-3xl mx-auto">
                Join interactive experiences from the comfort of your home. Connect with hosts and other participants from around the world.
            </p>
        </div>

        <!-- Live Now Section -->
        <div class="mb-12">
            <div class="flex items-center justify-between mb-6">
                <h2 class="text-2xl font-bold text-gray-900 dark:text-white">🔴 Live Now</h2>
                <span class="text-sm text-red-600 dark:text-red-400 font-medium">Join immediately</span>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm overflow-hidden hover:shadow-md transition-shadow border-2 border-red-200 dark:border-red-800">
                    <div class="h-48 bg-gradient-to-r from-red-400 to-pink-500 relative">
                        <div class="absolute top-3 left-3 bg-red-500 text-white px-2 py-1 rounded-full text-xs font-bold animate-pulse">
                            🔴 LIVE
                        </div>
                        <div class="absolute top-3 right-3 bg-white bg-opacity-90 px-2 py-1 rounded-full text-xs">
                            12 watching
                        </div>
                    </div>
                    <div class="p-4">
                        <div class="flex items-center mb-2">
                            <div class="w-6 h-6 bg-green-400 rounded-full mr-2"></div>
                            <span class="text-sm text-gray-600 dark:text-gray-400">Chef Maria • Rome</span>
                        </div>
                        <h3 class="font-semibold text-gray-900 dark:text-white mb-2">Live Pasta Making Class</h3>
                        <p class="text-gray-600 dark:text-gray-400 text-sm mb-3">Learn to make fresh pasta from scratch</p>
                        <div class="flex items-center justify-between">
                            <span class="text-lg font-bold text-gray-900 dark:text-white">$25</span>
                            <button onclick="joinLiveExperience('Live Pasta Making Class', '$25')" class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg text-sm font-medium">
                                Join Live
                            </button>
                        </div>
                    </div>
                </div>

                <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm overflow-hidden hover:shadow-md transition-shadow border-2 border-red-200 dark:border-red-800">
                    <div class="h-48 bg-gradient-to-r from-blue-400 to-purple-500 relative">
                        <div class="absolute top-3 left-3 bg-red-500 text-white px-2 py-1 rounded-full text-xs font-bold animate-pulse">
                            🔴 LIVE
                        </div>
                        <div class="absolute top-3 right-3 bg-white bg-opacity-90 px-2 py-1 rounded-full text-xs">
                            8 watching
                        </div>
                    </div>
                    <div class="p-4">
                        <div class="flex items-center mb-2">
                            <div class="w-6 h-6 bg-green-400 rounded-full mr-2"></div>
                            <span class="text-sm text-gray-600 dark:text-gray-400">Yuki • Tokyo</span>
                        </div>
                        <h3 class="font-semibold text-gray-900 dark:text-white mb-2">Virtual Tokyo Tour</h3>
                        <p class="text-gray-600 dark:text-gray-400 text-sm mb-3">Explore Tokyo's hidden gems live</p>
                        <div class="flex items-center justify-between">
                            <span class="text-lg font-bold text-gray-900 dark:text-white">$15</span>
                            <button onclick="joinLiveExperience('Virtual Tokyo Tour', '$15')" class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg text-sm font-medium">
                                Join Live
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Upcoming Sessions -->
        <div class="mb-12">
            <h2 class="text-2xl font-bold text-gray-900 dark:text-white mb-6">📅 Upcoming Sessions</h2>
            
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm overflow-hidden hover:shadow-md transition-shadow">
                    <div class="h-48 bg-gradient-to-r from-green-400 to-teal-500 relative">
                        <div class="absolute top-3 left-3 bg-green-600 text-white px-2 py-1 rounded-full text-xs font-medium">
                            Tomorrow 2PM EST
                        </div>
                        <div class="absolute top-3 right-3 bg-white bg-opacity-90 px-2 py-1 rounded-full text-xs">
                            ⭐ 4.9 (156)
                        </div>
                    </div>
                    <div class="p-4">
                        <div class="flex items-center mb-2">
                            <div class="w-6 h-6 bg-gray-300 rounded-full mr-2"></div>
                            <span class="text-sm text-gray-600 dark:text-gray-400">Sophie • Paris</span>
                        </div>
                        <h3 class="font-semibold text-gray-900 dark:text-white mb-2">Virtual Wine Tasting</h3>
                        <p class="text-gray-600 dark:text-gray-400 text-sm mb-3">Taste French wines from home</p>
                        <div class="flex items-center justify-between">
                            <span class="text-lg font-bold text-gray-900 dark:text-white">$45</span>
                            <button onclick="bookOnlineExperience('Virtual Wine Tasting', '$45', 'Tomorrow 2PM EST')" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm">
                                Book Session
                            </button>
                        </div>
                    </div>
                </div>

                <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm overflow-hidden hover:shadow-md transition-shadow">
                    <div class="h-48 bg-gradient-to-r from-purple-400 to-pink-500 relative">
                        <div class="absolute top-3 left-3 bg-purple-600 text-white px-2 py-1 rounded-full text-xs font-medium">
                            Wed 7PM EST
                        </div>
                        <div class="absolute top-3 right-3 bg-white bg-opacity-90 px-2 py-1 rounded-full text-xs">
                            ⭐ 4.8 (89)
                        </div>
                    </div>
                    <div class="p-4">
                        <div class="flex items-center mb-2">
                            <div class="w-6 h-6 bg-gray-300 rounded-full mr-2"></div>
                            <span class="text-sm text-gray-600 dark:text-gray-400">Carlos • Barcelona</span>
                        </div>
                        <h3 class="font-semibold text-gray-900 dark:text-white mb-2">Flamenco Dance Class</h3>
                        <p class="text-gray-600 dark:text-gray-400 text-sm mb-3">Learn traditional Spanish dance</p>
                        <div class="flex items-center justify-between">
                            <span class="text-lg font-bold text-gray-900 dark:text-white">$30</span>
                            <button onclick="bookOnlineExperience('Flamenco Dance Class', '$30', 'Wed 7PM EST')" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm">
                                Book Session
                            </button>
                        </div>
                    </div>
                </div>

                <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm overflow-hidden hover:shadow-md transition-shadow">
                    <div class="h-48 bg-gradient-to-r from-yellow-400 to-orange-500 relative">
                        <div class="absolute top-3 left-3 bg-orange-600 text-white px-2 py-1 rounded-full text-xs font-medium">
                            Fri 10AM EST
                        </div>
                        <div class="absolute top-3 right-3 bg-white bg-opacity-90 px-2 py-1 rounded-full text-xs">
                            ⭐ 4.7 (203)
                        </div>
                    </div>
                    <div class="p-4">
                        <div class="flex items-center mb-2">
                            <div class="w-6 h-6 bg-gray-300 rounded-full mr-2"></div>
                            <span class="text-sm text-gray-600 dark:text-gray-400">Raj • Mumbai</span>
                        </div>
                        <h3 class="font-semibold text-gray-900 dark:text-white mb-2">Indian Cooking Masterclass</h3>
                        <p class="text-gray-600 dark:text-gray-400 text-sm mb-3">Authentic curry and spice techniques</p>
                        <div class="flex items-center justify-between">
                            <span class="text-lg font-bold text-gray-900 dark:text-white">$35</span>
                            <button onclick="bookOnlineExperience('Indian Cooking Masterclass', '$35', 'Fri 10AM EST')" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm">
                                Book Session
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Categories -->
        <div class="mb-12">
            <h2 class="text-2xl font-bold text-gray-900 dark:text-white mb-6">Browse by Category</h2>
            
            <div class="grid grid-cols-2 md:grid-cols-4 gap-6">
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 text-center hover:shadow-md transition-shadow cursor-pointer" onclick="filterOnlineCategory('cooking')">
                    <div class="w-16 h-16 bg-red-100 dark:bg-red-900/20 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-utensils text-red-600 dark:text-red-400 text-2xl"></i>
                    </div>
                    <h3 class="font-semibold text-gray-900 dark:text-white mb-2">Cooking</h3>
                    <p class="text-sm text-gray-600 dark:text-gray-400">18 experiences</p>
                </div>
                
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 text-center hover:shadow-md transition-shadow cursor-pointer" onclick="filterOnlineCategory('art')">
                    <div class="w-16 h-16 bg-purple-100 dark:bg-purple-900/20 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-palette text-purple-600 dark:text-purple-400 text-2xl"></i>
                    </div>
                    <h3 class="font-semibold text-gray-900 dark:text-white mb-2">Arts & Crafts</h3>
                    <p class="text-sm text-gray-600 dark:text-gray-400">12 experiences</p>
                </div>
                
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 text-center hover:shadow-md transition-shadow cursor-pointer" onclick="filterOnlineCategory('wellness')">
                    <div class="w-16 h-16 bg-green-100 dark:bg-green-900/20 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-leaf text-green-600 dark:text-green-400 text-2xl"></i>
                    </div>
                    <h3 class="font-semibold text-gray-900 dark:text-white mb-2">Wellness</h3>
                    <p class="text-sm text-gray-600 dark:text-gray-400">15 experiences</p>
                </div>
                
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 text-center hover:shadow-md transition-shadow cursor-pointer" onclick="filterOnlineCategory('tours')">
                    <div class="w-16 h-16 bg-blue-100 dark:bg-blue-900/20 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-globe text-blue-600 dark:text-blue-400 text-2xl"></i>
                    </div>
                    <h3 class="font-semibold text-gray-900 dark:text-white mb-2">Virtual Tours</h3>
                    <p class="text-sm text-gray-600 dark:text-gray-400">9 experiences</p>
                </div>
            </div>
        </div>

        <!-- Call to Action -->
        <div class="bg-gradient-to-r from-green-600 to-teal-600 rounded-lg p-8 text-center text-white">
            <h2 class="text-2xl font-bold mb-4">Ready to Join the Fun?</h2>
            <p class="text-green-100 mb-6">Connect with amazing hosts and participants from around the world</p>
            <div class="space-x-4">
                <button onclick="alert('Browse all online experiences feature coming soon!')" class="bg-white text-green-600 px-6 py-3 rounded-lg font-medium hover:bg-gray-100 transition-colors">
                    Browse All Online
                </button>
                <button onclick="alert('Host online experience feature coming soon!')" class="border border-white text-white px-6 py-3 rounded-lg font-medium hover:bg-white hover:text-green-600 transition-colors">
                    Host Online
                </button>
            </div>
        </div>
    </div>
</div>

<script>
function joinLiveExperience(name, price) {
    if (confirm(`Join "${name}" live session for ${price}?\n\nYou'll receive a Zoom link immediately after payment.`)) {
        alert('Joining live session...\n\nYou would receive:\n• Zoom meeting link\n• Session materials\n• Host contact information\n\nPayment processing...');
    }
}

function bookOnlineExperience(name, price, time) {
    if (confirm(`Book "${name}" for ${price}?\n\nScheduled: ${time}\n\nYou'll receive confirmation and joining instructions via email.`)) {
        alert('Booking confirmed!\n\nYou would receive:\n• Calendar invite\n• Zoom meeting details\n• Preparation instructions\n• Host contact information');
    }
}

function filterOnlineCategory(category) {
    const categoryNames = {
        'cooking': 'Cooking & Food',
        'art': 'Arts & Crafts',
        'wellness': 'Wellness & Meditation',
        'tours': 'Virtual Tours'
    };
    
    alert(`Showing ${categoryNames[category]} online experiences...\n\nFiltering feature coming soon!`);
}
</script>
{% endblock %}
