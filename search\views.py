from django.shortcuts import render
from django.views.generic import ListView
from django.db.models import Q
from django.http import JsonResponse
from rooms.models import Room


class SearchResultsView(ListView):
    """Search results view"""
    model = Room
    template_name = 'search/results.html'
    context_object_name = 'rooms'
    paginate_by = 20

    def get_queryset(self):
        queryset = Room.objects.filter(is_active=True).select_related('host', 'room_type').prefetch_related('images')

        # Get search parameters
        location = self.request.GET.get('location', '')
        check_in = self.request.GET.get('check_in', '')
        check_out = self.request.GET.get('check_out', '')
        adults = self.request.GET.get('adults', 1)
        children = self.request.GET.get('children', 0)
        infants = self.request.GET.get('infants', 0)

        # Filter by location
        if location:
            queryset = queryset.filter(
                Q(city__icontains=location) |
                Q(country__icontains=location) |
                Q(address__icontains=location)
            )

        # Filter by guest capacity
        try:
            total_guests = int(adults) + int(children) + int(infants)
            queryset = queryset.filter(max_guests__gte=total_guests)
        except (ValueError, TypeError):
            pass

        return queryset

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['search_params'] = self.request.GET
        return context


def get_available_cities(request):
    """API endpoint to get available cities for search autocomplete"""
    query = request.GET.get('q', '').strip()

    # Get distinct cities from active rooms
    cities_queryset = Room.objects.filter(is_active=True).values_list('city', flat=True).distinct()

    # Filter cities based on query if provided
    if query:
        cities_queryset = cities_queryset.filter(city__icontains=query)

    # Limit to 10 results and sort
    cities = list(cities_queryset.order_by('city')[:10])

    # Also get countries for context
    cities_with_countries = []
    for city in cities:
        # Get a sample room from this city to get the country
        sample_room = Room.objects.filter(city=city, is_active=True).first()
        if sample_room:
            cities_with_countries.append({
                'city': city,
                'country': sample_room.country,
                'display': f"{city}, {sample_room.country}"
            })

    return JsonResponse({
        'cities': cities_with_countries
    })
