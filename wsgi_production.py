"""
WSGI config for airbnb_clone project in production.

It exposes the WSGI callable as a module-level variable named ``application``.

For more information on this file, see
https://docs.djangoproject.com/en/5.2/howto/deployment/wsgi/
"""

import os
from django.core.wsgi import get_wsgi_application

# Set the settings module for production
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'airbnb_clone.settings_production')

application = get_wsgi_application()
