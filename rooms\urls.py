from django.urls import path
from . import views

app_name = 'rooms'

urlpatterns = [
    path('<int:pk>/', views.RoomDetailView.as_view(), name='room_detail'),
    path('<int:pk>/book/', views.BookingView.as_view(), name='book'),
    path('destinations/', views.DestinationListView.as_view(), name='destinations'),
    path('destinations/<slug:slug>/', views.DestinationDetailView.as_view(), name='destination_detail'),
    path('city/<str:city>/', views.CityHotelsView.as_view(), name='city_hotels'),
    path('api/cities/', views.get_cities_api, name='cities_api'),
]

