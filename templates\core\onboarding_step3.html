<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Create Wishlists - Step 3</title>
    {% load static %}
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'nestria-red': '#FF5A5F',
                    }
                }
            }
        }
    </script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body class="bg-gray-50 min-h-screen flex items-center justify-center p-4">
    <div class="bg-white rounded-2xl shadow-xl w-full max-w-lg p-8 relative">
        <!-- Progress Bar -->
        <div class="mb-8">
            <div class="flex justify-between items-center mb-2">
                <span class="text-sm text-gray-600">Step 3 of 3</span>
                <span class="text-sm text-gray-600">100%</span>
            </div>
            <div class="w-full bg-gray-200 rounded-full h-2">
                <div class="bg-nestria-red h-2 rounded-full" style="width: 100%"></div>
            </div>
        </div>

        <!-- Content -->
        <div class="text-center mb-8">
            <div class="w-16 h-16 bg-pink-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <i class="fas fa-heart text-pink-600 text-2xl"></i>
            </div>
            <h1 class="text-2xl font-bold text-gray-900 mb-4">Create Wishlists</h1>
            <p class="text-gray-600 mb-6">
                Save your favorite places and organize them into collections for easy planning.
            </p>
        </div>

        <!-- Wishlist Features -->
        <div class="space-y-4 mb-8">
            <div class="bg-gray-50 rounded-lg p-4">
                <div class="flex items-center space-x-3 mb-2">
                    <i class="fas fa-bookmark text-nestria-red"></i>
                    <h3 class="font-semibold text-gray-900">Save Favorites</h3>
                </div>
                <p class="text-sm text-gray-600">Click the heart icon on any listing to save it to your wishlist</p>
            </div>
            
            <div class="bg-gray-50 rounded-lg p-4">
                <div class="flex items-center space-x-3 mb-2">
                    <i class="fas fa-folder text-nestria-red"></i>
                    <h3 class="font-semibold text-gray-900">Organize Collections</h3>
                </div>
                <p class="text-sm text-gray-600">Create different wishlists for different trips or occasions</p>
            </div>
            
            <div class="bg-gray-50 rounded-lg p-4">
                <div class="flex items-center space-x-3 mb-2">
                    <i class="fas fa-share text-nestria-red"></i>
                    <h3 class="font-semibold text-gray-900">Share with Friends</h3>
                </div>
                <p class="text-sm text-gray-600">Share your wishlists with travel companions for group planning</p>
            </div>
        </div>

        <!-- Demo Wishlist Creation -->
        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-8">
            <h3 class="font-semibold text-blue-900 mb-2">Try it now!</h3>
            <p class="text-sm text-blue-800 mb-3">Create your first wishlist to get started</p>
            <button onclick="openCreateWishlistModal()" class="w-full bg-blue-600 text-white py-2 rounded-lg font-medium hover:bg-blue-700 transition-all">
                <i class="fas fa-plus mr-2"></i>Create My First Wishlist
            </button>
        </div>

        <!-- Navigation -->
        <div class="flex justify-between">
            <a href="{% url 'core:onboarding_step2' %}" class="px-6 py-3 text-gray-600 hover:text-gray-800 font-medium">
                Back
            </a>
            <a href="{% url 'core:home' %}" class="px-8 py-3 bg-nestria-red text-white rounded-lg font-semibold hover:bg-red-600 transition-all">
                Start Exploring
            </a>
        </div>
    </div>

    <!-- Include Wishlist Modal -->
    {% include 'components/create_wishlist_modal.html' %}

    <script>
        function openCreateWishlistModal() {
            // Open the wishlist creation modal
            const modal = document.getElementById('wishlist-modal');
            if (modal) {
                modal.classList.remove('hidden');
                document.body.style.overflow = 'hidden';
                // Focus on the input field
                const input = document.getElementById('wishlist-name');
                if (input) {
                    setTimeout(() => input.focus(), 100);
                }
            } else {
                // Fallback: redirect to onboarding completion
                window.location.href = '{% url "core:onboarding_step1" %}';
            }
        }
    </script>
</body>
</html>
