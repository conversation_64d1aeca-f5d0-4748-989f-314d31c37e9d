{% extends 'base.html' %}
{% load static %}

{% block title %}Find your perfect stay - Nestria{% endblock %}

{% csrf_token %}

{% block content %}
<!-- Hero Section with Search -->
<section class="relative min-h-screen flex flex-col justify-start pt-24 bg-gradient-to-br from-primary-500 via-secondary-500 to-accent-500 text-white overflow-hidden">
    <!-- Animated background elements -->
    <div class="absolute inset-0">
        <div class="absolute top-20 left-20 w-72 h-72 bg-white/10 rounded-full blur-3xl animate-pulse-slow"></div>
        <div class="absolute bottom-20 right-20 w-96 h-96 bg-white/5 rounded-full blur-3xl animate-bounce-gentle"></div>
        <div class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[800px] h-[800px] bg-white/5 rounded-full blur-3xl"></div>
    </div>

    <!-- Dark overlay -->
    <div class="absolute inset-0 bg-black/20 dark:bg-black/40"></div>

    <div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6 z-10 flex-1 flex flex-col justify-start">
        <div class="text-center">
            <!-- Logo Nestria animé -->
            <div class="flex items-center justify-center mb-4 animate-fade-in">
                <div class="relative">
                    <!-- Logo principal Nestria - Image exacte -->
                    <img src="{% static 'images/nestria-logo-transparent.png' %}"
                         alt="Nestria Logo"
                         class="w-24 h-24 object-contain nestria-logo-pulse">
                </div>
            </div>

            <h1 class="text-3xl md:text-5xl font-display font-bold mb-3 animate-fade-in">
                Find your perfect
                <span class="block text-white font-black tracking-wider text-4xl md:text-6xl">
                    NESTRIA
                </span>
            </h1>
            <p class="text-base md:text-lg mb-6 opacity-90 max-w-3xl mx-auto animate-slide-up">
                Travel to survive and discover amazing places around the world. From cozy apartments to luxury villas, your perfect adventure starts here.
            </p>

            <!-- Search Form -->
            <div class="max-w-5xl mx-auto bg-white rounded-full shadow-2xl p-1.5 search-form mt-4" x-data="searchForm()">
                <form id="search-form" action="{% url 'search:results' %}" method="GET" class="flex flex-col md:flex-row items-center">
                    <!-- Location -->
                    <div class="flex-1 px-5 py-3 border-r border-gray-200">
                        <label class="block text-xs font-semibold text-gray-600 uppercase tracking-wide mb-1">Where</label>
                        <input
                            type="text"
                            name="location"
                            id="location-input"
                            placeholder="Search destination"
                            class="w-full text-gray-900 placeholder-gray-500 search-input text-sm"
                            x-model="location"
                        >
                    </div>

                    <!-- Check-in -->
                    <div class="flex-1 px-5 py-3 border-r border-gray-200">
                        <label class="block text-xs font-semibold text-gray-600 uppercase tracking-wide mb-1">Check in</label>
                        <input
                            type="date"
                            name="check_in"
                            class="w-full text-gray-900 search-input text-sm"
                            x-model="checkIn"
                            :min="today"
                        >
                    </div>

                    <!-- Check-out -->
                    <div class="flex-1 px-5 py-3 border-r border-gray-200">
                        <label class="block text-xs font-semibold text-gray-600 uppercase tracking-wide mb-1">Check out</label>
                        <input
                            type="date"
                            name="check_out"
                            class="w-full text-gray-900 search-input text-sm"
                            x-model="checkOut"
                            :min="checkIn || today"
                        >
                    </div>
                    
                    <!-- Guests -->
                    <div class="flex-1 px-6 py-4 relative" x-data="{ open: false }">
                        <label class="block text-xs font-semibold text-gray-600 uppercase tracking-wide mb-1">Who</label>
                        <button
                            type="button"
                            @click="open = !open"
                            class="w-full text-left text-gray-900 text-sm flex items-center justify-between hover:bg-gray-50 rounded-md p-2 -m-2 transition-colors"
                        >
                            <span x-text="guestDisplay"></span>
                            <i class="fas fa-chevron-down text-gray-400 text-xs transition-transform" :class="{ 'rotate-180': open }"></i>
                        </button>
                        
                        <!-- Guests Dropdown -->
                        <div x-show="open" @click.away="open = false" x-transition class="absolute top-full left-0 right-0 bg-white border border-gray-200 rounded-lg shadow-lg z-10 p-4 mt-2 min-w-72">
                            <div class="space-y-3">
                                <div class="flex items-center justify-between py-1">
                                    <div class="flex-1">
                                        <div class="font-medium text-gray-900 text-sm">Adults</div>
                                        <div class="text-xs text-gray-500">Ages 13 or above</div>
                                    </div>
                                    <div class="flex items-center space-x-3">
                                        <button type="button" @click="decrementGuests('adults')"
                                                class="w-8 h-8 rounded-full border border-gray-300 flex items-center justify-center hover:border-gray-400 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                                                :disabled="guests.adults <= 1">
                                            <i class="fas fa-minus text-gray-600 text-xs"></i>
                                        </button>
                                        <span x-text="guests.adults" class="w-6 text-center font-medium text-sm"></span>
                                        <button type="button" @click="incrementGuests('adults')"
                                                class="w-8 h-8 rounded-full border border-gray-300 flex items-center justify-center hover:border-gray-400 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                                                :disabled="guests.adults >= 16">
                                            <i class="fas fa-plus text-gray-600 text-xs"></i>
                                        </button>
                                    </div>
                                </div>
                                <div class="flex items-center justify-between py-1 border-t border-gray-100">
                                    <div class="flex-1">
                                        <div class="font-medium text-gray-900 text-sm">Children</div>
                                        <div class="text-xs text-gray-500">Ages 2-12</div>
                                    </div>
                                    <div class="flex items-center space-x-3">
                                        <button type="button" @click="decrementGuests('children')"
                                                class="w-8 h-8 rounded-full border border-gray-300 flex items-center justify-center hover:border-gray-400 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                                                :disabled="guests.children <= 0">
                                            <i class="fas fa-minus text-gray-600 text-xs"></i>
                                        </button>
                                        <span x-text="guests.children" class="w-6 text-center font-medium text-sm"></span>
                                        <button type="button" @click="incrementGuests('children')"
                                                class="w-8 h-8 rounded-full border border-gray-300 flex items-center justify-center hover:border-gray-400 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                                                :disabled="guests.children >= 5">
                                            <i class="fas fa-plus text-gray-600 text-xs"></i>
                                        </button>
                                    </div>
                                </div>
                                <div class="flex items-center justify-between py-1 border-t border-gray-100">
                                    <div class="flex-1">
                                        <div class="font-medium text-gray-900 text-sm">Infants</div>
                                        <div class="text-xs text-gray-500">Under 2 years old</div>
                                    </div>
                                    <div class="flex items-center space-x-3">
                                        <button type="button" @click="decrementGuests('infants')"
                                                class="w-8 h-8 rounded-full border border-gray-300 flex items-center justify-center hover:border-gray-400 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                                                :disabled="guests.infants <= 0">
                                            <i class="fas fa-minus text-gray-600 text-xs"></i>
                                        </button>
                                        <span x-text="guests.infants" class="w-6 text-center font-medium text-sm"></span>
                                        <button type="button" @click="incrementGuests('infants')"
                                                class="w-8 h-8 rounded-full border border-gray-300 flex items-center justify-center hover:border-gray-400 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                                                :disabled="guests.infants >= 5">
                                            <i class="fas fa-plus text-gray-600 text-xs"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Hidden inputs for form submission -->
                        <input type="hidden" name="adults" :value="guests.adults">
                        <input type="hidden" name="children" :value="guests.children">
                        <input type="hidden" name="infants" :value="guests.infants">
                    </div>
                    
                    <!-- Search Button -->
                    <div class="px-2">
                        <button type="submit" class="bg-airbnb-red hover:bg-red-600 text-white p-4 rounded-full transition-colors btn-primary">
                            <i class="fas fa-search text-lg"></i>
                        </button>
                    </div>
                </form>
            </div>

                {% if user.is_authenticated %}
                <div class="mt-8 text-center space-y-4">
                    <a href="{% url 'core:onboarding_step1' %}"
                       class="inline-flex items-center px-6 py-2 bg-white/10 backdrop-blur-sm border border-white/20 text-white font-medium rounded-full hover:bg-white/20 transition-all duration-300">
                        <i class="fas fa-graduation-cap mr-2"></i>
                        Learn How to Use Nestria
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</section>

<!-- Featured Properties -->
<section class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
    <div class="text-center mb-12">
        <h2 class="text-3xl font-bold text-gray-900 mb-4">Featured stays</h2>
        <p class="text-lg text-gray-600">Discover our most popular destinations</p>
    </div>
    
    {% if featured_rooms %}
        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 property-grid">
            {% for room in featured_rooms %}
                <div class="property-card bg-white rounded-lg overflow-hidden shadow-sm hover:shadow-lg transition-shadow">
                    <a href="{{ room.get_absolute_url }}" class="block">
                        <div class="relative">
                            {% if room.main_image %}
                                <img src="{{ room.main_image.url }}" alt="{{ room.title }}" class="w-full h-48 object-cover">
                            {% else %}
                                <div class="w-full h-48 bg-gray-200 flex items-center justify-center">
                                    <i class="fas fa-home text-gray-400 text-3xl"></i>
                                </div>
                            {% endif %}

                            <!-- Favorite Button -->
                            <button class="absolute top-3 right-3 favorite-btn heart-icon" data-property-id="{{ room.id }}">
                                <i class="far fa-heart text-white text-xl"></i>
                            </button>
                            
                            <!-- Favorite Button -->
                            <button class="absolute top-3 right-3 favorite-btn heart-icon" data-property-id="{{ room.id }}">
                                <i class="far fa-heart text-white text-xl"></i>
                            </button>
                        </div>
                        
                        <div class="p-4">
                            <div class="flex items-center justify-between mb-2">
                                <h3 class="font-semibold text-gray-900 truncate">{{ room.title }}</h3>
                                {% if room.average_rating > 0 %}
                                    <div class="flex items-center">
                                        <i class="fas fa-star text-airbnb-red text-sm"></i>
                                        <span class="text-sm text-gray-600 ml-1">{{ room.average_rating|floatformat:1 }}</span>
                                    </div>
                                {% endif %}
                            </div>
                            
                            <p class="text-gray-600 text-sm mb-2">{{ room.city }}, {{ room.country }}</p>
                            <p class="text-gray-500 text-sm mb-3">{{ room.max_guests }} guest{{ room.max_guests|pluralize }} · {{ room.bedrooms }} bedroom{{ room.bedrooms|pluralize }}</p>
                            
                            <div class="flex items-center justify-between">
                                <div>
                                    <span class="font-semibold text-gray-900 price-display">${{ room.price_per_night|floatformat:0 }}</span>
                                    <span class="text-gray-600 text-sm">/ night</span>
                                </div>
                                {% if room.instant_book %}
                                    <span class="text-xs bg-airbnb-red text-white px-2 py-1 rounded">Instant Book</span>
                                {% endif %}
                            </div>
                        </div>
                    </a>
                </div>
            {% endfor %}
        </div>
        
        <div class="text-center mt-12">
            <a href="{% url 'search:results' %}" class="inline-block bg-airbnb-red text-white px-8 py-3 rounded-lg font-medium hover:bg-red-600 transition-colors btn-primary">
                View all properties
            </a>
        </div>
    {% else %}
        <div class="text-center py-12">
            <i class="fas fa-home text-gray-300 text-6xl mb-4"></i>
            <h3 class="text-xl font-semibold text-gray-600 mb-2">No properties available</h3>
            <p class="text-gray-500">Check back later for amazing stays!</p>
        </div>
    {% endif %}
</section>

<!-- Featured Destinations Section -->
<section class="py-16">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
            <h2 class="text-3xl font-bold text-gray-900 mb-4">Explore amazing destinations</h2>
            <p class="text-lg text-gray-600">Discover the world's most beautiful and historic places</p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-8">
            {% for destination in featured_destinations %}
                <a href="{{ destination.get_absolute_url }}" class="destination-card group block">
                    <div class="relative overflow-hidden rounded-lg shadow-lg hover:shadow-xl transition-all duration-300">
                        <div class="relative h-64 overflow-hidden destination-hero">
                            {% if destination.main_image %}
                                <img src="{{ destination.main_image.url }}"
                                     alt="{{ destination.name }}"
                                     class="destination-image w-full h-full object-cover"
                                     onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                                <div class="w-full h-full bg-gradient-to-br from-blue-400 to-purple-500 flex items-center justify-center" style="display: none;">
                                    <i class="fas fa-map-marker-alt text-white text-4xl"></i>
                                </div>
                            {% else %}
                                <div class="w-full h-full bg-gradient-to-br from-blue-400 to-purple-500 flex items-center justify-center">
                                    <i class="fas fa-map-marker-alt text-white text-4xl"></i>
                                </div>
                            {% endif %}

                            <!-- Photo Count Badge -->
                            {% if destination.images.count > 0 %}
                                <div class="absolute top-4 right-4 gallery-counter text-white">
                                    <i class="fas fa-images mr-1"></i>
                                    {{ destination.images.count }} photos
                                </div>
                            {% endif %}

                            <!-- Content Overlay -->
                            <div class="absolute bottom-0 left-0 right-0 photo-overlay p-6 text-white">
                                <h3 class="text-2xl font-bold mb-2">{{ destination.name }}</h3>
                                <p class="text-sm opacity-90 mb-2">{{ destination.country }}</p>
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center text-sm">
                                        <i class="fas fa-home mr-2"></i>
                                        <span>{{ destination.room_count }} properties</span>
                                    </div>
                                    <div class="flex items-center text-sm opacity-75">
                                        <i class="fas fa-star mr-1"></i>
                                        <span>Featured</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </a>
            {% empty %}
                <!-- Fallback if no destinations -->
                <div class="col-span-3 text-center py-12">
                    <i class="fas fa-globe text-gray-300 text-6xl mb-4"></i>
                    <h3 class="text-xl font-semibold text-gray-600 mb-2">No destinations available</h3>
                    <p class="text-gray-500">Check back later for amazing destinations!</p>
                </div>
            {% endfor %}
        </div>

        <div class="text-center">
            <a href="{% url 'rooms:destinations' %}" class="inline-block bg-airbnb-red text-white px-8 py-3 rounded-lg font-medium hover:bg-red-600 transition-colors btn-primary">
                View all destinations
            </a>
        </div>
    </div>
</section>

<!-- Categories Section -->
<section class="bg-airbnb-light-gray py-16">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
            <h2 class="text-3xl font-bold text-gray-900 mb-4">Explore by category</h2>
            <p class="text-lg text-gray-600">Find the perfect type of stay for your trip</p>
        </div>
        
        <div class="grid grid-cols-2 md:grid-cols-4 gap-6">
            <a href="{% url 'search:results' %}?room_type=entire_place" class="bg-white rounded-lg p-6 text-center hover:shadow-md transition-shadow">
                <i class="fas fa-home text-airbnb-red text-3xl mb-3"></i>
                <h3 class="font-semibold text-gray-900">Entire places</h3>
                <p class="text-sm text-gray-600 mt-1">Have a place to yourself</p>
            </a>
            
            <a href="{% url 'search:results' %}?room_type=private_room" class="bg-white rounded-lg p-6 text-center hover:shadow-md transition-shadow">
                <i class="fas fa-bed text-airbnb-red text-3xl mb-3"></i>
                <h3 class="font-semibold text-gray-900">Private rooms</h3>
                <p class="text-sm text-gray-600 mt-1">Your own room in a home</p>
            </a>
            
            <a href="{% url 'search:results' %}?amenities=pool" class="bg-white rounded-lg p-6 text-center hover:shadow-md transition-shadow">
                <i class="fas fa-swimming-pool text-airbnb-red text-3xl mb-3"></i>
                <h3 class="font-semibold text-gray-900">Amazing pools</h3>
                <p class="text-sm text-gray-600 mt-1">Homes with pools</p>
            </a>
            
            <a href="{% url 'search:results' %}?amenities=wifi" class="bg-white rounded-lg p-6 text-center hover:shadow-md transition-shadow">
                <i class="fas fa-wifi text-airbnb-red text-3xl mb-3"></i>
                <h3 class="font-semibold text-gray-900">WiFi included</h3>
                <p class="text-sm text-gray-600 mt-1">Stay connected</p>
            </a>
        </div>
    </div>
</section>
{% endblock %}

{% block extra_js %}
<script>
function searchForm() {
    return {
        location: '',
        checkIn: '',
        checkOut: '',
        guests: {
            adults: 1,
            children: 0,
            infants: 0
        },
        today: new Date().toISOString().split('T')[0],
        
        get guestDisplay() {
            const total = this.guests.adults + this.guests.children + this.guests.infants;
            return total === 1 ? '1 guest' : `${total} guests`;
        },
        
        incrementGuests(type) {
            if (type === 'adults' && this.guests.adults < 16) {
                this.guests.adults++;
            } else if (type === 'children' && this.guests.children < 5) {
                this.guests.children++;
            } else if (type === 'infants' && this.guests.infants < 5) {
                this.guests.infants++;
            }
        },
        
        decrementGuests(type) {
            if (type === 'adults' && this.guests.adults > 1) {
                this.guests.adults--;
            } else if (type === 'children' && this.guests.children > 0) {
                this.guests.children--;
            } else if (type === 'infants' && this.guests.infants > 0) {
                this.guests.infants--;
            }
        }
    }
}

// Initialize favorites manager
document.addEventListener('DOMContentLoaded', function() {
    new FavoritesManager();
});

// Notification function
function showNotification(message, type) {
    const notification = document.createElement('div');
    notification.className = `fixed top-4 right-4 z-50 px-6 py-3 rounded-lg text-white font-medium transition-all duration-300 transform translate-x-full ${
        type === 'success' ? 'bg-green-500' : 'bg-red-500'
    }`;
    notification.textContent = message;

    document.body.appendChild(notification);

    // Animate in
    setTimeout(() => {
        notification.classList.remove('translate-x-full');
    }, 100);

    // Animate out and remove
    setTimeout(() => {
        notification.classList.add('translate-x-full');
        setTimeout(() => {
            document.body.removeChild(notification);
        }, 300);
    }, 3000);
}
</script>

<!-- City Autocomplete Script -->
<script src="{% static 'js/city-autocomplete.js' %}"></script>
<!-- Favorites Script -->
<script src="{% static 'js/favorites.js' %}"></script>
{% endblock %}


