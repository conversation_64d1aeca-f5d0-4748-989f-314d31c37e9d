<!DOCTYPE html>
<html lang="en" x-data="{ darkMode: localStorage.getItem('darkMode') === 'true' }" x-init="$watch('darkMode', val => localStorage.setItem('darkMode', val))" :class="{ 'dark': darkMode }">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Nestria - Travel to Survive{% endblock %}</title>

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        // Nouvelle palette de couleurs moderne
                        'primary': {
                            50: '#f0f9ff',
                            100: '#e0f2fe',
                            200: '#bae6fd',
                            300: '#7dd3fc',
                            400: '#38bdf8',
                            500: '#0ea5e9',
                            600: '#0284c7',
                            700: '#0369a1',
                            800: '#075985',
                            900: '#0c4a6e',
                        },
                        'secondary': {
                            50: '#fdf4ff',
                            100: '#fae8ff',
                            200: '#f5d0fe',
                            300: '#f0abfc',
                            400: '#e879f9',
                            500: '#d946ef',
                            600: '#c026d3',
                            700: '#a21caf',
                            800: '#86198f',
                            900: '#701a75',
                        },
                        'accent': {
                            50: '#fff7ed',
                            100: '#ffedd5',
                            200: '#fed7aa',
                            300: '#fdba74',
                            400: '#fb923c',
                            500: '#f97316',
                            600: '#ea580c',
                            700: '#c2410c',
                            800: '#9a3412',
                            900: '#7c2d12',
                        },
                        'success': '#10b981',
                        'warning': '#f59e0b',
                        'error': '#ef4444',
                        'dark': {
                            50: '#f8fafc',
                            100: '#f1f5f9',
                            200: '#e2e8f0',
                            300: '#cbd5e1',
                            400: '#94a3b8',
                            500: '#64748b',
                            600: '#475569',
                            700: '#334155',
                            800: '#1e293b',
                            900: '#0f172a',
                        }
                    },
                    fontFamily: {
                        'sans': ['Inter', 'system-ui', 'sans-serif'],
                        'display': ['Poppins', 'system-ui', 'sans-serif'],
                    },
                    animation: {
                        'fade-in': 'fadeIn 0.5s ease-in-out',
                        'slide-up': 'slideUp 0.3s ease-out',
                        'bounce-gentle': 'bounceGentle 2s infinite',
                        'pulse-slow': 'pulse 3s infinite',
                    },
                    keyframes: {
                        fadeIn: {
                            '0%': { opacity: '0' },
                            '100%': { opacity: '1' },
                        },
                        slideUp: {
                            '0%': { transform: 'translateY(10px)', opacity: '0' },
                            '100%': { transform: 'translateY(0)', opacity: '1' },
                        },
                        bounceGentle: {
                            '0%, 100%': { transform: 'translateY(0)' },
                            '50%': { transform: 'translateY(-5px)' },
                        },
                    },
                    boxShadow: {
                        'glow': '0 0 20px rgba(14, 165, 233, 0.3)',
                        'glow-lg': '0 0 40px rgba(14, 165, 233, 0.4)',
                        'dark-lg': '0 10px 25px rgba(0, 0, 0, 0.3)',
                    }
                }
            }
        }
    </script>

    <!-- Alpine.js -->
    <script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=Poppins:wght@400;500;600;700;800&display=swap" rel="stylesheet">

    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css">

    <!-- Leaflet CSS -->
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />

    {% load static %}
    <link rel="stylesheet" href="{% static 'css/custom.css' %}">
    <link rel="stylesheet" href="{% static 'css/nestria-logo.css' %}">
    <link rel="stylesheet" href="{% static 'css/animations.css' %}">
    <link rel="stylesheet" href="{% static 'css/favorites.css' %}">

    {% block extra_css %}{% endblock %}
</head>
<body class="font-sans bg-white dark:bg-dark-900 text-gray-900 dark:text-gray-100 transition-colors duration-300" data-user-authenticated="{% if user.is_authenticated %}true{% else %}false{% endif %}"
      x-data="{ mobileMenuOpen: false }"
      :class="{ 'overflow-hidden': mobileMenuOpen }">
    <!-- Navigation -->
    <nav class="bg-white/80 dark:bg-dark-900/80 backdrop-blur-lg border-b border-gray-200 dark:border-dark-700 sticky top-0 z-50 transition-colors duration-300">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <!-- Logo -->
                <div class="flex-shrink-0">
                    <a href="{% url 'core:home' %}" class="flex items-center group">
                        <!-- Logo Nestria - Image exacte -->
                        <div class="relative">
                            <img src="{% static 'images/nestria-logo-transparent.png' %}"
                                 alt="Nestria Logo"
                                 class="w-12 h-12 object-contain nestria-logo-img">
                        </div>
                        <div class="ml-3">
                            <span class="text-2xl font-display font-bold text-gray-900 dark:text-white">
                                NESTRIA
                            </span>
                            <div class="text-xs text-gray-500 dark:text-gray-400 font-medium">
                                Travel to Survive
                            </div>
                        </div>
                    </a>
                </div>
                
                <!-- Desktop Navigation -->
                <div class="hidden md:block">
                    <div class="ml-10 flex items-center space-x-1">
                        <a href="{% url 'core:home' %}" class="text-gray-700 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400 px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 hover:bg-gray-100 dark:hover:bg-dark-800">
                            <i class="fas fa-home mr-2"></i>{{ translations.home|default:"Home" }}
                        </a>
                        <a href="{% url 'rooms:destinations' %}" class="text-gray-700 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400 px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 hover:bg-gray-100 dark:hover:bg-dark-800">
                            <i class="fas fa-map-marked-alt mr-2"></i>{{ translations.destinations|default:"Destinations" }}
                        </a>
                        <a href="{% url 'core:experiences' %}" class="text-gray-700 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400 px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 hover:bg-gray-100 dark:hover:bg-dark-800">
                            <i class="fas fa-star mr-2"></i>{{ translations.experiences|default:"Experiences" }}
                        </a>
                        <a href="{% url 'core:online' %}" class="text-gray-700 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400 px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 hover:bg-gray-100 dark:hover:bg-dark-800">
                            <i class="fas fa-video mr-2"></i>{{ translations.online|default:"Online" }}
                        </a>
                    </div>
                </div>
                
                <!-- Right side controls -->
                <div class="hidden md:flex items-center space-x-4">
                    <!-- Dark Mode Toggle -->
                    <button @click="darkMode = !darkMode"
                            class="p-2 rounded-lg bg-gray-100 dark:bg-dark-800 text-gray-600 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-dark-700 transition-all duration-200 hover:scale-105"
                            :class="{ 'bg-primary-100 text-primary-600': darkMode }">
                        <i class="fas fa-moon" x-show="!darkMode"></i>
                        <i class="fas fa-sun" x-show="darkMode"></i>
                    </button>

                    <!-- User Menu -->
                    {% if user.is_authenticated %}
                        <div class="relative" x-data="{ open: false }">
                            <button @click="open = !open" class="flex items-center space-x-2 text-sm rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 p-2 hover:bg-gray-100 dark:hover:bg-dark-800 transition-all duration-200">
                                <div class="h-8 w-8 rounded-lg bg-gradient-to-br from-primary-500 to-secondary-500 flex items-center justify-center shadow-md">
                                    <span class="text-white font-medium text-sm">{{ user.first_name.0|default:user.username.0 }}</span>
                                </div>
                                <span class="hidden lg:block text-gray-700 dark:text-gray-300 font-medium">{{ user.first_name|default:user.username }}</span>
                                <i class="fas fa-chevron-down text-xs text-gray-500"></i>
                            </button>

                            <div x-show="open" @click.away="open = false" x-transition class="origin-top-right absolute right-0 mt-2 w-56 rounded-xl shadow-lg bg-white dark:bg-dark-800 ring-1 ring-black ring-opacity-5 border dark:border-dark-700">
                                <div class="py-2">
                                    <div class="px-4 py-3 border-b dark:border-dark-700">
                                        <p class="text-sm font-medium text-gray-900 dark:text-gray-100">{{ user.first_name }} {{ user.last_name }}</p>
                                        <p class="text-sm text-gray-500 dark:text-gray-400">{{ user.email }}</p>
                                    </div>
                                    <a href="{% url 'core:profile' %}" class="flex items-center px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-dark-700 transition-colors">
                                        <i class="fas fa-user mr-3 text-gray-400"></i>Your Profile
                                    </a>
                                    <a href="{% url 'core:your_trips' %}" class="flex items-center px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-dark-700 transition-colors">
                                        <i class="fas fa-suitcase mr-3 text-gray-400"></i>Your Trips
                                    </a>
                                    <a href="{% url 'core:favorites' %}" class="flex items-center px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-dark-700 transition-colors">
                                        <i class="fas fa-heart mr-3 text-gray-400"></i>Favorites
                                    </a>
                                    <a href="{% url 'core:settings' %}" class="flex items-center px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-dark-700 transition-colors">
                                        <i class="fas fa-cog mr-3 text-gray-400"></i>Settings
                                    </a>
                                    <div class="border-t dark:border-dark-700 mt-2 pt-2">
                                        <a href="{% url 'core:logout' %}" class="flex items-center px-4 py-2 text-sm text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/20 transition-colors">
                                            <i class="fas fa-sign-out-alt mr-3"></i>Sign out
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    {% else %}
                        <div class="flex items-center space-x-3">
                            <a href="{% url 'core:modern_login' %}" class="bg-gradient-to-r from-green-500 to-blue-600 hover:from-green-600 hover:to-blue-700 text-white px-6 py-2 rounded-lg text-sm font-medium transition-all duration-200 shadow-md hover:shadow-lg hover:scale-105">
                                Login
                            </a>
                        </div>
                    {% endif %}
                </div>
                
                <!-- Mobile menu button -->
                <div class="md:hidden flex items-center space-x-2">
                    <!-- Mobile Dark Mode Toggle -->
                    <button @click="darkMode = !darkMode"
                            class="p-2 rounded-lg bg-gray-100 dark:bg-dark-800 text-gray-600 dark:text-gray-300 transition-all duration-200">
                        <i class="fas fa-moon text-sm" x-show="!darkMode"></i>
                        <i class="fas fa-sun text-sm" x-show="darkMode"></i>
                    </button>

                    <button @click="mobileMenuOpen = !mobileMenuOpen" class="text-gray-700 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400 focus:outline-none p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-dark-800 transition-all duration-200">
                        <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path x-show="!mobileMenuOpen" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
                            <path x-show="mobileMenuOpen" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>
            </div>
        </div>
        
        <!-- Mobile menu -->
        <div x-show="mobileMenuOpen" @click.away="mobileMenuOpen = false" x-transition class="md:hidden">
            <div class="px-4 pt-4 pb-6 space-y-2 bg-white/95 dark:bg-dark-900/95 backdrop-blur-lg border-t border-gray-200 dark:border-dark-700">
                <a href="{% url 'core:home' %}" class="flex items-center text-gray-700 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400 hover:bg-gray-100 dark:hover:bg-dark-800 px-4 py-3 rounded-lg text-base font-medium transition-all duration-200">
                    <i class="fas fa-home mr-3"></i>Home
                </a>
                <a href="{% url 'rooms:destinations' %}" class="flex items-center text-gray-700 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400 hover:bg-gray-100 dark:hover:bg-dark-800 px-4 py-3 rounded-lg text-base font-medium transition-all duration-200">
                    <i class="fas fa-map-marked-alt mr-3"></i>Destinations
                </a>
                <a href="{% url 'core:experiences' %}" class="flex items-center text-gray-700 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400 hover:bg-gray-100 dark:hover:bg-dark-800 px-4 py-3 rounded-lg text-base font-medium transition-all duration-200">
                    <i class="fas fa-star mr-3"></i>Experiences
                </a>
                <a href="{% url 'core:online' %}" class="flex items-center text-gray-700 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400 hover:bg-gray-100 dark:hover:bg-dark-800 px-4 py-3 rounded-lg text-base font-medium transition-all duration-200">
                    <i class="fas fa-video mr-3"></i>Online
                </a>

                {% if user.is_authenticated %}
                    <div class="border-t dark:border-dark-700 pt-4 mt-4">
                        <div class="flex items-center px-4 py-3 bg-gray-50 dark:bg-dark-800 rounded-lg mb-3">
                            <div class="h-10 w-10 rounded-lg bg-gradient-to-br from-primary-500 to-secondary-500 flex items-center justify-center shadow-md mr-3">
                                <span class="text-white font-medium">{{ user.first_name.0|default:user.username.0 }}</span>
                            </div>
                            <div>
                                <p class="text-sm font-medium text-gray-900 dark:text-gray-100">{{ user.first_name }} {{ user.last_name }}</p>
                                <p class="text-xs text-gray-500 dark:text-gray-400">{{ user.email }}</p>
                            </div>
                        </div>
                        <a href="{% url 'core:profile' %}" class="flex items-center text-gray-700 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400 hover:bg-gray-100 dark:hover:bg-dark-800 px-4 py-3 rounded-lg text-base font-medium transition-all duration-200">
                            <i class="fas fa-user mr-3"></i>Profile
                        </a>
                        <a href="{% url 'core:your_trips' %}" class="flex items-center text-gray-700 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400 hover:bg-gray-100 dark:hover:bg-dark-800 px-4 py-3 rounded-lg text-base font-medium transition-all duration-200">
                            <i class="fas fa-suitcase mr-3"></i>Your Trips
                        </a>
                        <a href="{% url 'core:favorites' %}" class="flex items-center text-gray-700 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400 hover:bg-gray-100 dark:hover:bg-dark-800 px-4 py-3 rounded-lg text-base font-medium transition-all duration-200">
                            <i class="fas fa-heart mr-3"></i>Favorites
                        </a>
                        <a href="{% url 'core:logout' %}" class="flex items-center text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/20 px-4 py-3 rounded-lg text-base font-medium transition-all duration-200">
                            <i class="fas fa-sign-out-alt mr-3"></i>Logout
                        </a>
                    </div>
                {% else %}
                    <div class="border-t dark:border-dark-700 pt-4 mt-4 space-y-2">
                        <a href="{% url 'core:modern_login' %}" class="flex items-center justify-center bg-gradient-to-r from-green-500 to-blue-600 hover:from-green-600 hover:to-blue-700 text-white px-4 py-3 rounded-lg text-base font-medium transition-all duration-200 shadow-md">
                            Login
                        </a>
                    </div>
                {% endif %}
            </div>
        </div>
    </nav>
    

    
    <!-- Main Content -->
    <main>
        {% block content %}{% endblock %}
    </main>
    
    <!-- Footer -->
    <footer class="bg-gray-50 dark:bg-dark-800 border-t border-gray-200 dark:border-dark-700 mt-16 transition-colors duration-300">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
            <!-- Logo et description -->
            <div class="mb-12 text-center">
                <div class="flex items-center justify-center mb-4">
                    <!-- Logo Nestria pour le footer -->
                    <img src="{% static 'images/nestria-logo-transparent.png' %}"
                         alt="Nestria Logo"
                         class="w-20 h-20 object-contain mr-4 nestria-logo-img">
                    <span class="text-4xl font-display font-bold text-gray-900 dark:text-white">
                        NESTRIA
                    </span>
                </div>
                <p class="text-gray-600 dark:text-gray-400 max-w-md mx-auto">
                    Travel to survive and discover amazing places around the world. Your perfect adventure starts here.
                </p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                <div>
                    <h3 class="text-sm font-semibold text-gray-900 dark:text-gray-100 uppercase tracking-wider mb-4">Support</h3>
                    <ul class="space-y-3">
                        <li><a href="#" class="text-gray-600 dark:text-gray-400 hover:text-primary-600 dark:hover:text-primary-400 transition-colors">Help Center</a></li>
                        <li><a href="#" class="text-gray-600 dark:text-gray-400 hover:text-primary-600 dark:hover:text-primary-400 transition-colors">Safety information</a></li>
                        <li><a href="#" class="text-gray-600 dark:text-gray-400 hover:text-primary-600 dark:hover:text-primary-400 transition-colors">Cancellation options</a></li>
                        <li><a href="#" class="text-gray-600 dark:text-gray-400 hover:text-primary-600 dark:hover:text-primary-400 transition-colors">Contact us</a></li>
                    </ul>
                </div>
                <div>
                    <h3 class="text-sm font-semibold text-gray-900 dark:text-gray-100 uppercase tracking-wider mb-4">Community</h3>
                    <ul class="space-y-3">
                        <li><a href="#" class="text-gray-600 dark:text-gray-400 hover:text-primary-600 dark:hover:text-primary-400 transition-colors">StayVibe.org</a></li>
                        <li><a href="#" class="text-gray-600 dark:text-gray-400 hover:text-primary-600 dark:hover:text-primary-400 transition-colors">Diversity & Belonging</a></li>
                        <li><a href="#" class="text-gray-600 dark:text-gray-400 hover:text-primary-600 dark:hover:text-primary-400 transition-colors">Against Discrimination</a></li>
                        <li><a href="#" class="text-gray-600 dark:text-gray-400 hover:text-primary-600 dark:hover:text-primary-400 transition-colors">Accessibility</a></li>
                    </ul>
                </div>
                <div>
                    <h3 class="text-sm font-semibold text-gray-900 dark:text-gray-100 uppercase tracking-wider mb-4">Hosting</h3>
                    <ul class="space-y-3">
                        <li><a href="#" class="text-gray-600 dark:text-gray-400 hover:text-primary-600 dark:hover:text-primary-400 transition-colors">Host your home</a></li>
                        <li><a href="#" class="text-gray-600 dark:text-gray-400 hover:text-primary-600 dark:hover:text-primary-400 transition-colors">Host an experience</a></li>
                        <li><a href="#" class="text-gray-600 dark:text-gray-400 hover:text-primary-600 dark:hover:text-primary-400 transition-colors">Responsible hosting</a></li>
                        <li><a href="#" class="text-gray-600 dark:text-gray-400 hover:text-primary-600 dark:hover:text-primary-400 transition-colors">Resource center</a></li>
                    </ul>
                </div>
                <div>
                    <h3 class="text-sm font-semibold text-gray-900 dark:text-gray-100 uppercase tracking-wider mb-4">About</h3>
                    <ul class="space-y-3">
                        <li><a href="#" class="text-gray-600 dark:text-gray-400 hover:text-primary-600 dark:hover:text-primary-400 transition-colors">Newsroom</a></li>
                        <li><a href="#" class="text-gray-600 dark:text-gray-400 hover:text-primary-600 dark:hover:text-primary-400 transition-colors">New features</a></li>
                        <li><a href="#" class="text-gray-600 dark:text-gray-400 hover:text-primary-600 dark:hover:text-primary-400 transition-colors">Careers</a></li>
                        <li><a href="#" class="text-gray-600 dark:text-gray-400 hover:text-primary-600 dark:hover:text-primary-400 transition-colors">Investors</a></li>
                    </ul>
                </div>
            </div>

            <!-- Social Media & Copyright -->
            <div class="mt-12 pt-8 border-t border-gray-200 dark:border-dark-700">
                <div class="flex flex-col md:flex-row justify-between items-center">
                    <div class="flex space-x-6 mb-4 md:mb-0">
                        <a href="#" class="text-gray-400 hover:text-primary-500 transition-colors">
                            <i class="fab fa-facebook-f text-xl"></i>
                        </a>
                        <a href="#" class="text-gray-400 hover:text-primary-500 transition-colors">
                            <i class="fab fa-twitter text-xl"></i>
                        </a>
                        <a href="#" class="text-gray-400 hover:text-primary-500 transition-colors">
                            <i class="fab fa-instagram text-xl"></i>
                        </a>
                        <a href="#" class="text-gray-400 hover:text-primary-500 transition-colors">
                            <i class="fab fa-linkedin text-xl"></i>
                        </a>
                    </div>
                    <p class="text-gray-500 dark:text-gray-400 text-center">
                        &copy; 2024 <span class="font-semibold text-blue-600">Nestria</span>. All rights reserved.
                    </p>
                </div>
            </div>
        </div>
    </footer>
    
    <!-- Leaflet JavaScript -->
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>

    <script src="{% static 'js/main.js' %}"></script>
    <script src="{% static 'js/page-transitions.js' %}"></script>
    <script src="{% static 'js/favorites.js' %}"></script>
    {% block extra_js %}{% endblock %}
</body>
</html>



