#!/usr/bin/env python
"""
Script pour créer des utilisateurs de test dans la base de données
"""
import os
import django

# Configuration Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'airbnb_clone.settings')
django.setup()

from django.contrib.auth.models import User
from core.models import UserProfile

def create_test_users():
    """Créer des utilisateurs de test"""
    
    test_users = [
        {
            'email': '<EMAIL>',
            'password': 'password123',
            'first_name': 'Test',
            'last_name': 'User',
        },
        {
            'email': '<EMAIL>',
            'password': 'admin123',
            'first_name': 'Admin',
            'last_name': 'Nestria',
        },
        {
            'email': '<EMAIL>',
            'password': 'demo123',
            'first_name': 'Demo',
            'last_name': 'Account',
        }
    ]
    
    for user_data in test_users:
        email = user_data['email']
        
        # Vérifier si l'utilisateur existe déjà
        if User.objects.filter(email=email).exists():
            print(f"ℹ️  User {email} already exists")
            continue
        
        # Créer l'utilisateur
        user = User.objects.create_user(
            username=email,
            email=email,
            password=user_data['password'],
            first_name=user_data['first_name'],
            last_name=user_data['last_name']
        )
        
        # Créer le profil utilisateur
        UserProfile.objects.get_or_create(user=user)
        
        print(f"✅ Created user: {email} (password: {user_data['password']})")
    
    print("\n🎯 Test users created successfully!")
    print("\n📝 You can now login with:")
    print("- <EMAIL> / password123")
    print("- <EMAIL> / admin123") 
    print("- <EMAIL> / demo123")

if __name__ == '__main__':
    create_test_users()
