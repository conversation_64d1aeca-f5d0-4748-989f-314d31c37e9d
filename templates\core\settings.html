{% extends 'base.html' %}
{% load static %}

{% block title %}Settings - Nestria{% endblock %}

{% csrf_token %}

{% block content %}
<div class="min-h-screen bg-gray-50 dark:bg-gray-900 py-8">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-gray-900 dark:text-white mb-2">{{ translations.settings|default:"Settings" }}</h1>
            <p class="text-gray-600 dark:text-gray-400">Manage your account preferences</p>
        </div>

        <div class="space-y-6">
            <!-- Profile Information -->
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6">
                <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">{{ translations.profile_information|default:"Profile Information" }}</h2>
                <form method="post" class="space-y-4">
                    {% csrf_token %}
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                First Name
                            </label>
                            <input type="text" 
                                   value="{{ user.first_name }}"
                                   class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                Last Name
                            </label>
                            <input type="text" 
                                   value="{{ user.last_name }}"
                                   class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white">
                        </div>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Email
                        </label>
                        <input type="email" 
                               value="{{ user.email }}"
                               class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white">
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Phone Number
                        </label>
                        <input type="tel"
                               name="phone_number"
                               value="{{ user.profile.phone_number }}"
                               placeholder="+****************"
                               class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white">
                    </div>



                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                City
                            </label>
                            <input type="text"
                                   name="city"
                                   value="{{ user.profile.city }}"
                                   placeholder="Your city"
                                   class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                Country
                            </label>
                            <input type="text"
                                   name="country"
                                   value="{{ user.profile.country }}"
                                   placeholder="Your country"
                                   class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white">
                        </div>
                    </div>
                    
                    <button type="submit" 
                            class="px-6 py-2 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg transition-colors">
                        Save Changes
                    </button>
                </form>
            </div>

            <!-- Notification Preferences -->
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6">
                <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">{{ translations.notification_preferences|default:"Notification Preferences" }}</h2>
                <div class="space-y-4">
                    <div class="flex items-center justify-between">
                        <div>
                            <h3 class="font-medium text-gray-900 dark:text-white">Email Notifications</h3>
                            <p class="text-sm text-gray-600 dark:text-gray-400">Receive booking confirmations and updates</p>
                        </div>
                        <label class="relative inline-flex items-center cursor-pointer">
                            <input type="checkbox" checked class="sr-only peer">
                            <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
                        </label>
                    </div>
                    
                    <div class="flex items-center justify-between">
                        <div>
                            <h3 class="font-medium text-gray-900 dark:text-white">Marketing Emails</h3>
                            <p class="text-sm text-gray-600 dark:text-gray-400">Receive special offers and travel inspiration</p>
                        </div>
                        <label class="relative inline-flex items-center cursor-pointer">
                            <input type="checkbox" class="sr-only peer">
                            <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
                        </label>
                    </div>
                </div>
            </div>

            <!-- Privacy & Security -->
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6">
                <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">{{ translations.privacy_security|default:"Privacy & Security" }}</h2>
                <div class="space-y-4">
                    <a href="{% url 'core:change_password' %}" class="block w-full text-left p-4 border border-gray-200 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                        <div class="flex items-center justify-between">
                            <div>
                                <h3 class="font-medium text-gray-900 dark:text-white">Change Password</h3>
                                <p class="text-sm text-gray-600 dark:text-gray-400">Update your account password</p>
                            </div>
                            <i class="fas fa-chevron-right text-gray-400"></i>
                        </div>
                    </a>

                    <!-- Language Settings -->
                    <div class="p-4 border border-gray-200 dark:border-gray-600 rounded-lg">
                        <div class="flex items-center justify-between">
                            <div>
                                <h3 class="font-medium text-gray-900 dark:text-white">{{ translations.language_region|default:"Language & Region" }}</h3>
                                <p class="text-sm text-gray-600 dark:text-gray-400">Choose your preferred language</p>
                            </div>
                            <div class="flex items-center space-x-3">
                                <select id="languageSelect" class="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg text-sm dark:bg-gray-700 dark:text-white">
                                    <option value="en" {% if user.profile.language == 'en' %}selected{% endif %}>🇺🇸 English</option>
                                    <option value="fr" {% if user.profile.language == 'fr' %}selected{% endif %}>🇫🇷 Français</option>
                                    <option value="es" {% if user.profile.language == 'es' %}selected{% endif %}>🇪🇸 Español</option>
                                    <option value="de" {% if user.profile.language == 'de' %}selected{% endif %}>🇩🇪 Deutsch</option>
                                    <option value="it" {% if user.profile.language == 'it' %}selected{% endif %}>🇮🇹 Italiano</option>
                                    <option value="pt" {% if user.profile.language == 'pt' %}selected{% endif %}>🇵🇹 Português</option>
                                    <option value="ar" {% if user.profile.language == 'ar' %}selected{% endif %}>🇸🇦 العربية</option>
                                </select>
                                <button onclick="changeLanguage()" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors">
                                    {{ translations.apply|default:"Apply" }}
                                </button>
                            </div>
                        </div>
                    </div>

                    <button onclick="alert('Two-factor authentication feature coming soon!')" class="w-full text-left p-4 border border-gray-200 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                        <div class="flex items-center justify-between">
                            <div>
                                <h3 class="font-medium text-gray-900 dark:text-white">Two-Factor Authentication</h3>
                                <p class="text-sm text-gray-600 dark:text-gray-400">Add an extra layer of security</p>
                            </div>
                            <i class="fas fa-chevron-right text-gray-400"></i>
                        </div>
                    </button>
                </div>
            </div>

            <!-- Email Test Section -->
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6">
                <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">Test Email System</h2>
                <div class="space-y-4">
                    <p class="text-sm text-gray-600 dark:text-gray-400">
                        Test the email system to ensure it's working correctly.
                    </p>
                    <div class="flex items-center space-x-3">
                        <input type="email" id="testEmailInput" placeholder="Enter email address"
                               value="{% if user.is_authenticated %}{{ user.email }}{% endif %}"
                               class="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg text-sm dark:bg-gray-700 dark:text-white">
                        <button onclick="sendTestEmail()" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors">
                            Send Test Email
                        </button>
                    </div>
                </div>
            </div>

            <!-- Danger Zone -->
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 border-l-4 border-red-500">
                <h2 class="text-xl font-semibold text-red-600 dark:text-red-400 mb-4">Danger Zone</h2>
                <div class="space-y-4">
                    <a href="{% url 'core:delete_account' %}" class="inline-block px-6 py-2 bg-red-600 hover:bg-red-700 text-white font-medium rounded-lg transition-colors">
                        <i class="fas fa-trash mr-2"></i>
                        Delete Account
                    </a>
                    <p class="text-sm text-gray-600 dark:text-gray-400">
                        Once you delete your account, there is no going back. Please be certain.
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function changeLanguage() {
    const languageSelect = document.getElementById('languageSelect');
    const selectedLanguage = languageSelect.value;

    // Show loading state
    const button = document.querySelector('button[onclick="changeLanguage()"]');
    const originalText = button.textContent;
    button.textContent = 'Applying...';
    button.disabled = true;

    // Make API call to change language
    fetch('{% url "core:set_language" %}', {
        method: 'POST',
        headers: {
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: `language=${selectedLanguage}`
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Show success message
            showNotification(data.message, 'success');

            // Reload page to apply language changes
            setTimeout(() => {
                window.location.reload();
            }, 1000);
        } else {
            showNotification(data.message, 'error');
            button.textContent = originalText;
            button.disabled = false;
        }
    })
    .catch(error => {
        showNotification('An error occurred while changing language.', 'error');
        button.textContent = originalText;
        button.disabled = false;
    });
}

// Test email function
function sendTestEmail() {
    const emailInput = document.getElementById('testEmailInput');
    const email = emailInput.value.trim();

    if (!email) {
        showNotification('Please enter an email address', 'warning');
        return;
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
        showNotification('Please enter a valid email address', 'error');
        return;
    }

    // Show loading state
    const button = document.querySelector('button[onclick="sendTestEmail()"]');
    const originalText = button.textContent;
    button.textContent = 'Sending...';
    button.disabled = true;

    // Get CSRF token
    const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]')?.value ||
                     getCookie('csrftoken');

    // Send test email
    fetch('/send-test-email/', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
            'X-CSRFToken': csrfToken,
        },
        body: `email=${encodeURIComponent(email)}`
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification(`Test email sent to ${email}! Check console for development mode.`, 'success', 5000);
        } else {
            showNotification(data.error || 'Failed to send test email', 'error');
        }
    })
    .catch(error => {
        console.error('Error sending test email:', error);
        showNotification('Network error. Please try again.', 'error');
    })
    .finally(() => {
        // Restore button state
        button.textContent = originalText;
        button.disabled = false;
    });
}

function getCookie(name) {
    let cookieValue = null;
    if (document.cookie && document.cookie !== '') {
        const cookies = document.cookie.split(';');
        for (let i = 0; i < cookies.length; i++) {
            const cookie = cookies[i].trim();
            if (cookie.substring(0, name.length + 1) === (name + '=')) {
                cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                break;
            }
        }
    }
    return cookieValue;
}

// Notification function
function showNotification(message, type) {
    const notification = document.createElement('div');
    notification.className = `fixed top-4 right-4 z-50 px-6 py-3 rounded-lg text-white font-medium transition-all duration-300 transform translate-x-full ${
        type === 'success' ? 'bg-green-500' : 'bg-red-500'
    }`;
    notification.textContent = message;

    document.body.appendChild(notification);

    // Animate in
    setTimeout(() => {
        notification.classList.remove('translate-x-full');
    }, 100);

    // Animate out and remove
    setTimeout(() => {
        notification.classList.add('translate-x-full');
        setTimeout(() => {
            if (document.body.contains(notification)) {
                document.body.removeChild(notification);
            }
        }, 300);
    }, 3000);
}
</script>
{% endblock %}
