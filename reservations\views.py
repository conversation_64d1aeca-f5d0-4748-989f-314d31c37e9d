from django.shortcuts import render, get_object_or_404, redirect
from django.views.generic import TemplateView, ListView
from django.contrib.auth.mixins import LoginRequiredMixin
from django.contrib import messages
from django.core.mail import send_mail
from django.template.loader import render_to_string
from django.utils.html import strip_tags
from django.conf import settings
from django.urls import reverse
from django.http import Http404, JsonResponse
from django.views.decorators.http import require_http_methods
from django.contrib.auth.decorators import login_required
from datetime import datetime
import uuid
from rooms.models import Room
from .models import Reservation
from core.pdf_service import generate_reservation_pdf


class CheckoutView(LoginRequiredMixin, TemplateView):
    """Checkout view for booking a room"""
    template_name = 'reservations/checkout.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        room = get_object_or_404(Room, pk=kwargs['room_id'], is_active=True)
        context['room'] = room
        return context

    def post(self, request, *args, **kwargs):
        """Handle booking form submission"""
        room = get_object_or_404(Room, pk=kwargs['room_id'], is_active=True)

        # Get form data
        check_in = request.POST.get('check_in')
        check_out = request.POST.get('check_out')
        adults = int(request.POST.get('adults', 1))
        children = int(request.POST.get('children', 0))
        infants = int(request.POST.get('infants', 0))
        guest_first_name = request.POST.get('guest_first_name')
        guest_last_name = request.POST.get('guest_last_name')
        guest_email = request.POST.get('guest_email')
        guest_phone = request.POST.get('guest_phone', '')
        special_requests = request.POST.get('special_requests', '')
        payment_method = request.POST.get('payment_method', 'card')

        try:
            # Parse dates
            check_in_date = datetime.strptime(check_in, '%Y-%m-%d').date()
            check_out_date = datetime.strptime(check_out, '%Y-%m-%d').date()

            # Calculate nights and prices
            nights = (check_out_date - check_in_date).days
            base_price = room.price_per_night * nights
            total_price = base_price + room.cleaning_fee + room.service_fee

            # Create reservation
            reservation = Reservation.objects.create(
                room=room,
                guest=request.user,
                check_in=check_in_date,
                check_out=check_out_date,
                adults=adults,
                children=children,
                infants=infants,
                guest_first_name=guest_first_name,
                guest_last_name=guest_last_name,
                guest_email=guest_email,
                guest_phone=guest_phone,
                special_requests=special_requests,
                payment_method=payment_method,
                base_price=base_price,
                total_price=total_price,
                confirmation_number=str(uuid.uuid4())[:8].upper()
            )

            # Send confirmation email
            self.send_confirmation_email(reservation, room, request)

            messages.success(request, f'Booking confirmed! Confirmation number: {reservation.confirmation_number}')
            return redirect('reservations:confirmation', reservation_id=reservation.id)

        except Exception as e:
            messages.error(request, f'Booking failed: {str(e)}')
            return self.get(request, *args, **kwargs)

    def send_confirmation_email(self, reservation, room, request):
        """Send booking confirmation email"""
        try:
            # Prepare context for email template
            context = {
                'reservation': reservation,
                'room': room,
                'guest_name': f"{reservation.guest_first_name} {reservation.guest_last_name}",
                'guest_email': reservation.guest_email,
                'property_url': request.build_absolute_uri(
                    reverse('rooms:room_detail', kwargs={'pk': room.id})
                ),
                'request': request,  # Pour les URLs absolues dans les templates
            }

            # Render email templates
            html_message = render_to_string('emails/booking_confirmed.html', context)
            plain_message = render_to_string('emails/booking_confirmation.txt', context)

            # Send email
            send_mail(
                subject=f'Booking Confirmation - {room.title}',
                message=plain_message,
                from_email=settings.DEFAULT_FROM_EMAIL,
                recipient_list=[reservation.guest_email],
                html_message=html_message,
                fail_silently=False,
            )

        except Exception as e:
            print(f"Failed to send confirmation email: {e}")
            # Don't fail the booking if email fails


class ConfirmationView(LoginRequiredMixin, TemplateView):
    """Booking confirmation view"""
    template_name = 'reservations/confirmation.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        reservation = get_object_or_404(Reservation, pk=kwargs['reservation_id'], guest=self.request.user)
        context['reservation'] = reservation
        return context


class MyTripsView(LoginRequiredMixin, ListView):
    """User's trips/reservations view"""
    model = Reservation
    template_name = 'reservations/my_trips.html'
    context_object_name = 'reservations'
    paginate_by = 10

    def get_queryset(self):
        return Reservation.objects.filter(guest=self.request.user).select_related('room').order_by('-created_at')


def download_reservation_pdf(request, reservation_id):
    """Vue pour télécharger le PDF de confirmation de réservation"""
    try:
        # Récupérer la réservation
        reservation = get_object_or_404(
            Reservation,
            id=reservation_id,
            guest=request.user
        )

        # Générer et retourner le PDF
        return generate_reservation_pdf(reservation)

    except Reservation.DoesNotExist:
        raise Http404("Réservation non trouvée")
    except Exception as e:
        messages.error(request, f"Erreur lors de la génération du PDF: {str(e)}")
        return redirect('reservations:confirmation', reservation_id=reservation_id)


@require_http_methods(["POST"])
@login_required
def cancel_reservation(request, reservation_id):
    """Cancel a reservation if within 48h policy"""
    try:
        reservation = get_object_or_404(
            Reservation,
            id=reservation_id,
            guest=request.user
        )

        # Check if cancellation is allowed
        if not reservation.can_cancel:
            return JsonResponse({
                'success': False,
                'error': 'Cancellation not allowed. Must be more than 48 hours before check-in.'
            })

        # Update reservation status
        reservation.status = 'cancelled'
        reservation.save()

        # Send cancellation email (optional)
        try:
            send_cancellation_email(reservation)
        except Exception as e:
            print(f"Failed to send cancellation email: {e}")

        return JsonResponse({
            'success': True,
            'message': 'Reservation cancelled successfully'
        })

    except Reservation.DoesNotExist:
        return JsonResponse({
            'success': False,
            'error': 'Reservation not found'
        })
    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': str(e)
        })


def send_cancellation_email(reservation):
    """Send cancellation confirmation email"""
    try:
        context = {
            'reservation': reservation,
            'room': reservation.room,
            'guest_name': f"{reservation.guest_first_name} {reservation.guest_last_name}",
        }

        html_message = render_to_string('emails/booking_cancelled.html', context)
        plain_message = f"Your reservation for {reservation.room.title} has been cancelled. Confirmation: {reservation.confirmation_number}"

        send_mail(
            subject=f'Reservation Cancelled - {reservation.room.title}',
            message=plain_message,
            from_email=settings.DEFAULT_FROM_EMAIL,
            recipient_list=[reservation.guest_email],
            html_message=html_message,
            fail_silently=False,
        )
    except Exception as e:
        print(f"Failed to send cancellation email: {e}")
