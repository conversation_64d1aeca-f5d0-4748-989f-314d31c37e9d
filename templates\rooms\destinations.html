{% extends 'base.html' %}
{% load static %}

{% block title %}Destinations - Airbnb Clone{% endblock %}

{% block content %}
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Header -->
    <div class="text-center mb-12">
        <h1 class="text-4xl font-bold text-gray-900 mb-4">Discover Amazing Destinations</h1>
        <p class="text-xl text-gray-600 max-w-3xl mx-auto">
            Explore the world's most beautiful and historic destinations. Each location offers unique experiences, 
            rich culture, and unforgettable memories waiting to be made.
        </p>
    </div>

    <!-- Destinations Grid -->
    {% if destinations %}
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {% for destination in destinations %}
                <div class="destination-card destination-{{ destination.name|lower }} group cursor-pointer">
                    <a href="{{ destination.get_absolute_url }}" class="block">
                        <div class="relative overflow-hidden rounded-lg shadow-lg hover:shadow-xl transition-all duration-300">
                            <!-- Main Image -->
                            <div class="relative h-64 overflow-hidden destination-hero">
                                {% if destination.main_image %}
                                    <img src="{{ destination.main_image.url }}"
                                         alt="{{ destination.name }}"
                                         class="destination-image w-full h-full object-cover">
                                {% else %}
                                    <div class="w-full h-full bg-gradient-to-br from-airbnb-red to-pink-500 flex items-center justify-center">
                                        <i class="fas fa-map-marker-alt text-white text-4xl"></i>
                                    </div>
                                {% endif %}

                                <!-- Photo Count Badge -->
                                {% if destination.images.count > 0 %}
                                    <div class="absolute top-4 right-4 gallery-counter text-white">
                                        <i class="fas fa-images mr-1"></i>
                                        {{ destination.images.count }} photos
                                    </div>
                                {% endif %}

                                <!-- Content Overlay -->
                                <div class="absolute bottom-0 left-0 right-0 photo-overlay p-6 text-white">
                                    <h3 class="text-2xl font-bold mb-2">{{ destination.name }}</h3>
                                    <p class="text-sm opacity-90 mb-2">{{ destination.country }}</p>
                                    <div class="flex items-center justify-between">
                                        <div class="flex items-center text-sm">
                                            <i class="fas fa-home mr-2"></i>
                                            <span>{{ destination.room_count }} propert{{ destination.room_count|pluralize:"y,ies" }}</span>
                                        </div>
                                        {% if destination.latitude and destination.longitude %}
                                            <div class="flex items-center text-sm opacity-75">
                                                <i class="fas fa-map-marker-alt mr-1"></i>
                                                <span>{{ destination.latitude|floatformat:1 }}°, {{ destination.longitude|floatformat:1 }}°</span>
                                            </div>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Description -->
                        <div class="mt-4">
                            <p class="text-gray-600 text-sm leading-relaxed">
                                {{ destination.description|truncatewords:20 }}
                            </p>
                            {% if destination.images.count > 0 %}
                                <div class="mt-3 flex items-center text-xs text-gray-500">
                                    <i class="fas fa-camera mr-1"></i>
                                    <span>Professional photography available</span>
                                </div>
                            {% endif %}

                            <!-- View Hotels Button -->
                            <div class="mt-4 pt-3 border-t border-gray-200">
                                <a href="{% url 'rooms:city_hotels' destination.name %}"
                                   class="inline-flex items-center text-sm font-medium text-airbnb-red hover:text-red-600 transition-colors group">
                                    <i class="fas fa-hotel mr-2"></i>
                                    View Hotels in {{ destination.name }}
                                    <i class="fas fa-arrow-right ml-2 transform group-hover:translate-x-1 transition-transform"></i>
                                </a>
                            </div>
                        </div>
                    </a>
                </div>
            {% endfor %}
        </div>

        <!-- Pagination -->
        {% if is_paginated %}
            <div class="mt-12 flex justify-center">
                <nav class="flex items-center space-x-2">
                    {% if page_obj.has_previous %}
                        <a href="?page={{ page_obj.previous_page_number }}" 
                           class="px-3 py-2 text-sm font-medium text-gray-500 hover:text-gray-700 border border-gray-300 rounded-md hover:bg-gray-50">
                            Previous
                        </a>
                    {% endif %}
                    
                    {% for num in page_obj.paginator.page_range %}
                        {% if page_obj.number == num %}
                            <span class="px-3 py-2 text-sm font-medium text-white bg-airbnb-red border border-airbnb-red rounded-md">
                                {{ num }}
                            </span>
                        {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                            <a href="?page={{ num }}" 
                               class="px-3 py-2 text-sm font-medium text-gray-500 hover:text-gray-700 border border-gray-300 rounded-md hover:bg-gray-50">
                                {{ num }}
                            </a>
                        {% endif %}
                    {% endfor %}
                    
                    {% if page_obj.has_next %}
                        <a href="?page={{ page_obj.next_page_number }}" 
                           class="px-3 py-2 text-sm font-medium text-gray-500 hover:text-gray-700 border border-gray-300 rounded-md hover:bg-gray-50">
                            Next
                        </a>
                    {% endif %}
                </nav>
            </div>
        {% endif %}
    {% else %}
        <!-- No Destinations -->
        <div class="text-center py-12">
            <i class="fas fa-globe text-gray-300 text-6xl mb-4"></i>
            <h3 class="text-xl font-semibold text-gray-600 mb-2">No destinations available</h3>
            <p class="text-gray-500">Check back later for amazing destinations!</p>
        </div>
    {% endif %}
</div>

<!-- Featured Destinations Section -->
<section class="bg-airbnb-light-gray py-16 mt-16">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
            <h2 class="text-3xl font-bold text-gray-900 mb-4">Why Choose These Destinations?</h2>
            <p class="text-lg text-gray-600">Each destination has been carefully selected for its unique charm and experiences</p>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div class="text-center">
                <div class="w-16 h-16 bg-airbnb-red rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-landmark text-white text-2xl"></i>
                </div>
                <h3 class="text-xl font-semibold text-gray-900 mb-2">Rich History</h3>
                <p class="text-gray-600">Discover destinations with fascinating historical backgrounds and cultural heritage.</p>
            </div>
            
            <div class="text-center">
                <div class="w-16 h-16 bg-airbnb-red rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-camera text-white text-2xl"></i>
                </div>
                <h3 class="text-xl font-semibold text-gray-900 mb-2">Stunning Views</h3>
                <p class="text-gray-600">Experience breathtaking landscapes and iconic landmarks that will leave you amazed.</p>
            </div>
            
            <div class="text-center">
                <div class="w-16 h-16 bg-airbnb-red rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-users text-white text-2xl"></i>
                </div>
                <h3 class="text-xl font-semibold text-gray-900 mb-2">Local Culture</h3>
                <p class="text-gray-600">Immerse yourself in authentic local experiences and connect with welcoming communities.</p>
            </div>
        </div>
    </div>
</section>
{% endblock %}
