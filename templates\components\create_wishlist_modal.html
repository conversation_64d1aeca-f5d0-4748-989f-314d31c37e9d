<!-- Create Wishlist Modal -->
<div id="wishlist-modal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50 hidden">
<!-- Alternative ID for compatibility -->
<div id="create-wishlist-modal" class="hidden"></div>
    <div class="bg-white rounded-2xl w-full max-w-md p-6 relative">
        <!-- Close Button -->
        <button onclick="closeWishlistModal()" class="absolute top-4 right-4 text-gray-400 hover:text-gray-600">
            <i class="fas fa-times text-xl"></i>
        </button>

        <!-- Header -->
        <div class="mb-6">
            <h2 class="text-xl font-semibold text-gray-900">Create wishlist</h2>
        </div>

        <!-- Form -->
        <form id="wishlist-form" class="space-y-6">
            {% csrf_token %}
            
            <!-- Name Input -->
            <div>
                <input type="text" 
                       name="name" 
                       id="wishlist-name"
                       placeholder="Name"
                       maxlength="50"
                       class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-nestria-red focus:border-transparent text-base"
                       required>
                <div class="flex justify-between items-center mt-2">
                    <span></span>
                    <span id="char-count" class="text-sm text-gray-500">0/50 characters</span>
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="flex space-x-3">
                <!-- Cancel Button -->
                <button type="button" 
                        onclick="closeWishlistModal()"
                        class="flex-1 bg-white text-gray-700 py-3 rounded-lg font-semibold border border-gray-300 hover:border-gray-400 transition-all">
                    Cancel
                </button>

                <!-- Create Button -->
                <button type="submit" 
                        id="create-btn"
                        class="flex-1 bg-gray-300 text-gray-500 py-3 rounded-lg font-semibold cursor-not-allowed transition-all"
                        disabled>
                    Create
                </button>
            </div>
        </form>
    </div>
</div>

<script>
// Variables globales pour la modal wishlist
const wishlistModal = document.getElementById('wishlist-modal');
const wishlistNameInput = document.getElementById('wishlist-name');
const charCount = document.getElementById('char-count');
const createBtn = document.getElementById('create-btn');
const wishlistForm = document.getElementById('wishlist-form');

// Ouvrir la modal
function openWishlistModal() {
    wishlistModal.classList.remove('hidden');
    wishlistNameInput.focus();
    document.body.style.overflow = 'hidden';
}

// Fonction globale pour compatibilité
function openCreateWishlistModal() {
    openWishlistModal();
}

// Fermer la modal
function closeWishlistModal() {
    wishlistModal.classList.add('hidden');
    wishlistForm.reset();
    updateCreateButton();
    updateCharCount();
    document.body.style.overflow = 'auto';
}

// Mettre à jour le compteur de caractères
function updateCharCount() {
    const length = wishlistNameInput.value.length;
    charCount.textContent = `${length}/50 characters`;
}

// Mettre à jour le bouton Create
function updateCreateButton() {
    const name = wishlistNameInput.value.trim();
    
    if (name.length > 0) {
        createBtn.disabled = false;
        createBtn.classList.remove('bg-gray-300', 'text-gray-500', 'cursor-not-allowed');
        createBtn.classList.add('bg-nestria-red', 'text-white', 'hover:bg-red-600');
    } else {
        createBtn.disabled = true;
        createBtn.classList.add('bg-gray-300', 'text-gray-500', 'cursor-not-allowed');
        createBtn.classList.remove('bg-nestria-red', 'text-white', 'hover:bg-red-600');
    }
}

// Event listeners
wishlistNameInput.addEventListener('input', function() {
    updateCharCount();
    updateCreateButton();
});

// Gérer la soumission du formulaire
wishlistForm.addEventListener('submit', function(e) {
    e.preventDefault();
    
    const name = wishlistNameInput.value.trim();
    
    if (!name) {
        alert('Please enter a name for your wishlist.');
        return;
    }

    // Envoyer la requête pour créer la wishlist
    fetch('/create-wishlist/', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
        },
        body: JSON.stringify({
            name: name
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Fermer la modal et afficher un message de succès
            closeWishlistModal();
            showSuccessMessage('Wishlist created successfully!');
            
            // Optionnel: recharger la page ou mettre à jour la liste des wishlists
            // window.location.reload();
        } else {
            alert(data.error || 'Error creating wishlist. Please try again.');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Error creating wishlist. Please try again.');
    });
});

// Fermer la modal en cliquant à l'extérieur
wishlistModal.addEventListener('click', function(e) {
    if (e.target === wishlistModal) {
        closeWishlistModal();
    }
});

// Fermer la modal avec la touche Escape
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape' && !wishlistModal.classList.contains('hidden')) {
        closeWishlistModal();
    }
});

// Fonction pour afficher un message de succès
function showSuccessMessage(message) {
    // Créer un toast de succès
    const toast = document.createElement('div');
    toast.className = 'fixed top-4 right-4 bg-green-500 text-white px-6 py-3 rounded-lg shadow-lg z-50 transform translate-x-full transition-transform duration-300';
    toast.textContent = message;
    
    document.body.appendChild(toast);
    
    // Animer l'entrée
    setTimeout(() => {
        toast.classList.remove('translate-x-full');
    }, 100);
    
    // Supprimer après 3 secondes
    setTimeout(() => {
        toast.classList.add('translate-x-full');
        setTimeout(() => {
            document.body.removeChild(toast);
        }, 300);
    }, 3000);
}

// Initialiser l'état des boutons
updateCreateButton();
updateCharCount();
</script>

<style>
.nestria-red {
    background-color: #FF5A5F;
}

.hover\:bg-red-600:hover {
    background-color: #dc2626;
}

.focus\:ring-nestria-red:focus {
    --tw-ring-color: #FF5A5F;
}
</style>
