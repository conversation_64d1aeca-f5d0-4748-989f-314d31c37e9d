from django.http import JsonResponse
from django.contrib.auth.decorators import login_required
from django.views.decorators.http import require_POST
from django.shortcuts import get_object_or_404
from .models import Room, Favorite


@login_required
@require_POST
def toggle_favorite(request, property_id):
    """Toggle favorite status for a property"""
    room = get_object_or_404(Room, id=property_id)
    favorite, created = Favorite.objects.get_or_create(
        user=request.user,
        room=room
    )
    
    if not created:
        # If favorite already exists, remove it
        favorite.delete()
        is_favorite = False
    else:
        # If favorite was created, it's now favorited
        is_favorite = True
    
    return JsonResponse({
        'is_favorite': is_favorite,
        'message': 'Added to favorites' if is_favorite else 'Removed from favorites'
    })
