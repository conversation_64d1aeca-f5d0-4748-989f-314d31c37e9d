/* Simple Page Animations */

/* Simple fade in animation */
@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

/* Simple slide up animation */
@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Simple page transition classes */
.page-transition {
    animation: fadeIn 0.3s ease-out;
}

.slide-up {
    animation: slideUp 0.3s ease-out;
}

/* Card animations */
.card-hover {
    transition: all 0.3s ease;
}

.card-hover:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

/* Button animations */
.btn-animate {
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.btn-animate:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.btn-animate::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.btn-animate:hover::before {
    left: 100%;
}

/* Loading animation */
@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-spinner {
    animation: spin 1s linear infinite;
}

/* Pulse animation */
@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
}

.pulse {
    animation: pulse 2s infinite;
}

/* Shake animation for errors */
@keyframes shake {
    0%, 100% { transform: translateX(0); }
    10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }
    20%, 40%, 60%, 80% { transform: translateX(5px); }
}

.shake {
    animation: shake 0.5s ease-in-out;
}

/* Smooth scrolling */
html {
    scroll-behavior: smooth;
}

/* Page loader */
.page-loader {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    opacity: 1;
    transition: opacity 0.5s ease-out;
}

.page-loader.fade-out {
    opacity: 0;
    pointer-events: none;
}

.loader-content {
    text-align: center;
    color: white;
}

.loader-logo {
    font-size: 2.5rem;
    font-weight: bold;
    margin-bottom: 1rem;
    animation: bounceIn 1s ease-out;
}

.loader-text {
    font-size: 1rem;
    opacity: 0.9;
    animation: fadeIn 1s ease-out 0.5s both;
}

.loader-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid rgba(255, 255, 255, 0.3);
    border-top: 4px solid white;
    border-radius: 50%;
    margin: 1rem auto;
    animation: spin 1s linear infinite;
}

/* Navigation animations */
.nav-item {
    transition: all 0.3s ease;
}

.nav-item:hover {
    transform: translateY(-2px);
}

/* Hero section animations */
.hero-content {
    animation: fadeIn 1s ease-out;
}

.hero-title {
    animation: slideInLeft 0.8s ease-out 0.2s both;
}

.hero-subtitle {
    animation: slideInLeft 0.8s ease-out 0.4s both;
}

.hero-buttons {
    animation: slideInLeft 0.8s ease-out 0.6s both;
}

/* Stagger animation for cards */
.stagger-item {
    opacity: 0;
    transform: translateY(30px);
    animation: fadeIn 0.6s ease-out forwards;
}

.stagger-item:nth-child(1) { animation-delay: 0.1s; }
.stagger-item:nth-child(2) { animation-delay: 0.2s; }
.stagger-item:nth-child(3) { animation-delay: 0.3s; }
.stagger-item:nth-child(4) { animation-delay: 0.4s; }
.stagger-item:nth-child(5) { animation-delay: 0.5s; }
.stagger-item:nth-child(6) { animation-delay: 0.6s; }

/* Form animations */
.form-group {
    animation: slideInRight 0.5s ease-out;
}

.form-group:nth-child(1) { animation-delay: 0.1s; }
.form-group:nth-child(2) { animation-delay: 0.2s; }
.form-group:nth-child(3) { animation-delay: 0.3s; }

/* Modal animations */
.modal-enter {
    animation: scaleIn 0.3s ease-out;
}

.modal-backdrop {
    animation: fadeIn 0.3s ease-out;
}

/* Notification animations */
.notification-enter {
    animation: slideInRight 0.3s ease-out;
}

.notification-exit {
    animation: slideInRight 0.3s ease-out reverse;
}

/* Responsive animations */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* Dark mode transitions */
.dark-mode-transition {
    transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
}
