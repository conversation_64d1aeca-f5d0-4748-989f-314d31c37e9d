<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Contact info - Nestria</title>
    {% load static %}
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'nestria-red': '#FF5A5F',
                        'airbnb-pink': '#E91E63',
                    }
                }
            }
        }
    </script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body class="bg-gradient-to-br from-purple-400 via-purple-500 to-purple-600 min-h-screen flex items-center justify-center p-4">
    <div class="bg-white rounded-3xl shadow-2xl w-full max-w-md p-8 relative">
        <!-- Back Button -->
        <button onclick="window.history.back()" class="absolute top-6 left-6 text-gray-600 hover:text-gray-800 text-xl transition-colors">
            <i class="fas fa-arrow-left"></i>
        </button>

        <!-- Header -->
        <div class="text-center mb-8 mt-8">
            <h1 class="text-2xl font-bold text-gray-900 mb-2">Contact info</h1>
            <p class="text-gray-600 text-sm">We'll use this to send you trip confirmations and receipts</p>
        </div>

        <!-- Form -->
        <form method="post" class="space-y-6" id="contactForm">
            {% csrf_token %}

            <!-- Email Input with Modern Design -->
            <div class="relative">
                <div class="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                    <i class="fas fa-envelope text-gray-400"></i>
                </div>
                <input type="email"
                       name="email"
                       id="email"
                       placeholder="Email address"
                       value="<EMAIL>"
                       class="w-full pl-12 pr-4 py-4 border border-gray-300 rounded-xl focus:ring-2 focus:ring-nestria-red focus:border-transparent transition-all text-gray-900 placeholder-gray-500"
                       required>
            </div>

            <!-- Privacy & Terms Section with Checkbox -->
            <div class="space-y-4">
                <div class="flex items-start space-x-3">
                    <input type="checkbox"
                           id="terms_agreement"
                           name="terms_agreement"
                           class="mt-1 h-5 w-5 text-nestria-red border-gray-300 rounded focus:ring-nestria-red focus:ring-2"
                           required>
                    <label for="terms_agreement" class="text-sm text-gray-700 leading-relaxed">
                        By selecting <strong>Complete Registration</strong>, I agree to Nestria's
                        <button type="button" onclick="openModal('termsModal')" class="text-nestria-red hover:text-red-600 underline font-medium">Terms of Service</button>,
                        <button type="button" onclick="openModal('privacyModal')" class="text-nestria-red hover:text-red-600 underline font-medium">Privacy Policy</button>, and
                        <button type="button" onclick="openModal('communityModal')" class="text-nestria-red hover:text-red-600 underline font-medium">Community Guidelines</button>.
                    </label>
                </div>
            </div>

            <!-- Submit Button -->
            <button type="submit"
                    id="submitBtn"
                    class="w-full bg-gray-300 text-gray-500 py-4 rounded-xl font-semibold text-lg transition-all flex items-center justify-center cursor-not-allowed"
                    disabled>
                <i class="fas fa-check-circle mr-2"></i>
                Complete Registration
            </button>
        </form>
    </div>

    <!-- Terms of Service Modal -->
    <div id="termsModal" class="fixed inset-0 bg-black bg-opacity-50 hidden flex items-center justify-center z-50 p-4">
        <div class="bg-white rounded-2xl shadow-xl w-full max-w-2xl max-h-[80vh] overflow-hidden">
            <div class="flex justify-between items-center p-6 border-b border-gray-200">
                <h2 class="text-xl font-bold text-gray-900">Terms of Service</h2>
                <button onclick="closeModal('termsModal')" class="text-gray-400 hover:text-gray-600 text-2xl">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="p-6 overflow-y-auto max-h-[60vh]">
                <div class="prose prose-sm max-w-none">
                    <h3 class="text-lg font-semibold mb-3">1. Acceptance of Terms</h3>
                    <p class="mb-4">By accessing and using Nestria, you accept and agree to be bound by the terms and provision of this agreement.</p>

                    <h3 class="text-lg font-semibold mb-3">2. Use License</h3>
                    <p class="mb-4">Permission is granted to temporarily download one copy of Nestria's materials for personal, non-commercial transitory viewing only.</p>

                    <h3 class="text-lg font-semibold mb-3">3. Disclaimer</h3>
                    <p class="mb-4">The materials on Nestria are provided on an 'as is' basis. Nestria makes no warranties, expressed or implied, and hereby disclaims and negates all other warranties including without limitation, implied warranties or conditions of merchantability, fitness for a particular purpose, or non-infringement of intellectual property or other violation of rights.</p>

                    <h3 class="text-lg font-semibold mb-3">4. Limitations</h3>
                    <p class="mb-4">In no event shall Nestria or its suppliers be liable for any damages (including, without limitation, damages for loss of data or profit, or due to business interruption) arising out of the use or inability to use Nestria's materials.</p>
                </div>
            </div>
            <div class="p-6 border-t border-gray-200">
                <button onclick="closeModal('termsModal')" class="w-full bg-nestria-red text-white py-3 rounded-xl font-medium hover:bg-red-600 transition-colors">
                    I Understand
                </button>
            </div>
        </div>
    </div>

    <!-- Privacy Policy Modal -->
    <div id="privacyModal" class="fixed inset-0 bg-black bg-opacity-50 hidden flex items-center justify-center z-50 p-4">
        <div class="bg-white rounded-2xl shadow-xl w-full max-w-2xl max-h-[80vh] overflow-hidden">
            <div class="flex justify-between items-center p-6 border-b border-gray-200">
                <h2 class="text-xl font-bold text-gray-900">Privacy Policy</h2>
                <button onclick="closeModal('privacyModal')" class="text-gray-400 hover:text-gray-600 text-2xl">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="p-6 overflow-y-auto max-h-[60vh]">
                <div class="prose prose-sm max-w-none">
                    <h3 class="text-lg font-semibold mb-3">Information We Collect</h3>
                    <p class="mb-4">We collect information you provide directly to us, such as when you create an account, make a reservation, or contact us for support.</p>

                    <h3 class="text-lg font-semibold mb-3">How We Use Your Information</h3>
                    <p class="mb-4">We use the information we collect to provide, maintain, and improve our services, process transactions, and communicate with you.</p>

                    <h3 class="text-lg font-semibold mb-3">Information Sharing</h3>
                    <p class="mb-4">We do not sell, trade, or otherwise transfer your personal information to third parties without your consent, except as described in this policy.</p>

                    <h3 class="text-lg font-semibold mb-3">Data Security</h3>
                    <p class="mb-4">We implement appropriate security measures to protect your personal information against unauthorized access, alteration, disclosure, or destruction.</p>
                </div>
            </div>
            <div class="p-6 border-t border-gray-200">
                <button onclick="closeModal('privacyModal')" class="w-full bg-nestria-red text-white py-3 rounded-xl font-medium hover:bg-red-600 transition-colors">
                    I Understand
                </button>
            </div>
        </div>
    </div>

    <!-- Community Guidelines Modal -->
    <div id="communityModal" class="fixed inset-0 bg-black bg-opacity-50 hidden flex items-center justify-center z-50 p-4">
        <div class="bg-white rounded-2xl shadow-xl w-full max-w-2xl max-h-[80vh] overflow-hidden">
            <div class="flex justify-between items-center p-6 border-b border-gray-200">
                <h2 class="text-xl font-bold text-gray-900">Community Guidelines</h2>
                <button onclick="closeModal('communityModal')" class="text-gray-400 hover:text-gray-600 text-2xl">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="p-6 overflow-y-auto max-h-[60vh]">
                <div class="prose prose-sm max-w-none">
                    <h3 class="text-lg font-semibold mb-3">Respect and Inclusion</h3>
                    <p class="mb-4">Treat everyone in the Nestria community with respect and without judgment or bias, regardless of race, religion, national origin, ethnicity, disability, sex, gender identity, sexual orientation, or age.</p>

                    <h3 class="text-lg font-semibold mb-3">Safety First</h3>
                    <p class="mb-4">Look out for one another and help create a safe and welcoming environment for all community members.</p>

                    <h3 class="text-lg font-semibold mb-3">Honest Communication</h3>
                    <p class="mb-4">Be honest and accurate in your communications with other community members. Provide complete and truthful information in your profile and listings.</p>

                    <h3 class="text-lg font-semibold mb-3">Responsible Hosting and Guesting</h3>
                    <p class="mb-4">Whether you're hosting or staying as a guest, be considerate of others and treat spaces and belongings with care.</p>
                </div>
            </div>
            <div class="p-6 border-t border-gray-200">
                <button onclick="closeModal('communityModal')" class="w-full bg-nestria-red text-white py-3 rounded-xl font-medium hover:bg-red-600 transition-colors">
                    I Understand
                </button>
            </div>
        </div>
    </div>

    <script>
        // Elements
        const form = document.getElementById('contactForm');
        const emailInput = document.getElementById('email');
        const termsCheckbox = document.getElementById('terms_agreement');
        const submitBtn = document.getElementById('submitBtn');

        // Modal functions
        function openModal(modalId) {
            document.getElementById(modalId).classList.remove('hidden');
            document.body.style.overflow = 'hidden';
        }

        function closeModal(modalId) {
            document.getElementById(modalId).classList.add('hidden');
            document.body.style.overflow = 'auto';
        }

        // Close modal when clicking outside
        document.querySelectorAll('[id$="Modal"]').forEach(modal => {
            modal.addEventListener('click', function(e) {
                if (e.target === this) {
                    closeModal(this.id);
                }
            });
        });

        // Enable/disable submit button based on checkbox
        function updateSubmitButton() {
            if (termsCheckbox.checked) {
                submitBtn.disabled = false;
                submitBtn.classList.remove('bg-gray-300', 'text-gray-500', 'cursor-not-allowed');
                submitBtn.classList.add('bg-nestria-red', 'text-white', 'hover:bg-red-600', 'cursor-pointer');
            } else {
                submitBtn.disabled = true;
                submitBtn.classList.add('bg-gray-300', 'text-gray-500', 'cursor-not-allowed');
                submitBtn.classList.remove('bg-nestria-red', 'text-white', 'hover:bg-red-600', 'cursor-pointer');
            }
        }

        // Event listeners
        termsCheckbox.addEventListener('change', updateSubmitButton);

        // Form validation
        form.addEventListener('submit', function(e) {
            const email = emailInput.value.trim();

            if (!email) {
                e.preventDefault();
                alert('Please enter your email address.');
                emailInput.focus();
                return;
            }

            if (!isValidEmail(email)) {
                e.preventDefault();
                alert('Please enter a valid email address.');
                emailInput.focus();
                return;
            }

            if (!termsCheckbox.checked) {
                e.preventDefault();
                alert('Please accept the terms and conditions to continue.');
                termsCheckbox.focus();
                return;
            }
        });

        function isValidEmail(email) {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            return emailRegex.test(email);
        }

        // Initialize
        updateSubmitButton();
        emailInput.focus();

        // Escape key to close modals
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                document.querySelectorAll('[id$="Modal"]').forEach(modal => {
                    if (!modal.classList.contains('hidden')) {
                        closeModal(modal.id);
                    }
                });
            }
        });
    </script>
</body>
</html>
