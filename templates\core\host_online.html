{% extends 'base.html' %}
{% load static %}

{% block title %}Host Online Experience - Nestria{% endblock %}

{% block content %}
<div class="min-h-screen bg-gray-50 dark:bg-gray-900 py-8">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="text-center mb-12">
            <h1 class="text-4xl font-bold text-gray-900 dark:text-white mb-4">
                Host an Online Experience
            </h1>
            <p class="text-xl text-gray-600 dark:text-gray-400 max-w-3xl mx-auto">
                Share your expertise with a global audience from the comfort of your home. Create engaging online experiences and connect with people worldwide.
            </p>
        </div>

        <!-- Benefits Section -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-8 mb-12">
            <div class="text-center">
                <div class="w-16 h-16 bg-green-100 dark:bg-green-900/20 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-globe text-green-600 dark:text-green-400 text-2xl"></i>
                </div>
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">Global Reach</h3>
                <p class="text-gray-600 dark:text-gray-400">Connect with participants from around the world</p>
            </div>
            
            <div class="text-center">
                <div class="w-16 h-16 bg-blue-100 dark:bg-blue-900/20 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-home text-blue-600 dark:text-blue-400 text-2xl"></i>
                </div>
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">From Home</h3>
                <p class="text-gray-600 dark:text-gray-400">Host from anywhere with just a computer and internet</p>
            </div>
            
            <div class="text-center">
                <div class="w-16 h-16 bg-purple-100 dark:bg-purple-900/20 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-clock text-purple-600 dark:text-purple-400 text-2xl"></i>
                </div>
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">Flexible Schedule</h3>
                <p class="text-gray-600 dark:text-gray-400">Set your own schedule and host when convenient</p>
            </div>
        </div>

        <!-- Online Experience Form -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-8 mb-8">
            <h2 class="text-2xl font-bold text-gray-900 dark:text-white mb-6">Create Your Online Experience</h2>
            
            <form class="space-y-6">
                {% csrf_token %}
                
                <!-- Basic Information -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Experience Title *
                        </label>
                        <input type="text" 
                               placeholder="e.g., Virtual Cooking Class: Italian Pasta"
                               class="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white">
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Category *
                        </label>
                        <select class="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white">
                            <option>Select a category</option>
                            <option>Cooking & Food</option>
                            <option>Arts & Crafts</option>
                            <option>Wellness & Meditation</option>
                            <option>Music & Performance</option>
                            <option>Virtual Tours</option>
                            <option>Language Learning</option>
                            <option>Business & Skills</option>
                        </select>
                    </div>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Description *
                    </label>
                    <textarea rows="4" 
                              placeholder="Describe what participants will learn and do during your online experience..."
                              class="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"></textarea>
                </div>

                <!-- Session Details -->
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Duration *
                        </label>
                        <select class="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white">
                            <option>30 minutes</option>
                            <option>45 minutes</option>
                            <option>1 hour</option>
                            <option>1.5 hours</option>
                            <option>2 hours</option>
                            <option>2.5 hours</option>
                            <option>3 hours</option>
                        </select>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Max Participants *
                        </label>
                        <select class="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white">
                            <option>1-5 participants</option>
                            <option>1-10 participants</option>
                            <option>1-15 participants</option>
                            <option>1-20 participants</option>
                            <option>1-30 participants</option>
                            <option>Unlimited</option>
                        </select>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Language *
                        </label>
                        <select class="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white">
                            <option>English</option>
                            <option>Spanish</option>
                            <option>French</option>
                            <option>German</option>
                            <option>Italian</option>
                            <option>Portuguese</option>
                            <option>Mandarin</option>
                            <option>Japanese</option>
                            <option>Other</option>
                        </select>
                    </div>
                </div>

                <!-- Pricing and Platform -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Price per Person *
                        </label>
                        <div class="relative">
                            <span class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500">$</span>
                            <input type="number" 
                                   placeholder="25"
                                   class="w-full pl-8 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white">
                        </div>
                        <p class="text-xs text-gray-500 mt-1">Recommended: $15-50 for online experiences</p>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Platform *
                        </label>
                        <select class="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white">
                            <option>Zoom (Recommended)</option>
                            <option>Google Meet</option>
                            <option>Microsoft Teams</option>
                            <option>Skype</option>
                            <option>Other</option>
                        </select>
                    </div>
                </div>

                <!-- Requirements -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        What Participants Need
                    </label>
                    <div class="space-y-2">
                        <div class="flex items-center">
                            <input type="checkbox" class="w-4 h-4 text-blue-600 rounded" checked disabled>
                            <label class="ml-2 text-sm text-gray-700 dark:text-gray-300">Computer/tablet with camera and microphone</label>
                        </div>
                        <div class="flex items-center">
                            <input type="checkbox" class="w-4 h-4 text-blue-600 rounded" checked disabled>
                            <label class="ml-2 text-sm text-gray-700 dark:text-gray-300">Stable internet connection</label>
                        </div>
                        <div class="flex items-center">
                            <input type="checkbox" class="w-4 h-4 text-blue-600 rounded">
                            <label class="ml-2 text-sm text-gray-700 dark:text-gray-300">Specific materials (list below)</label>
                        </div>
                        <div class="flex items-center">
                            <input type="checkbox" class="w-4 h-4 text-blue-600 rounded">
                            <label class="ml-2 text-sm text-gray-700 dark:text-gray-300">Prior experience/knowledge</label>
                        </div>
                    </div>
                    
                    <div class="mt-4">
                        <textarea rows="3" 
                                  placeholder="List any specific materials, ingredients, or preparation needed..."
                                  class="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"></textarea>
                    </div>
                </div>

                <!-- Schedule -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Schedule Type *
                    </label>
                    <div class="space-y-3">
                        <div class="flex items-center">
                            <input type="radio" name="schedule_type" value="recurring" class="w-4 h-4 text-blue-600">
                            <label class="ml-2 text-sm text-gray-700 dark:text-gray-300">Recurring sessions (same time weekly)</label>
                        </div>
                        <div class="flex items-center">
                            <input type="radio" name="schedule_type" value="one_time" class="w-4 h-4 text-blue-600">
                            <label class="ml-2 text-sm text-gray-700 dark:text-gray-300">One-time sessions (book individually)</label>
                        </div>
                        <div class="flex items-center">
                            <input type="radio" name="schedule_type" value="on_demand" class="w-4 h-4 text-blue-600">
                            <label class="ml-2 text-sm text-gray-700 dark:text-gray-300">On-demand (participants request times)</label>
                        </div>
                    </div>
                </div>

                <!-- Technical Setup -->
                <div class="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-6">
                    <h3 class="text-lg font-semibold text-blue-900 dark:text-blue-200 mb-4">
                        <i class="fas fa-laptop mr-2"></i>
                        Technical Requirements
                    </h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                        <div>
                            <h4 class="font-medium text-blue-800 dark:text-blue-300 mb-2">You'll Need:</h4>
                            <ul class="space-y-1 text-blue-700 dark:text-blue-400">
                                <li>• Computer with camera and microphone</li>
                                <li>• Stable internet (minimum 5 Mbps upload)</li>
                                <li>• Good lighting and quiet space</li>
                                <li>• Video conferencing software</li>
                            </ul>
                        </div>
                        <div>
                            <h4 class="font-medium text-blue-800 dark:text-blue-300 mb-2">We Provide:</h4>
                            <ul class="space-y-1 text-blue-700 dark:text-blue-400">
                                <li>• Booking and payment processing</li>
                                <li>• Participant management</li>
                                <li>• Email reminders and instructions</li>
                                <li>• 24/7 technical support</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <!-- Submit Button -->
                <div class="flex space-x-4">
                    <button type="button" 
                            onclick="submitOnlineExperience()"
                            class="flex-1 bg-green-600 hover:bg-green-700 text-white py-3 px-6 rounded-lg font-medium transition-colors">
                        <i class="fas fa-video mr-2"></i>
                        Create Online Experience
                    </button>
                    <button type="button" 
                            onclick="saveDraft()"
                            class="bg-gray-300 hover:bg-gray-400 text-gray-700 py-3 px-6 rounded-lg font-medium transition-colors">
                        Save Draft
                    </button>
                </div>
            </form>
        </div>

        <!-- Support Section -->
        <div class="bg-green-50 dark:bg-green-900/20 rounded-lg p-6 text-center">
            <h3 class="text-lg font-semibold text-green-900 dark:text-green-200 mb-2">
                <i class="fas fa-headset mr-2"></i>
                Need Technical Help?
            </h3>
            <p class="text-green-700 dark:text-green-300 mb-4">
                Our technical team can help you set up your online experience and test your equipment.
            </p>
            <button onclick="getTechnicalSupport()" class="bg-green-600 hover:bg-green-700 text-white px-6 py-2 rounded-lg font-medium">
                Get Technical Support
            </button>
        </div>
    </div>
</div>

<script>
function submitOnlineExperience() {
    if (confirm('Submit your online experience for review?\n\nOur team will review your experience and help you with technical setup within 24 hours.')) {
        alert('Online experience submitted successfully!\n\nNext steps:\n• Technical setup call (if needed)\n• Test session with our team\n• Experience goes live after approval\n\nYou\'ll receive an email with next steps shortly.');
    }
}

function saveDraft() {
    alert('Draft saved successfully! You can continue editing your online experience later from your host dashboard.');
}

function getTechnicalSupport() {
    alert('Technical Support Options:\n\n📧 Email: <EMAIL>\n📞 Phone: +1 (555) 123-TECH\n💬 Live Chat: Available 9AM-9PM EST\n🎥 Video Call: Schedule a setup session\n\nOur technical team can help with:\n• Platform setup and testing\n• Audio/video optimization\n• Interactive features\n• Troubleshooting');
}
</script>
{% endblock %}
