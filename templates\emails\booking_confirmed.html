{% extends 'emails/base_email.html' %}

{% block title %}Booking Confirmed - {{ reservation.room.title }}{% endblock %}

{% block email_title %}Your booking is confirmed! ✅{% endblock %}

{% block content %}
    <!-- Success Banner -->
    <div style="background: linear-gradient(135deg, #10B981 0%, #059669 100%); border-radius: 12px; padding: 30px; text-align: center; margin-bottom: 30px;">
        <div style="font-size: 48px; margin-bottom: 15px;">🎉</div>
        <h1 style="color: white; font-size: 28px; font-weight: 800; margin: 0 0 10px 0; text-shadow: 0 2px 4px rgba(0,0,0,0.2);">
            Booking Confirmed!
        </h1>
        <p style="color: rgba(255,255,255,0.95); font-size: 16px; margin: 0; font-weight: 500;">
            Your adventure awaits
        </p>
    </div>

    <p class="email-text" style="font-size: 18px; font-weight: 500;">
        Hi {{ guest_name }},
    </p>

    <p class="email-text">
        🌟 Fantastic news! Your reservation has been confirmed and we're thrilled to host you at
        <strong style="color: #FF5A5F;">{{ reservation.room.title }}</strong>.
        Get ready for an unforgettable experience!
    </p>
    
    <!-- Booking Details Card -->
    <div style="background: linear-gradient(135deg, #F8FAFC 0%, #F1F5F9 100%); border: 2px solid #E2E8F0; border-radius: 16px; padding: 30px; margin: 30px 0; box-shadow: 0 4px 12px rgba(0,0,0,0.05);">
        <h2 style="margin: 0 0 25px 0; font-size: 20px; color: #1f2937; font-weight: 700; display: flex; align-items: center;">
            📋 Booking Details
        </h2>

        <table style="width: 100%; border-collapse: collapse;">
            <tr>
                <td style="padding: 12px 0; font-weight: 600; color: #374151; width: 35%; display: flex; align-items: center;">
                    🏠 Property:
                </td>
                <td style="padding: 12px 0; color: #1f2937; font-weight: 500;">{{ reservation.room.title }}</td>
            </tr>
            <tr>
                <td style="padding: 12px 0; font-weight: 600; color: #374151;">📍 Location:</td>
                <td style="padding: 12px 0; color: #1f2937;">{{ reservation.room.city }}, {{ reservation.room.country }}</td>
            </tr>
            <tr>
                <td style="padding: 12px 0; font-weight: 600; color: #374151;">📅 Check-in:</td>
                <td style="padding: 12px 0; color: #1f2937; font-weight: 600;">{{ reservation.check_in|date:"F d, Y" }}</td>
            </tr>
            <tr>
                <td style="padding: 12px 0; font-weight: 600; color: #374151;">📅 Check-out:</td>
                <td style="padding: 12px 0; color: #1f2937; font-weight: 600;">{{ reservation.check_out|date:"F d, Y" }}</td>
            </tr>
            <tr>
                <td style="padding: 12px 0; font-weight: 600; color: #374151;">👥 Guests:</td>
                <td style="padding: 12px 0; color: #1f2937;">
                    {{ reservation.adults }} adult{{ reservation.adults|pluralize }}
                    {% if reservation.children %}, {{ reservation.children }} child{{ reservation.children|pluralize }}{% endif %}
                    {% if reservation.infants %}, {{ reservation.infants }} infant{{ reservation.infants|pluralize }}{% endif %}
                </td>
            </tr>
            <tr style="border-top: 2px solid #E2E8F0;">
                <td style="padding: 16px 0 8px 0; font-weight: 700; color: #FF5A5F; font-size: 16px;">💰 Total Price:</td>
                <td style="padding: 16px 0 8px 0; color: #FF5A5F; font-size: 24px; font-weight: 800;">${{ reservation.total_price|floatformat:2 }}</td>
            </tr>
            <tr>
                <td style="padding: 8px 0; font-weight: 600; color: #374151;">🎫 Booking ID:</td>
                <td style="padding: 8px 0; color: #1f2937; font-family: 'Courier New', monospace; background: #F3F4F6; padding: 4px 8px; border-radius: 4px; display: inline-block;">#{{ reservation.confirmation_number|default:reservation.id }}</td>
            </tr>
        </table>
    </div>
    
    <div style="text-align: center; margin: 35px 0;">
        <a href="{{ property_url }}" class="email-button" style="margin-right: 15px;">
            🏠 View Property Details
        </a>
        <a href="#" class="email-button email-button-secondary">
            📱 Download Mobile App
        </a>
    </div>

    <!-- Important Information -->
    <div style="background: linear-gradient(135deg, #FEF3C7 0%, #FDE68A 100%); border: 2px solid #F59E0B; border-radius: 12px; padding: 25px; margin: 30px 0; box-shadow: 0 4px 12px rgba(245, 158, 11, 0.15);">
        <h3 style="margin: 0 0 20px 0; font-size: 18px; color: #92400E; font-weight: 700; display: flex; align-items: center;">
            ⚠️ Important Check-in Information
        </h3>
        <div style="display: grid; gap: 12px;">
            <div style="display: flex; align-items: center; color: #92400E;">
                <span style="margin-right: 10px; font-size: 16px;">🆔</span>
                <span>Please bring a valid government-issued ID for check-in</span>
            </div>
            <div style="display: flex; align-items: center; color: #92400E;">
                <span style="margin-right: 10px; font-size: 16px;">🕒</span>
                <span>Check-in: After 3:00 PM | Check-out: Before 11:00 AM</span>
            </div>
            <div style="display: flex; align-items: center; color: #92400E;">
                <span style="margin-right: 10px; font-size: 16px;">📞</span>
                <span>Contact your host 24 hours before arrival for specific instructions</span>
            </div>
            <div style="display: flex; align-items: center; color: #92400E;">
                <span style="margin-right: 10px; font-size: 16px;">🧳</span>
                <span>Pack light and bring only essentials for a comfortable stay</span>
            </div>
        </div>
    </div>
    
    {% if reservation.special_requests %}
    <div style="margin: 25px 0;">
        <h3 style="margin: 0 0 10px 0; font-size: 16px; color: #1f2937;">Special Requests</h3>
        <p style="background-color: #f3f4f6; padding: 15px; border-radius: 6px; margin: 0; color: #374151;">
            {{ reservation.special_requests }}
        </p>
    </div>
    {% endif %}
    
    <div class="email-divider"></div>
    
    <p class="email-text">
        <strong>Questions about your booking?</strong><br>
        Contact us at <a href="mailto:{{ support_email }}" style="color: #3b82f6;">{{ support_email }}</a> 
        or visit your booking management page.
    </p>
    
    <p class="email-text">
        We hope you have an amazing stay! 🌟
    </p>
{% endblock %}
