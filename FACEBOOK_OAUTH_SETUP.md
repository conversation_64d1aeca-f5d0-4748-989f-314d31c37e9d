# Configuration Facebook Login pour Nestria

## 🎯 Objectif
Configurer l'authentification Facebook pour permettre aux utilisateurs de se connecter avec leur compte Facebook et récupérer automatiquement leurs informations.

## 📋 Étapes de Configuration

### 1. C<PERSON>er une Application Facebook

1. Aller sur [Facebook for Developers](https://developers.facebook.com/)
2. Cliquer sur **Mes apps** → **Créer une app**
3. Choisir **Consommateur** ou **Aucun**
4. Nom de l'app : `Nestria`
5. Email de contact : votre email
6. Cliquer sur **Créer l'app**

### 2. Configurer Facebook Login

1. Dans le tableau de bord de l'app, cliquer sur **+ Ajouter un produit**
2. Trouver **Facebook Login** → **Configurer**
3. Choisir **Web** comme plateforme
4. URL du site : `http://localhost:8000`

### 3. Configurer les URLs de Redirection

1. Dans le menu de gauche : **Facebook Login** → **Paramètres**
2. **URIs de redirection OAuth valides** :
   ```
   http://localhost:8000/accounts/facebook/login/callback/
   http://127.0.0.1:8000/accounts/facebook/login/callback/
   ```
3. **Domaines d'app autorisés** :
   ```
   localhost
   127.0.0.1
   ```

### 4. Récupérer les Clés

1. Dans le menu de gauche : **Paramètres** → **De base**
2. Récupérer :
   - **ID de l'app** : `****************`
   - **Clé secrète de l'app** : `abcdef1234567890abcdef1234567890`

### 5. Configurer les Autorisations

1. **Facebook Login** → **Paramètres**
2. **Autorisations et fonctionnalités** :
   - `email` : ✅ Activé
   - `public_profile` : ✅ Activé

### 6. Mode de Développement

⚠️ **Important** : L'app est en mode développement par défaut
- Seuls les développeurs/testeurs peuvent se connecter
- Pour permettre à tous de se connecter, passer en mode **Live**
- Aller dans **Paramètres** → **De base** → **Mode de l'app** → **Live**

## 🔧 Configuration Django

Exécuter le script :
```bash
python configure_facebook_oauth.py
```

Ou manuellement dans l'admin :
1. `http://127.0.0.1:8000/admin/socialaccount/socialapp/`
2. **Facebook OAuth** :
   - **Client id** : ID de l'app Facebook
   - **Secret key** : Clé secrète de l'app

## 🧪 Test

1. `http://127.0.0.1:8000/signup/`
2. **Continue with Facebook**
3. Autoriser l'application
4. Redirection vers `finish-signup` avec données pré-remplies

## 📊 Données Récupérées

- `email` : Email de l'utilisateur
- `first_name` : Prénom
- `last_name` : Nom
- `picture` : Photo de profil
- `id` : ID Facebook unique

## ⚠️ Limitations Facebook

Facebook a des restrictions strictes :
- Révision d'app requise pour certaines autorisations
- Mode développement limité aux testeurs
- Politique de données stricte

## 🚨 Note Importante

Facebook Login est en cours de dépréciation pour certains cas d'usage. Considérez Google OAuth comme alternative principale.
