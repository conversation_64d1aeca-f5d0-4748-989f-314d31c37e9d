/* Custom CSS for StayVibe */

/* Root variables for consistent theming */
:root {
    --primary-50: #f0f9ff;
    --primary-500: #0ea5e9;
    --primary-600: #0284c7;
    --secondary-500: #d946ef;
    --secondary-600: #c026d3;
    --accent-500: #f97316;
    --dark-800: #1e293b;
    --dark-900: #0f172a;
}

/* Dark mode variables */
.dark {
    --bg-primary: var(--dark-900);
    --bg-secondary: var(--dark-800);
    --text-primary: #f1f5f9;
    --text-secondary: #cbd5e1;
}

/* Smooth transitions for all elements */
* {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Custom scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f5f9;
}

.dark ::-webkit-scrollbar-track {
    background: var(--dark-800);
}

::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 6px;
}

.dark ::-webkit-scrollbar-thumb {
    background: #475569;
}

::-webkit-scrollbar-thumb:hover {
    background: #94a3b8;
}

.dark ::-webkit-scrollbar-thumb:hover {
    background: #64748b;
}

/* Modern Card Effects */
.property-card {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    backdrop-filter: blur(10px);
}

.property-card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 20px 40px rgba(14, 165, 233, 0.15);
}

.dark .property-card:hover {
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

/* Glass morphism effects */
.glass {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.dark .glass {
    background: rgba(15, 23, 42, 0.8);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Search form styling */
.search-form {
    box-shadow: 0 8px 32px rgba(14, 165, 233, 0.12);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.dark .search-form {
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.search-input {
    border: none;
    outline: none;
    background: transparent;
}

.search-input:focus {
    outline: none;
}

/* Modern Button Effects */
.btn-primary {
    background: linear-gradient(135deg, var(--primary-500) 0%, var(--secondary-500) 100%);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.btn-primary::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.btn-primary:hover::before {
    left: 100%;
}

.btn-primary:hover {
    background: linear-gradient(135deg, var(--primary-600) 0%, var(--secondary-600) 100%);
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(14, 165, 233, 0.4);
}

/* Gradient text effects */
.gradient-text {
    background: linear-gradient(135deg, var(--primary-600), var(--secondary-600));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Image gallery styles */
.image-gallery {
    position: relative;
    overflow: hidden;
    border-radius: 12px;
}

.image-gallery img {
    transition: transform 0.3s ease-in-out;
}

.image-gallery:hover img {
    transform: scale(1.05);
}

/* Heart icon animation */
.heart-icon {
    transition: all 0.2s ease-in-out;
}

.heart-icon:hover {
    transform: scale(1.1);
}

.heart-icon.liked {
    color: #FF5A5F;
}

/* Loading spinner */
.spinner {
    border: 2px solid #f3f3f3;
    border-top: 2px solid #FF5A5F;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Date picker custom styling */
.date-picker {
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 12px;
    transition: border-color 0.2s ease-in-out;
}

.date-picker:focus {
    border-color: #FF5A5F;
    box-shadow: 0 0 0 2px rgba(255, 90, 95, 0.1);
}

/* Modal styles */
.modal-overlay {
    background-color: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(4px);
}

.modal-content {
    animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Price display */
.price-display {
    font-weight: 600;
    color: #222;
}

/* Rating stars */
.rating-stars {
    color: #FF5A5F;
}

/* Responsive grid adjustments */
@media (max-width: 640px) {
    .property-grid {
        grid-template-columns: 1fr;
    }
}

@media (min-width: 641px) and (max-width: 1024px) {
    .property-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (min-width: 1025px) {
    .property-grid {
        grid-template-columns: repeat(3, 1fr);
    }
}

@media (min-width: 1280px) {
    .property-grid {
        grid-template-columns: repeat(4, 1fr);
    }
}

/* Search filters */
.filter-chip {
    background: white;
    border: 1px solid #ddd;
    border-radius: 24px;
    padding: 8px 16px;
    cursor: pointer;
    transition: all 0.2s ease-in-out;
}

.filter-chip:hover {
    border-color: #222;
}

.filter-chip.active {
    background: #222;
    color: white;
    border-color: #222;
}

/* Amenity icons */
.amenity-icon {
    width: 24px;
    height: 24px;
    color: #717171;
}

/* Host profile image */
.host-avatar {
    border-radius: 50%;
    border: 2px solid #fff;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* Review card */
.review-card {
    background: #f9f9f9;
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 16px;
}

/* Booking form */
.booking-form {
    background: white;
    border: 1px solid #ddd;
    border-radius: 12px;
    padding: 24px;
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.12);
    position: sticky;
    top: 100px;
}

/* Map container */
.map-container {
    border-radius: 12px;
    overflow: hidden;
    height: 400px;
}

/* Breadcrumb */
.breadcrumb {
    color: #717171;
    font-size: 14px;
}

.breadcrumb a {
    color: #717171;
    text-decoration: underline;
}

.breadcrumb a:hover {
    color: #222;
}

/* Error states */
.error-message {
    color: #c13515;
    font-size: 14px;
    margin-top: 4px;
}

.input-error {
    border-color: #c13515;
}

.input-error:focus {
    border-color: #c13515;
    box-shadow: 0 0 0 2px rgba(193, 53, 21, 0.1);
}

/* Success states */
.success-message {
    color: #008a05;
    font-size: 14px;
    margin-top: 4px;
}

/* Skeleton loading */
.skeleton {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% {
        background-position: 200% 0;
    }
    100% {
        background-position: -200% 0;
    }
}

/* Interactive Map Styles */
.leaflet-container {
    border-radius: 8px;
}

.custom-marker {
    background: transparent !important;
    border: none !important;
}

.poi-marker {
    background: transparent !important;
    border: none !important;
}

.leaflet-popup-content-wrapper {
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.leaflet-popup-content {
    margin: 8px 12px;
    font-family: inherit;
}

/* Photo Gallery Styles */
.gallery-thumbnail {
    transition: all 0.3s ease;
}

.gallery-thumbnail:hover {
    transform: scale(1.05);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.gallery-modal {
    backdrop-filter: blur(4px);
}

.gallery-navigation {
    transition: all 0.3s ease;
}

.gallery-navigation:hover {
    transform: scale(1.1);
}

/* Professional Photo Effects */
.photo-overlay {
    background: linear-gradient(
        to bottom,
        rgba(0, 0, 0, 0) 0%,
        rgba(0, 0, 0, 0.1) 50%,
        rgba(0, 0, 0, 0.7) 100%
    );
}

.destination-hero {
    position: relative;
    overflow: hidden;
}

.destination-hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
        135deg,
        rgba(255, 90, 95, 0.1) 0%,
        rgba(0, 0, 0, 0.3) 100%
    );
    z-index: 1;
}

.destination-hero > * {
    position: relative;
    z-index: 2;
}

/* Destination Card Hover Effects */
.destination-card {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.destination-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.destination-card:hover .destination-image {
    transform: scale(1.1);
}

.destination-image {
    transition: transform 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Map Controls Styling */
.leaflet-control-zoom a {
    background-color: #FF5A5F !important;
    color: white !important;
    border: none !important;
}

.leaflet-control-zoom a:hover {
    background-color: #E31C5F !important;
}

/* Gallery Counter Badge */
.gallery-counter {
    background: rgba(0, 0, 0, 0.7);
    backdrop-filter: blur(8px);
    border-radius: 20px;
    padding: 8px 16px;
    font-size: 14px;
    font-weight: 500;
}

/* Smooth scrolling for gallery navigation */
.gallery-thumbnails {
    scroll-behavior: smooth;
}

/* Professional image loading */
.destination-image-loading {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
}

/* Enhanced button styles for gallery */
.gallery-btn {
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(8px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
}

.gallery-btn:hover {
    background: rgba(0, 0, 0, 0.7);
    transform: scale(1.05);
}

/* Special Destination Backgrounds */
.destination-paris .destination-hero::before {
    background: linear-gradient(
        135deg,
        rgba(255, 107, 157, 0.3) 0%,
        rgba(196, 69, 105, 0.4) 50%,
        rgba(248, 181, 0, 0.3) 100%
    );
}

.destination-barcelona .destination-hero::before {
    background: linear-gradient(
        135deg,
        rgba(0, 77, 122, 0.3) 0%,
        rgba(0, 135, 147, 0.4) 50%,
        rgba(0, 191, 114, 0.3) 100%
    );
}

/* Paris special styling */
.destination-paris .destination-hero {
    background: linear-gradient(135deg, #ff6b9d, #c44569, #f8b500);
}

.destination-paris .photo-overlay {
    background: linear-gradient(
        to bottom,
        rgba(255, 107, 157, 0) 0%,
        rgba(196, 69, 105, 0.2) 50%,
        rgba(248, 181, 0, 0.8) 100%
    );
}

/* Barcelona special styling */
.destination-barcelona .destination-hero {
    background: linear-gradient(135deg, #004d7a, #008793, #00bf72);
}

.destination-barcelona .photo-overlay {
    background: linear-gradient(
        to bottom,
        rgba(0, 77, 122, 0) 0%,
        rgba(0, 135, 147, 0.2) 50%,
        rgba(0, 191, 114, 0.8) 100%
    );
}
