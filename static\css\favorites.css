/* Favorites functionality styles */

.favorite-btn {
    background: rgba(0, 0, 0, 0.3);
    border: none;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    backdrop-filter: blur(4px);
    z-index: 10;
}

.favorite-btn:hover {
    background: rgba(0, 0, 0, 0.5);
    transform: scale(1.1);
}

.favorite-btn:active {
    transform: scale(0.95);
}

.favorite-btn i {
    transition: all 0.3s ease;
    filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.3));
}

.favorite-btn:hover i {
    transform: scale(1.1);
}

/* Heart animation when favorited */
.favorite-btn i.fas.fa-heart {
    animation: heartBeat 0.6s ease-in-out;
}

@keyframes heartBeat {
    0% {
        transform: scale(1);
    }
    25% {
        transform: scale(1.3);
    }
    50% {
        transform: scale(1.1);
    }
    75% {
        transform: scale(1.25);
    }
    100% {
        transform: scale(1);
    }
}

/* Loading state */
.favorite-btn.loading {
    pointer-events: none;
}

.favorite-btn.loading i {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

/* Property card hover effects */
.property-card:hover .favorite-btn {
    background: rgba(0, 0, 0, 0.6);
}

/* Notification styles for favorites */
.favorite-notification {
    position: fixed;
    top: 20px;
    right: 20px;
    background: white;
    border-radius: 8px;
    padding: 12px 16px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    z-index: 1000;
    display: flex;
    align-items: center;
    gap: 8px;
    transform: translateX(100%);
    transition: transform 0.3s ease;
}

.favorite-notification.show {
    transform: translateX(0);
}

.favorite-notification.success {
    border-left: 4px solid #10b981;
}

.favorite-notification.error {
    border-left: 4px solid #ef4444;
}

.favorite-notification.info {
    border-left: 4px solid #3b82f6;
}

.favorite-notification i {
    font-size: 16px;
}

.favorite-notification.success i {
    color: #10b981;
}

.favorite-notification.error i {
    color: #ef4444;
}

.favorite-notification.info i {
    color: #3b82f6;
}

/* Responsive adjustments */
@media (max-width: 640px) {
    .favorite-btn {
        width: 36px;
        height: 36px;
    }
    
    .favorite-btn i {
        font-size: 16px !important;
    }
    
    .favorite-notification {
        right: 10px;
        top: 10px;
        font-size: 14px;
    }
}
