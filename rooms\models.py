from django.db import models
from django.contrib.auth.models import User
from django.urls import reverse
from django.core.validators import MinValueValidator, MaxValueValidator


class RoomType(models.Model):
    """Room type choices like Entire place, Private room, etc."""
    name = models.CharField(max_length=50, unique=True)
    description = models.TextField(blank=True)

    def __str__(self):
        return self.name


class Amenity(models.Model):
    """Amenities like WiFi, Kitchen, Pool, etc."""
    name = models.CharField(max_length=50, unique=True)
    icon = models.CharField(max_length=50, help_text="Font Awesome icon class")

    class Meta:
        verbose_name_plural = "Amenities"

    def __str__(self):
        return self.name


class Room(models.Model):
    """Main room/property model"""
    # Basic Information
    title = models.Char<PERSON>ield(max_length=200)
    description = models.TextField()
    room_type = models.ForeignKey(RoomType, on_delete=models.CASCADE)
    host = models.ForeignKey(User, on_delete=models.CASCADE, related_name='hosted_rooms')

    # Location
    country = models.CharField(max_length=100)
    city = models.CharField(max_length=100)
    address = models.CharField(max_length=255)
    latitude = models.DecimalField(max_digits=9, decimal_places=6, null=True, blank=True)
    longitude = models.DecimalField(max_digits=9, decimal_places=6, null=True, blank=True)

    # Capacity
    max_guests = models.PositiveIntegerField(validators=[MinValueValidator(1), MaxValueValidator(16)])
    bedrooms = models.PositiveIntegerField(default=1)
    beds = models.PositiveIntegerField(default=1)
    bathrooms = models.DecimalField(max_digits=3, decimal_places=1, default=1.0)

    # Pricing
    price_per_night = models.DecimalField(max_digits=10, decimal_places=2)
    cleaning_fee = models.DecimalField(max_digits=8, decimal_places=2, default=0)
    service_fee = models.DecimalField(max_digits=8, decimal_places=2, default=0)

    # Features
    amenities = models.ManyToManyField(Amenity, blank=True)
    instant_book = models.BooleanField(default=False)

    # Contact Information
    phone = models.CharField(max_length=20, blank=True)
    email = models.EmailField(blank=True)
    website = models.URLField(blank=True)

    # Status
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-created_at']

    def __str__(self):
        return self.title

    def get_absolute_url(self):
        return reverse('rooms:room_detail', kwargs={'pk': self.pk})

    @property
    def average_rating(self):
        """Calculate average rating from reviews"""
        reviews = self.reviews.all()
        if reviews:
            return sum(review.rating for review in reviews) / len(reviews)
        return 0

    @property
    def review_count(self):
        """Get total number of reviews"""
        return self.reviews.count()

    @property
    def main_image(self):
        """Get the first image as main image"""
        first_image = self.images.first()
        return first_image.image if first_image else None


class RoomImage(models.Model):
    """Images for rooms"""
    room = models.ForeignKey(Room, on_delete=models.CASCADE, related_name='images')
    image = models.ImageField(upload_to='room_images/')
    caption = models.CharField(max_length=200, blank=True)
    order = models.PositiveIntegerField(default=0)

    class Meta:
        ordering = ['order']

    def __str__(self):
        return f"{self.room.title} - Image {self.order}"


class Review(models.Model):
    """Reviews for rooms"""
    room = models.ForeignKey(Room, on_delete=models.CASCADE, related_name='reviews')
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    rating = models.PositiveIntegerField(validators=[MinValueValidator(1), MaxValueValidator(5)])
    comment = models.TextField()
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ['-created_at']
        unique_together = ['room', 'user']  # One review per user per room

    def __str__(self):
        return f"{self.user.username} - {self.room.title} ({self.rating}/5)"


class Favorite(models.Model):
    """User favorites"""
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='favorites')
    room = models.ForeignKey(Room, on_delete=models.CASCADE, related_name='favorited_by')
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        unique_together = ['user', 'room']

    def __str__(self):
        return f"{self.user.username} - {self.room.title}"


class Destination(models.Model):
    """Destination model for featured locations"""
    name = models.CharField(max_length=100)
    country = models.CharField(max_length=100)
    description = models.TextField()
    historical_info = models.TextField(help_text="Historical information about the destination")
    main_image = models.ImageField(upload_to='destinations/', help_text="Main representative image")
    is_featured = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)

    # Geographic coordinates
    latitude = models.DecimalField(max_digits=9, decimal_places=6, null=True, blank=True)
    longitude = models.DecimalField(max_digits=9, decimal_places=6, null=True, blank=True)

    # SEO and display
    slug = models.SlugField(unique=True)
    meta_description = models.CharField(max_length=160, blank=True)

    class Meta:
        ordering = ['name']

    def __str__(self):
        return f"{self.name}, {self.country}"

    def get_absolute_url(self):
        return reverse('rooms:destination_detail', kwargs={'slug': self.slug})

    @property
    def room_count(self):
        """Get number of rooms in this destination"""
        return Room.objects.filter(city__icontains=self.name, is_active=True).count()


class DestinationImage(models.Model):
    """Additional images for destinations"""
    destination = models.ForeignKey(Destination, on_delete=models.CASCADE, related_name='images')
    image = models.ImageField(upload_to='destinations/')
    caption = models.CharField(max_length=200, blank=True)
    order = models.PositiveIntegerField(default=0)

    class Meta:
        ordering = ['order']

    def __str__(self):
        return f"{self.destination.name} - Image {self.order}"
