{% extends 'base.html' %}
{% load static %}

{% block title %}My Trips - Airbnb Clone{% endblock %}

{% block content %}
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <h1 class="text-3xl font-bold text-gray-900 mb-8">Your trips</h1>
    
    {% if reservations %}
        <!-- Filter Tabs -->
        <div class="mb-8" x-data="{ activeTab: 'all' }">
            <nav class="flex space-x-8 border-b border-gray-200">
                <button @click="activeTab = 'all'" :class="activeTab === 'all' ? 'border-airbnb-red text-airbnb-red' : 'border-transparent text-gray-500 hover:text-gray-700'" class="py-2 px-1 border-b-2 font-medium text-sm">
                    All trips
                </button>
                <button @click="activeTab = 'upcoming'" :class="activeTab === 'upcoming' ? 'border-airbnb-red text-airbnb-red' : 'border-transparent text-gray-500 hover:text-gray-700'" class="py-2 px-1 border-b-2 font-medium text-sm">
                    Upcoming
                </button>
                <button @click="activeTab = 'past'" :class="activeTab === 'past' ? 'border-airbnb-red text-airbnb-red' : 'border-transparent text-gray-500 hover:text-gray-700'" class="py-2 px-1 border-b-2 font-medium text-sm">
                    Past trips
                </button>
                <button @click="activeTab = 'cancelled'" :class="activeTab === 'cancelled' ? 'border-airbnb-red text-airbnb-red' : 'border-transparent text-gray-500 hover:text-gray-700'" class="py-2 px-1 border-b-2 font-medium text-sm">
                    Cancelled
                </button>
            </nav>
        </div>

        <!-- Reservations List -->
        <div class="space-y-6">
            {% for reservation in reservations %}
                <div class="bg-white border border-gray-200 rounded-lg overflow-hidden hover:shadow-md transition-shadow">
                    <div class="p-6">
                        <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between">
                            <!-- Property Info -->
                            <div class="flex space-x-4 mb-4 lg:mb-0">
                                <div class="flex-shrink-0">
                                    {% if reservation.room.main_image %}
                                        <img src="{{ reservation.room.main_image.url }}" alt="{{ reservation.room.title }}" class="w-20 h-20 object-cover rounded-lg">
                                    {% else %}
                                        <div class="w-20 h-20 bg-gray-200 rounded-lg flex items-center justify-center">
                                            <i class="fas fa-home text-gray-400"></i>
                                        </div>
                                    {% endif %}
                                </div>
                                <div class="flex-1">
                                    <h3 class="font-semibold text-gray-900 mb-1">{{ reservation.room.title }}</h3>
                                    <p class="text-sm text-gray-600 mb-1">{{ reservation.room.city }}, {{ reservation.room.country }}</p>
                                    <p class="text-sm text-gray-500">
                                        {{ reservation.check_in|date:"M d" }} - {{ reservation.check_out|date:"M d, Y" }} · 
                                        {{ reservation.total_guests }} guest{{ reservation.total_guests|pluralize }}
                                    </p>
                                    <div class="mt-2">
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                            {% if reservation.status == 'confirmed' %}bg-green-100 text-green-800
                                            {% elif reservation.status == 'pending' %}bg-yellow-100 text-yellow-800
                                            {% elif reservation.status == 'cancelled' %}bg-red-100 text-red-800
                                            {% else %}bg-gray-100 text-gray-800{% endif %}">
                                            {{ reservation.get_status_display }}
                                        </span>
                                    </div>
                                </div>
                            </div>

                            <!-- Actions and Price -->
                            <div class="flex flex-col sm:flex-row sm:items-center sm:space-x-4">
                                <div class="text-right mb-4 sm:mb-0">
                                    <p class="font-semibold text-gray-900">${{ reservation.total_price|floatformat:0 }}</p>
                                    <p class="text-sm text-gray-500">Total paid</p>
                                </div>
                                
                                <div class="flex flex-wrap gap-2">
                                    <a href="{{ reservation.room.get_absolute_url }}" class="text-airbnb-red hover:text-red-600 text-sm font-medium px-3 py-1 border border-airbnb-red rounded-md hover:bg-red-50 transition-colors">
                                        View property
                                    </a>

                                    <!-- Download PDF -->
                                    <a href="{% url 'reservations:download_pdf' reservation.id %}" class="text-blue-600 hover:text-blue-800 text-sm font-medium px-3 py-1 border border-blue-600 rounded-md hover:bg-blue-50 transition-colors">
                                        <i class="fas fa-download mr-1"></i>PDF
                                    </a>

                                    {% if reservation.status == 'confirmed' %}
                                        <button class="text-gray-600 hover:text-gray-800 text-sm font-medium px-3 py-1 border border-gray-300 rounded-md hover:bg-gray-50 transition-colors">
                                            Contact host
                                        </button>

                                        <!-- Cancel button - only if more than 48h before check-in -->
                                        {% if reservation.can_cancel %}
                                            <button onclick="cancelReservation({{ reservation.id }}, '{{ reservation.room.title|escapejs }}')"
                                                    class="text-red-600 hover:text-red-800 text-sm font-medium px-3 py-1 border border-red-300 rounded-md hover:bg-red-50 transition-colors">
                                                <i class="fas fa-times mr-1"></i>Cancel
                                            </button>
                                        {% endif %}

                                        {% if reservation.check_out|date:"Y-m-d" < today|date:"Y-m-d" %}
                                            <button class="text-green-600 hover:text-green-800 text-sm font-medium px-3 py-1 border border-green-300 rounded-md hover:bg-green-50 transition-colors">
                                                <i class="fas fa-star mr-1"></i>Review
                                            </button>
                                        {% endif %}
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <!-- Expandable Details -->
                        <div class="mt-4 pt-4 border-t border-gray-200" x-data="{ expanded: false }">
                            <button @click="expanded = !expanded" class="flex items-center text-sm font-medium text-gray-600 hover:text-gray-800">
                                <span x-text="expanded ? 'Hide details' : 'Show details'"></span>
                                <i class="fas fa-chevron-down ml-1 transform transition-transform" :class="expanded ? 'rotate-180' : ''"></i>
                            </button>
                            
                            <div x-show="expanded" x-transition class="mt-4 grid grid-cols-1 md:grid-cols-3 gap-6">
                                <!-- Reservation Details -->
                                <div>
                                    <h4 class="font-medium text-gray-900 mb-2">Reservation details</h4>
                                    <div class="space-y-1 text-sm text-gray-600">
                                        <p>Reservation #{{ reservation.id|stringformat:"06d" }}</p>
                                        <p>Booked on {{ reservation.created_at|date:"M d, Y" }}</p>
                                        <p>{{ reservation.nights }} night{{ reservation.nights|pluralize }}</p>
                                    </div>
                                </div>
                                
                                <!-- Guest Details -->
                                <div>
                                    <h4 class="font-medium text-gray-900 mb-2">Guests</h4>
                                    <div class="space-y-1 text-sm text-gray-600">
                                        {% if reservation.adults > 0 %}
                                            <p>{{ reservation.adults }} adult{{ reservation.adults|pluralize }}</p>
                                        {% endif %}
                                        {% if reservation.children > 0 %}
                                            <p>{{ reservation.children }} child{{ reservation.children|pluralize }}</p>
                                        {% endif %}
                                        {% if reservation.infants > 0 %}
                                            <p>{{ reservation.infants }} infant{{ reservation.infants|pluralize }}</p>
                                        {% endif %}
                                    </div>
                                </div>
                                
                                <!-- Price Breakdown -->
                                <div>
                                    <h4 class="font-medium text-gray-900 mb-2">Price breakdown</h4>
                                    <div class="space-y-1 text-sm text-gray-600">
                                        <div class="flex justify-between">
                                            <span>${{ reservation.price_per_night|floatformat:0 }} x {{ reservation.nights }} nights</span>
                                            <span>${{ reservation.base_price|floatformat:0 }}</span>
                                        </div>
                                        {% if reservation.cleaning_fee > 0 %}
                                            <div class="flex justify-between">
                                                <span>Cleaning fee</span>
                                                <span>${{ reservation.cleaning_fee|floatformat:0 }}</span>
                                            </div>
                                        {% endif %}
                                        {% if reservation.service_fee > 0 %}
                                            <div class="flex justify-between">
                                                <span>Service fee</span>
                                                <span>${{ reservation.service_fee|floatformat:0 }}</span>
                                            </div>
                                        {% endif %}
                                        <div class="flex justify-between font-medium pt-1 border-t border-gray-200">
                                            <span>Total</span>
                                            <span>${{ reservation.total_price|floatformat:0 }}</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            {% endfor %}
        </div>

        <!-- Pagination -->
        {% if is_paginated %}
            <div class="mt-12 flex justify-center">
                <nav class="flex items-center space-x-2">
                    {% if page_obj.has_previous %}
                        <a href="?page={{ page_obj.previous_page_number }}" 
                           class="px-3 py-2 text-sm font-medium text-gray-500 hover:text-gray-700 border border-gray-300 rounded-md hover:bg-gray-50">
                            Previous
                        </a>
                    {% endif %}
                    
                    {% for num in page_obj.paginator.page_range %}
                        {% if page_obj.number == num %}
                            <span class="px-3 py-2 text-sm font-medium text-white bg-airbnb-red border border-airbnb-red rounded-md">
                                {{ num }}
                            </span>
                        {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                            <a href="?page={{ num }}" 
                               class="px-3 py-2 text-sm font-medium text-gray-500 hover:text-gray-700 border border-gray-300 rounded-md hover:bg-gray-50">
                                {{ num }}
                            </a>
                        {% endif %}
                    {% endfor %}
                    
                    {% if page_obj.has_next %}
                        <a href="?page={{ page_obj.next_page_number }}" 
                           class="px-3 py-2 text-sm font-medium text-gray-500 hover:text-gray-700 border border-gray-300 rounded-md hover:bg-gray-50">
                            Next
                        </a>
                    {% endif %}
                </nav>
            </div>
        {% endif %}
    {% else %}
        <!-- No Trips -->
        <div class="text-center py-12">
            <i class="fas fa-suitcase text-gray-300 text-6xl mb-4"></i>
            <h3 class="text-xl font-semibold text-gray-600 mb-2">No trips yet</h3>
            <p class="text-gray-500 mb-6">Time to dust off your bags and start planning your next adventure</p>
            <a href="{% url 'core:home' %}" class="inline-block bg-airbnb-red text-white px-6 py-3 rounded-lg font-medium hover:bg-red-600 transition-colors btn-primary">
                Start searching
            </a>
        </div>
    {% endif %}
</div>

<!-- Cancel Reservation Modal -->
<div id="cancelModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
        <div class="mt-3 text-center">
            <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100">
                <i class="fas fa-exclamation-triangle text-red-600"></i>
            </div>
            <h3 class="text-lg font-medium text-gray-900 mt-4">Cancel Reservation</h3>
            <div class="mt-2 px-7 py-3">
                <p class="text-sm text-gray-500" id="cancelMessage">
                    Are you sure you want to cancel your reservation for <span id="propertyName" class="font-medium"></span>?
                </p>
                <p class="text-xs text-gray-400 mt-2">
                    This action cannot be undone. You will receive a full refund according to our cancellation policy.
                </p>
            </div>
            <div class="items-center px-4 py-3">
                <button id="confirmCancel" class="px-4 py-2 bg-red-600 text-white text-base font-medium rounded-md w-full shadow-sm hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-300">
                    Yes, Cancel Reservation
                </button>
                <button onclick="closeCancelModal()" class="mt-3 px-4 py-2 bg-gray-300 text-gray-800 text-base font-medium rounded-md w-full shadow-sm hover:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-gray-300">
                    Keep Reservation
                </button>
            </div>
        </div>
    </div>
</div>

<script>
let currentReservationId = null;

function cancelReservation(reservationId, propertyName) {
    currentReservationId = reservationId;
    document.getElementById('propertyName').textContent = propertyName;
    document.getElementById('cancelModal').classList.remove('hidden');
}

function closeCancelModal() {
    document.getElementById('cancelModal').classList.add('hidden');
    currentReservationId = null;
}

document.getElementById('confirmCancel').addEventListener('click', function() {
    if (currentReservationId) {
        // Show loading state
        this.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Cancelling...';
        this.disabled = true;

        // Send cancel request
        fetch(`/reservations/cancel/${currentReservationId}/`, {
            method: 'POST',
            headers: {
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                'Content-Type': 'application/json',
            },
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Reload page to show updated status
                window.location.reload();
            } else {
                alert('Error: ' + (data.error || 'Failed to cancel reservation'));
                this.innerHTML = 'Yes, Cancel Reservation';
                this.disabled = false;
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('An error occurred while cancelling the reservation');
            this.innerHTML = 'Yes, Cancel Reservation';
            this.disabled = false;
        });
    }
});

// Close modal when clicking outside
document.getElementById('cancelModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeCancelModal();
    }
});
</script>

<!-- Add CSRF token for AJAX requests -->
{% csrf_token %}

{% endblock %}
