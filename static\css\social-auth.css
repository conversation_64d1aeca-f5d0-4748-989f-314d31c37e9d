/* Social Authentication Styles */

.social-button {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    padding: 12px 16px;
    border: 1px solid #d1d5db;
    border-radius: 8px;
    background-color: #ffffff;
    color: #374151;
    text-decoration: none;
    font-weight: 500;
    font-size: 14px;
    transition: all 0.2s ease;
    margin-bottom: 12px;
}

.social-button:hover {
    background-color: #f9fafb;
    border-color: #9ca3af;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.social-button:active {
    transform: translateY(0);
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.social-icon {
    width: 20px;
    height: 20px;
    margin-right: 12px;
    flex-shrink: 0;
}

/* Google Button */
.google-button {
    border-color: #dadce0;
}

.google-button:hover {
    background-color: #f8f9fa;
    border-color: #dadce0;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* Apple Button */
.apple-button {
    background-color: #000000;
    color: #ffffff;
    border-color: #000000;
}

.apple-button:hover {
    background-color: #1a1a1a;
    color: #ffffff;
}

.apple-icon {
    color: #ffffff;
    font-size: 18px;
}

/* Facebook Button */
.facebook-button {
    border-color: #1877f2;
}

.facebook-button:hover {
    background-color: #f0f8ff;
    border-color: #1877f2;
}

.facebook-icon {
    color: #1877f2;
    font-size: 18px;
}

/* Email Button */
.email-button {
    border-color: #6b7280;
}

.email-button:hover {
    background-color: #f9fafb;
    border-color: #4b5563;
}

.email-icon {
    color: #6b7280;
    font-size: 16px;
}

/* Phone Button */
.phone-button {
    background: linear-gradient(135deg, #FF5A5F 0%, #E31C5F 100%);
    color: #ffffff;
    border: none;
    font-weight: 600;
}

.phone-button:hover {
    background: linear-gradient(135deg, #E31C5F 0%, #C41E3A 100%);
    color: #ffffff;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(255, 90, 95, 0.3);
}

/* Loading states */
.social-button.loading {
    opacity: 0.7;
    cursor: not-allowed;
    pointer-events: none;
}

.social-button.loading::after {
    content: '';
    width: 16px;
    height: 16px;
    border: 2px solid #d1d5db;
    border-top: 2px solid #374151;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-left: 8px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Responsive adjustments */
@media (max-width: 640px) {
    .social-button {
        padding: 14px 16px;
        font-size: 16px;
    }
    
    .social-icon {
        width: 22px;
        height: 22px;
    }
}

/* Focus states for accessibility */
.social-button:focus {
    outline: 2px solid #3b82f6;
    outline-offset: 2px;
}

.social-button:focus:not(:focus-visible) {
    outline: none;
}

/* Error states */
.social-button.error {
    border-color: #ef4444;
    background-color: #fef2f2;
    color: #dc2626;
}

.social-button.error:hover {
    background-color: #fee2e2;
}
