"""
Service WhatsApp multi-méthodes
Utilise plusieurs APIs pour maximiser les chances de succès
"""

import requests
import logging
import os
from django.conf import settings

logger = logging.getLogger(__name__)


class MultiWhatsAppService:
    """Service WhatsApp utilisant plusieurs méthodes"""
    
    def __init__(self):
        self.methods = [
            self._try_callmebot,
            self._try_whatsapp_api,
            self._try_twilio_alternative,
            self._try_email_fallback
        ]
        
    def send_verification_code(self, phone_number, verification_code):
        """
        Essaie plusieurs méthodes d'envoi WhatsApp
        
        Args:
            phone_number (str): Numéro de téléphone (+212603999557)
            verification_code (str): Code à 6 chiffres
            
        Returns:
            dict: Résultat de l'envoi
        """
        
        clean_phone = self._clean_phone_number(phone_number)
        message = f"🏠 *Nestria Verification*\n\nYour verification code is: *{verification_code}*\n\nThis code will expire in 5 minutes.\nDo not share this code with anyone.\n\nWelcome to Nestria! 🌟"
        
        # Essayer chaque méthode
        for i, method in enumerate(self.methods, 1):
            try:
                logger.info(f"🔄 Trying method {i}/{len(self.methods)}: {method.__name__}")
                result = method(clean_phone, message, verification_code)
                
                if result.get('success'):
                    logger.info(f"✅ Success with method {i}: {method.__name__}")
                    return result
                else:
                    logger.warning(f"❌ Method {i} failed: {result.get('error', 'Unknown error')}")
                    
            except Exception as e:
                logger.error(f"💥 Method {i} exception: {str(e)}")
                continue
        
        # Si toutes les méthodes échouent, retourner simulation
        logger.warning("⚠️ All methods failed, falling back to simulation")
        return self._simulate_whatsapp_send(phone_number, verification_code)
    
    def _try_callmebot(self, phone_number, message, verification_code):
        """Méthode 1: CallMeBot API"""
        api_key = os.getenv('CALLMEBOT_API_KEY', '')
        
        if not api_key or api_key in ['', 'YOUR_REAL_API_KEY_HERE', 'test_key_pending']:
            return {'success': False, 'error': 'CallMeBot API key not configured'}
        
        try:
            params = {
                'phone': phone_number.replace('+', ''),
                'text': message,
                'apikey': api_key
            }
            
            response = requests.get("https://api.callmebot.com/whatsapp.php", params=params, timeout=10)
            
            if response.status_code == 200 and "Message queued" in response.text:
                return {
                    'success': True,
                    'message_id': f'callmebot_{verification_code}',
                    'method': 'callmebot'
                }
            else:
                return {'success': False, 'error': f'CallMeBot error: {response.text}'}
                
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def _try_whatsapp_api(self, phone_number, message, verification_code):
        """Méthode 2: WhatsApp Business API gratuite"""
        try:
            # Utiliser une API WhatsApp gratuite alternative
            # Par exemple: https://www.whatsapp-api.com/ ou similaire
            
            # Pour l'instant, simulation car nécessite configuration
            return {'success': False, 'error': 'WhatsApp Business API not configured'}
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def _try_twilio_alternative(self, phone_number, message, verification_code):
        """Méthode 3: Alternative Twilio ou SMS"""
        try:
            # Essayer d'envoyer par SMS si WhatsApp échoue
            # Utiliser une API SMS gratuite comme TextBelt
            
            sms_message = f"Nestria Verification Code: {verification_code}\n\nThis code expires in 5 minutes. Do not share."
            
            # TextBelt API (gratuit pour les tests)
            response = requests.post('https://textbelt.com/text', {
                'phone': phone_number,
                'message': sms_message,
                'key': 'textbelt'  # Clé gratuite limitée
            }, timeout=10)
            
            if response.status_code == 200:
                result = response.json()
                if result.get('success'):
                    return {
                        'success': True,
                        'message_id': f'sms_{verification_code}',
                        'method': 'sms'
                    }
            
            return {'success': False, 'error': 'SMS service failed'}
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def _try_email_fallback(self, phone_number, message, verification_code):
        """Méthode 4: Fallback par email si numéro connu"""
        try:
            # Si on a l'email de l'utilisateur en session, envoyer par email
            # Cette méthode nécessiterait l'accès à la session Django
            
            return {'success': False, 'error': 'Email fallback not implemented'}
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def _simulate_whatsapp_send(self, phone_number, verification_code):
        """Simulation finale si tout échoue"""
        
        print("\n" + "🔴" * 50)
        print("📱 WHATSAPP SIMULATION (ALL METHODS FAILED)")
        print("🔴" * 50)
        print(f"📞 TO: {phone_number}")
        print(f"🔐 CODE: {verification_code}")
        print(f"💬 MESSAGE:")
        print(f"   🏠 Nestria Verification")
        print(f"   Your verification code is: {verification_code}")
        print(f"   This code will expire in 5 minutes.")
        print(f"   Welcome to Nestria! 🌟")
        print("🔴" * 50)
        print("⚠️  POUR ACTIVER L'ENVOI RÉEL:")
        print("1. Configurez CALLMEBOT_API_KEY dans .env")
        print("2. OU configurez WhatsApp Business API")
        print("3. OU utilisez Twilio WhatsApp")
        print("🔴" * 50 + "\n")
        
        return {
            'success': True,
            'message_id': f'simulation_{verification_code}',
            'status': 'simulated',
            'method': 'simulation'
        }
    
    def _clean_phone_number(self, phone_number):
        """Nettoie le numéro de téléphone"""
        if not phone_number:
            return None
            
        # Enlever tous les caractères non numériques sauf le +
        clean = ''.join(c for c in phone_number if c.isdigit() or c == '+')
        
        # S'assurer qu'il commence par +
        if not clean.startswith('+'):
            clean = '+' + clean
            
        return clean


# Instance globale
multi_whatsapp_service = MultiWhatsAppService()


def send_whatsapp_verification_multi(phone_number, verification_code):
    """
    Fonction helper pour envoyer via multi-méthodes
    """
    result = multi_whatsapp_service.send_verification_code(phone_number, verification_code)
    return result.get('success', False)
