# Configuration Google OAuth pour Nestria

## 🎯 Objectif
Configurer l'authentification Google OAuth pour permettre aux utilisateurs de se connecter avec leur compte Google et récupérer automatiquement leurs informations (nom, prénom, email, photo).

## 📋 Étapes de Configuration

### 1. Créer un Projet Google Cloud

1. Aller sur [Google Cloud Console](https://console.cloud.google.com/)
2. Cliquer sur "Sélectionner un projet" → "Nouveau projet"
3. Nom du projet : `Nestria OAuth`
4. C<PERSON>r sur "Créer"

### 2. Activer l'API Google Identity

1. Dans le menu de gauche : **APIs & Services** → **Library**
2. Rechercher : `Google+ API` ou `Google Identity`
3. Cliquer sur **Google+ API** → **Activer**
4. Rechercher aussi : `People API` → **Activer**

### 3. Créer des Identifiants OAuth 2.0

1. **APIs & Services** → **Credentials**
2. <PERSON><PERSON><PERSON> sur **+ CREATE CREDENTIALS** → **OAuth client ID**
3. <PERSON>, configurer l'écran de consentement OAuth :
   - Type d'application : **External**
   - Nom de l'application : `Nestria`
   - Email de support utilisateur : votre email
   - Logo de l'application : (optionnel)
   - Domaine autorisé : `localhost`
   - Email de contact développeur : votre email
   - Scopes : `email`, `profile`, `openid`

4. Créer l'identifiant OAuth :
   - Type d'application : **Web application**
   - Nom : `Nestria Web OAuth`
   - **Origines JavaScript autorisées** :
     ```
     http://localhost:8000
     http://127.0.0.1:8000
     ```
   - **URIs de redirection autorisés** :
     ```
     http://localhost:8000/accounts/google/login/callback/
     http://127.0.0.1:8000/accounts/google/login/callback/
     ```

### 4. Récupérer les Clés

Après création, vous obtiendrez :
- **Client ID** : `************-abcdefghijklmnopqrstuvwxyz.apps.googleusercontent.com`
- **Client Secret** : `GOCSPX-abcdefghijklmnopqrstuvwxyz`

### 5. Configurer Django

Exécuter le script de configuration :

```bash
python configure_real_google_oauth.py
```

Ou manuellement dans l'admin Django :
1. Aller sur `http://127.0.0.1:8000/admin/`
2. **Social applications** → **Google OAuth**
3. Remplacer :
   - **Client id** : votre vrai Client ID
   - **Secret key** : votre vrai Client Secret

## 🧪 Test de l'Authentification

1. Aller sur : `http://127.0.0.1:8000/signup/`
2. Cliquer sur **Continue with Google**
3. Vous devriez voir l'écran de consentement Google
4. Après autorisation, redirection vers `finish-signup` avec les données pré-remplies

## 🔧 URLs Importantes

- **OAuth Login** : `/accounts/google/login/`
- **OAuth Callback** : `/accounts/google/login/callback/`
- **Admin Social Apps** : `/admin/socialaccount/socialapp/`

## 🎯 Données Récupérées

Après authentification Google, les données suivantes sont disponibles :
- `email` : Email de l'utilisateur
- `first_name` : Prénom
- `last_name` : Nom de famille
- `picture` : URL de la photo de profil
- `verified_email` : Email vérifié (true/false)

Ces données sont automatiquement stockées en session et utilisées pour pré-remplir le formulaire `finish-signup`.

## 🚨 Sécurité

- Ne jamais commiter les vraies clés dans le code
- Utiliser des variables d'environnement en production
- Configurer HTTPS en production
- Limiter les domaines autorisés en production
