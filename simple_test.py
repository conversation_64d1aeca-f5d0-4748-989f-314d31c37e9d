#!/usr/bin/env python
"""
Test simple des URLs
"""
import requests

def test_simple():
    base_url = "http://127.0.0.1:8000"
    
    print("🧪 Test simple des URLs")
    
    # Test page d'accueil
    try:
        response = requests.get(f"{base_url}/")
        print(f"Page d'accueil: {response.status_code}")
    except Exception as e:
        print(f"Erreur page d'accueil: {e}")
    
    # Test page signup
    try:
        response = requests.get(f"{base_url}/signup/")
        print(f"Page signup: {response.status_code}")
    except Exception as e:
        print(f"Erreur page signup: {e}")
    
    print("✅ Tests terminés")

if __name__ == '__main__':
    test_simple()
