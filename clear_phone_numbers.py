#!/usr/bin/env python
"""
Script pour supprimer tous les numéros de téléphone de la base de données
"""

import os
import sys
import django

# Configuration Django
sys.path.append('.')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'airbnb_clone.settings')
django.setup()

from django.contrib.auth.models import User
from core.models import UserProfile

def clear_phone_numbers():
    """Supprimer tous les numéros de téléphone de la base de données"""
    
    print("🗑️ SUPPRESSION DES NUMÉROS DE TÉLÉPHONE")
    print("=" * 50)
    
    try:
        # Compter les profils avec numéros
        profiles_with_phone = UserProfile.objects.exclude(phone_number='')
        count_before = profiles_with_phone.count()
        
        print(f"📊 Profils avec numéros avant suppression: {count_before}")
        
        if count_before > 0:
            # Afficher les numéros existants
            print("\n📱 Numéros existants:")
            for profile in profiles_with_phone:
                print(f"   • {profile.user.email}: {profile.phone_number}")
        
        # Supprimer tous les numéros de téléphone (utiliser chaîne vide au lieu de NULL)
        updated_count = UserProfile.objects.update(
            phone_number='',
            phone_verified=False
        )
        
        print(f"\n✅ {updated_count} profils mis à jour")
        print("✅ Tous les numéros de téléphone supprimés")
        print("✅ phone_verified remis à False")
        
        # Vérification
        remaining = UserProfile.objects.exclude(phone_number='').count()
        print(f"📊 Profils avec numéros après suppression: {remaining}")
        
        if remaining == 0:
            print("🎉 SUPPRESSION RÉUSSIE!")
        else:
            print("⚠️ Quelques numéros restent")
            
    except Exception as e:
        print(f"❌ Erreur: {str(e)}")
        return False
    
    return True

def show_database_status():
    """Afficher le statut de la base de données"""
    
    print("\n📊 STATUT BASE DE DONNÉES")
    print("=" * 30)
    
    try:
        # Compter les utilisateurs
        total_users = User.objects.count()
        print(f"👥 Total utilisateurs: {total_users}")
        
        # Compter les profils
        total_profiles = UserProfile.objects.count()
        print(f"📋 Total profils: {total_profiles}")
        
        # Profils avec email
        profiles_with_email = UserProfile.objects.exclude(user__email__isnull=True).exclude(user__email='')
        print(f"📧 Profils avec email: {profiles_with_email.count()}")
        
        # Profils avec numéro
        profiles_with_phone = UserProfile.objects.exclude(phone_number='')
        print(f"📱 Profils avec numéro: {profiles_with_phone.count()}")
        
        # Profils vérifiés
        verified_profiles = UserProfile.objects.filter(phone_verified=True)
        print(f"✅ Profils vérifiés: {verified_profiles.count()}")
        
        print("\n📧 Emails existants:")
        for user in User.objects.exclude(email=''):
            print(f"   • {user.email}")
            
    except Exception as e:
        print(f"❌ Erreur: {str(e)}")

if __name__ == '__main__':
    print("🚀 NETTOYAGE BASE DE DONNÉES NESTRIA")
    print("=" * 40)
    
    # Afficher le statut avant
    show_database_status()
    
    # Supprimer les numéros
    success = clear_phone_numbers()
    
    if success:
        # Afficher le statut après
        show_database_status()
        
        print("\n🎯 PRÊT POUR NOUVEAUX TESTS!")
        print("✅ Base de données nettoyée")
        print("✅ Numéros avec + peuvent être testés")
        print("✅ Pas de conflits de numéros")
    else:
        print("\n❌ Échec du nettoyage")
