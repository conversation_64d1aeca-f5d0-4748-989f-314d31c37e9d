# Système d'Inscription Nestria avec Vérification WhatsApp

## Vue d'ensemble

Ce système d'inscription implémente exactement les spécifications demandées avec un processus en 6 étapes incluant la vérification par WhatsApp et un onboarding interactif.

## Processus d'Inscription

### 1. **Inscription par Téléphone** (`/signup/`)
- Interface moderne avec options d'inscription sociale
- Saisie du numéro de téléphone avec sélection du code pays
- Validation du format du numéro
- Vérification que le numéro n'existe pas déjà

### 2. **Vérification WhatsApp** (`/phone-verification/`)
- Code de 6 chiffres généré automatiquement
- Interface avec 6 champs séparés pour la saisie
- Gestion automatique du focus entre les champs
- Expiration du code après 5 minutes
- Option de renvoyer le code
- **En mode développement**: Le code est affiché dans les messages pour faciliter les tests

### 3. **Informations de Contact** (`/contact-info/`)
- Saisie de l'email avec validation
- Acceptation des conditions d'utilisation
- Options de préférences marketing
- Liens vers les politiques de confidentialité

### 4. **Finalisation de l'Inscription** (`/finish-signup/`)
- Nom et prénom (correspondant à l'ID gouvernemental)
- Date de naissance avec validation d'âge (18+ ans)
- Message d'erreur automatique si moins de 18 ans
- Confirmation de l'email

### 5. **Engagement Communautaire** (`/community-commitment/`)
- Page d'engagement aux valeurs de Nestria
- Texte exact comme spécifié
- Options "Agree and continue" ou "Decline"
- Création automatique du compte utilisateur après acceptation

### 6. **Onboarding Interactif** (`/onboarding/`)
- Guide en 3 étapes pour comprendre Nestria
- Animations et transitions fluides
- Progression automatique optionnelle
- Interface responsive et moderne

## Fonctionnalités Supplémentaires

### Modal Wishlist
- Interface exacte comme demandée
- Validation du nom (50 caractères max)
- Compteur de caractères en temps réel
- Gestion des erreurs et succès

### Validation d'Âge
- Message d'erreur exact: "You must be 18 or older to use Nestria. Other people won't see your birthday."
- Validation côté client et serveur
- Blocage de la soumission si moins de 18 ans

### Sécurité et Validation
- Protection CSRF sur tous les formulaires
- Validation des données côté serveur
- Gestion des sessions sécurisée
- Nettoyage automatique des données de session

## URLs Disponibles

```
/signup/                    # Page d'inscription principale
/phone-verification/        # Vérification du code WhatsApp
/contact-info/             # Informations de contact
/finish-signup/            # Finalisation de l'inscription
/community-commitment/     # Engagement communautaire
/onboarding/              # Guide d'onboarding
/signup-demo/             # Page de démonstration complète
```

## API Endpoints

```
POST /resend-verification-code/  # Renvoyer le code de vérification
POST /create-wishlist/          # Créer une nouvelle wishlist
```

## Comment Tester

### 1. **Accéder à la Démonstration**
```
http://127.0.0.1:8000/signup-demo/
```

### 2. **Tester l'Inscription Complète**
1. Aller sur `/signup/`
2. Utiliser n'importe quel numéro (ex: +212 603999557)
3. Le code de vérification s'affiche dans les messages (mode dev)
4. Suivre toutes les étapes jusqu'à l'onboarding

### 3. **Codes de Test**
- **Numéro de téléphone**: +212 603999557 (ou tout autre)
- **Code de vérification**: Affiché dans les messages Django en mode développement
- **Date de naissance**: Utiliser une date qui donne 18+ ans (ex: 01/01/2000)

## Structure des Fichiers

### Templates
```
templates/registration/
├── phone_verification.html      # Vérification WhatsApp
├── contact_info.html           # Informations de contact
├── finish_signup.html          # Finalisation
├── community_commitment.html   # Engagement communautaire
└── signup.html                # Page d'inscription principale

templates/core/
├── onboarding.html            # Guide d'onboarding
└── signup_demo.html           # Page de démonstration

templates/components/
└── create_wishlist_modal.html # Modal de création de wishlist
```

### Vues Python
```python
# core/views.py
PhoneSignUpView              # Inscription par téléphone
PhoneVerificationView        # Vérification du code
ContactInfoView             # Informations de contact
FinishSignupView            # Finalisation
CommunityCommitmentView     # Engagement communautaire
OnboardingView              # Guide d'onboarding
SignupDemoView              # Page de démonstration

# Fonctions utilitaires
generate_verification_code() # Génération du code 6 chiffres
send_whatsapp_verification() # Simulation envoi WhatsApp
```

## Intégration WhatsApp Réelle

Pour une intégration WhatsApp réelle, remplacer la fonction `send_whatsapp_verification()` par:

```python
def send_whatsapp_verification(phone_number, code):
    """Envoi réel via WhatsApp Business API"""
    import requests
    
    url = "https://graph.facebook.com/v17.0/YOUR_PHONE_NUMBER_ID/messages"
    headers = {
        "Authorization": f"Bearer {WHATSAPP_ACCESS_TOKEN}",
        "Content-Type": "application/json"
    }
    
    data = {
        "messaging_product": "whatsapp",
        "to": phone_number,
        "type": "text",
        "text": {
            "body": f"Your Nestria verification code is: {code}. This code will expire in 5 minutes."
        }
    }
    
    response = requests.post(url, headers=headers, json=data)
    return response.status_code == 200
```

## Personnalisation

### Couleurs et Styles
- Couleur principale: `#FF5A5F` (nestria-red)
- Framework CSS: Tailwind CSS
- Icons: Font Awesome 6

### Messages et Textes
Tous les textes peuvent être modifiés dans les templates HTML correspondants.

### Validation d'Âge
Modifier la limite d'âge dans `core/models.py` et `core/views.py`.

## Support et Maintenance

- **Mode Développement**: Codes de vérification affichés dans les messages
- **Logs**: Codes imprimés dans la console du serveur
- **Session Management**: Nettoyage automatique après inscription
- **Error Handling**: Gestion complète des erreurs avec messages utilisateur

## Prochaines Étapes

1. **Intégration WhatsApp Business API** pour l'envoi réel de SMS
2. **Système de Wishlist** complet avec base de données
3. **Analytics** pour suivre le taux de conversion du processus d'inscription
4. **Tests automatisés** pour valider le processus complet
