#!/usr/bin/env python
"""
Production deployment script for Airbnb Clone
"""

import os
import sys
import subprocess

def run_command(command, description):
    """Run a command and handle errors"""
    print(f"\n🔄 {description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description} completed successfully")
        return result.stdout
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} failed: {e}")
        print(f"Error output: {e.stderr}")
        sys.exit(1)

def main():
    print("🚀 Starting Airbnb Clone Production Deployment")
    
    # Set production environment
    os.environ['DJANGO_SETTINGS_MODULE'] = 'airbnb_clone.settings_production'
    
    # Install dependencies
    run_command("pip install -r requirements.txt", "Installing dependencies")
    
    # Collect static files
    run_command("python manage.py collectstatic --noinput", "Collecting static files")
    
    # Run migrations
    run_command("python manage.py migrate", "Running database migrations")
    
    # Create superuser if it doesn't exist
    print("\n🔄 Creating superuser...")
    try:
        subprocess.run([
            "python", "manage.py", "shell", "-c",
            "from django.contrib.auth.models import User; "
            "User.objects.filter(username='admin').exists() or "
            "User.objects.create_superuser('admin', '<EMAIL>', 'admin123')"
        ], check=True)
        print("✅ Superuser created or already exists")
    except subprocess.CalledProcessError:
        print("⚠️  Could not create superuser")
    
    # Populate sample data
    try:
        run_command("python manage.py populate_data", "Populating sample room data")
        run_command("python manage.py populate_destinations", "Populating destination data")
    except:
        print("⚠️  Sample data population failed (may already exist)")
    
    print("\n🎉 Deployment completed successfully!")
    print("\n📋 Next steps:")
    print("1. Configure your web server (Nginx/Apache)")
    print("2. Set up SSL certificates")
    print("3. Configure environment variables")
    print("4. Start the application server:")
    print("   - WSGI: gunicorn --bind 0.0.0.0:8000 wsgi_production:application")
    print("   - ASGI: uvicorn asgi_production:application --host 0.0.0.0 --port 8000")

if __name__ == "__main__":
    main()
