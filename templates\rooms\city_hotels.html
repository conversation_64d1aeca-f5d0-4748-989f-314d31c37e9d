{% extends 'base.html' %}
{% load static %}

{% block title %}Hotels in {{ city|title }} - Airbnb Clone{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
<style>
.city-hero {
    background: linear-gradient(135deg, rgba(255, 90, 95, 0.9), rgba(227, 28, 95, 0.9));
    position: relative;
    overflow: hidden;
}

.city-hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: url('{% if destination and destination.main_image %}{{ destination.main_image.url }}{% else %}{% static "images/default-city.jpg" %}{% endif %}');
    background-size: cover;
    background-position: center;
    opacity: 0.3;
    z-index: -1;
}

.hotel-card {
    transition: all 0.3s ease;
    border: 1px solid #e5e7eb;
}

.hotel-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    border-color: #FF5A5F;
}

.map-container {
    height: 400px;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
}

.filter-section {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 30px;
}

.stats-card {
    background: white;
    border-radius: 12px;
    padding: 20px;
    text-align: center;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}
</style>
{% endblock %}

{% block content %}
<!-- City Hero Section -->
<div class="city-hero text-white py-20">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center">
            <h1 class="text-5xl font-bold mb-4">Hotels in {{ city|title }}</h1>
            {% if destination %}
                <p class="text-xl opacity-90 mb-6 max-w-3xl mx-auto">{{ destination.country }}</p>
                <div class="flex items-center justify-center space-x-6 text-lg">
                    <div class="flex items-center">
                        <i class="fas fa-home mr-2"></i>
                        <span>{{ rooms|length }} properties</span>
                    </div>
                    {% if destination.latitude and destination.longitude %}
                        <div class="flex items-center">
                            <i class="fas fa-map-marker-alt mr-2"></i>
                            <span>{{ destination.latitude|floatformat:1 }}°, {{ destination.longitude|floatformat:1 }}°</span>
                        </div>
                    {% endif %}
                </div>
            {% else %}
                <p class="text-xl opacity-90 mb-6">Discover amazing places to stay</p>
                <div class="flex items-center justify-center">
                    <i class="fas fa-home mr-2"></i>
                    <span class="text-lg">{{ rooms|length }} properties available</span>
                </div>
            {% endif %}
        </div>
    </div>
</div>

<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
    <!-- Statistics Section -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-12">
        <div class="stats-card">
            <div class="text-3xl font-bold text-airbnb-red mb-2">{{ rooms|length }}</div>
            <div class="text-gray-600">Properties</div>
        </div>
        <div class="stats-card">
            <div class="text-3xl font-bold text-airbnb-red mb-2">
                {% if rooms %}${{ rooms.0.price_per_night|floatformat:0 }}+{% else %}N/A{% endif %}
            </div>
            <div class="text-gray-600">From per night</div>
        </div>
        <div class="stats-card">
            <div class="text-3xl font-bold text-airbnb-red mb-2">
                {% regroup rooms by room_type.name as room_types %}{{ room_types|length }}
            </div>
            <div class="text-gray-600">Property types</div>
        </div>
        <div class="stats-card">
            <div class="text-3xl font-bold text-airbnb-red mb-2">4.8</div>
            <div class="text-gray-600">Average rating</div>
        </div>
    </div>

    <!-- Map Section -->
    <div class="mb-12">
        <h2 class="text-3xl font-bold text-gray-900 mb-6 text-center">
            <i class="fas fa-map-marked-alt text-airbnb-red mr-3"></i>
            Explore {{ city|title }} on the Map
        </h2>
        <div id="map" class="map-container"></div>
    </div>

    <!-- Filters Section -->
    <div class="filter-section">
        <h3 class="text-xl font-semibold text-gray-900 mb-4">
            <i class="fas fa-filter text-airbnb-red mr-2"></i>
            Filter Properties
        </h3>
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
            <select class="px-4 py-2 border border-gray-300 rounded-lg focus:ring-airbnb-red focus:border-airbnb-red">
                <option>All Property Types</option>
                {% regroup rooms by room_type.name as room_types %}
                {% for room_type in room_types %}
                    <option>{{ room_type.grouper }}</option>
                {% endfor %}
            </select>
            <select class="px-4 py-2 border border-gray-300 rounded-lg focus:ring-airbnb-red focus:border-airbnb-red">
                <option>Price Range</option>
                <option>$0 - $100</option>
                <option>$100 - $200</option>
                <option>$200 - $500</option>
                <option>$500+</option>
            </select>
            <select class="px-4 py-2 border border-gray-300 rounded-lg focus:ring-airbnb-red focus:border-airbnb-red">
                <option>Guests</option>
                <option>1 guest</option>
                <option>2 guests</option>
                <option>3+ guests</option>
            </select>
            <button class="bg-airbnb-red text-white px-6 py-2 rounded-lg hover:bg-red-600 transition-colors">
                <i class="fas fa-search mr-2"></i>Apply Filters
            </button>
        </div>
    </div>

    <!-- Hotels Grid -->
    <div class="mb-12">
        <h2 class="text-3xl font-bold text-gray-900 mb-8 text-center">Available Properties</h2>
        
        {% if rooms %}
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                {% for room in rooms %}
                    <div class="hotel-card bg-white rounded-xl overflow-hidden">
                        <a href="{% url 'rooms:room_detail' room.id %}" class="block">
                            <!-- Room Image -->
                            <div class="relative h-48 overflow-hidden">
                                {% if room.main_image %}
                                    <img src="{{ room.main_image.url }}"
                                         alt="{{ room.title }}"
                                         class="w-full h-full object-cover">
                                {% else %}
                                    <div class="w-full h-full bg-gradient-to-br from-gray-200 to-gray-300 flex items-center justify-center">
                                        <i class="fas fa-home text-gray-400 text-3xl"></i>
                                    </div>
                                {% endif %}

                                <!-- Favorite Button -->
                                <button class="absolute top-3 right-3 favorite-btn heart-icon" data-property-id="{{ room.id }}">
                                    <i class="far fa-heart text-white text-xl"></i>
                                </button>
                                
                                <!-- Price Badge -->
                                <div class="absolute top-4 left-4 bg-white bg-opacity-90 px-3 py-1 rounded-full">
                                    <span class="text-sm font-semibold text-gray-900">${{ room.price_per_night|floatformat:0 }}/night</span>
                                </div>
                                
                                <!-- Heart Icon -->
                                <div class="absolute top-4 right-4 w-8 h-8 bg-white bg-opacity-90 rounded-full flex items-center justify-center">
                                    <i class="far fa-heart text-gray-600 hover:text-airbnb-red cursor-pointer"></i>
                                </div>
                            </div>
                            
                            <!-- Room Details -->
                            <div class="p-6">
                                <div class="flex items-center justify-between mb-2">
                                    <span class="text-sm text-gray-500">{{ room.room_type.name }}</span>
                                    {% if room.average_rating > 0 %}
                                        <div class="flex items-center">
                                            <i class="fas fa-star text-yellow-400 text-sm"></i>
                                            <span class="ml-1 text-sm font-medium">{{ room.average_rating|floatformat:1 }}</span>
                                        </div>
                                    {% endif %}
                                </div>
                                
                                <h3 class="text-lg font-semibold text-gray-900 mb-2 line-clamp-2">{{ room.title }}</h3>
                                
                                <div class="flex items-center text-sm text-gray-600 mb-3">
                                    <i class="fas fa-map-marker-alt mr-1"></i>
                                    <span>{{ room.city }}, {{ room.country }}</span>
                                </div>
                                
                                <div class="flex items-center justify-between text-sm text-gray-600">
                                    <div class="flex items-center">
                                        <i class="fas fa-users mr-1"></i>
                                        <span>{{ room.max_guests }} guests</span>
                                    </div>
                                    <div class="flex items-center">
                                        <i class="fas fa-bed mr-1"></i>
                                        <span>{{ room.bedrooms }} bed{{ room.bedrooms|pluralize }}</span>
                                    </div>
                                </div>
                            </div>
                        </a>
                    </div>
                {% endfor %}
            </div>
            
            <!-- Pagination -->
            {% if is_paginated %}
                <div class="flex justify-center mt-12">
                    <nav class="flex items-center space-x-2">
                        {% if page_obj.has_previous %}
                            <a href="?page={{ page_obj.previous_page_number }}" 
                               class="px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50">Previous</a>
                        {% endif %}
                        
                        <span class="px-4 py-2 bg-airbnb-red text-white rounded-lg">
                            Page {{ page_obj.number }} of {{ page_obj.paginator.num_pages }}
                        </span>
                        
                        {% if page_obj.has_next %}
                            <a href="?page={{ page_obj.next_page_number }}" 
                               class="px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50">Next</a>
                        {% endif %}
                    </nav>
                </div>
            {% endif %}
        {% else %}
            <div class="text-center py-12">
                <i class="fas fa-home text-gray-300 text-6xl mb-4"></i>
                <h3 class="text-xl font-semibold text-gray-900 mb-2">No properties found</h3>
                <p class="text-gray-600">There are currently no properties available in {{ city|title }}.</p>
                <a href="{% url 'rooms:destinations' %}" 
                   class="inline-block mt-4 bg-airbnb-red text-white px-6 py-3 rounded-lg hover:bg-red-600 transition-colors">
                    Explore Other Destinations
                </a>
            </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize map
    const map = L.map('map').setView([{{ center_lat }}, {{ center_lng }}], 12);
    
    // Add tile layer
    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
        attribution: '© OpenStreetMap contributors'
    }).addTo(map);
    
    // Add markers for each room
    {% for room in rooms %}
        {% if room.latitude and room.longitude %}
            const marker{{ forloop.counter }} = L.marker([{{ room.latitude }}, {{ room.longitude }}])
                .addTo(map)
                .bindPopup(`
                    <div class="p-2">
                        <h4 class="font-semibold text-sm mb-1">{{ room.title }}</h4>
                        <p class="text-xs text-gray-600 mb-2">{{ room.room_type.name }}</p>
                        <div class="flex items-center justify-between">
                            <span class="text-sm font-medium text-airbnb-red">\${{ room.price_per_night|floatformat:0 }}/night</span>
                            <a href="{% url 'rooms:room_detail' room.id %}" 
                               class="text-xs bg-airbnb-red text-white px-2 py-1 rounded hover:bg-red-600">
                                View
                            </a>
                        </div>
                    </div>
                `);
        {% endif %}
    {% endfor %}
    
    // Add city center marker if destination exists
    {% if destination and destination.latitude and destination.longitude %}
        const cityMarker = L.marker([{{ destination.latitude }}, {{ destination.longitude }}], {
            icon: L.divIcon({
                className: 'city-marker',
                html: '<div style="background: #FF5A5F; color: white; border-radius: 50%; width: 30px; height: 30px; display: flex; align-items: center; justify-content: center; font-weight: bold; border: 3px solid white; box-shadow: 0 2px 4px rgba(0,0,0,0.3);"><i class="fas fa-city" style="font-size: 12px;"></i></div>',
                iconSize: [30, 30],
                iconAnchor: [15, 15]
            })
        }).addTo(map)
        .bindPopup(`
            <div class="p-3 text-center">
                <h4 class="font-bold text-lg text-airbnb-red mb-2">{{ destination.name }}</h4>
                <p class="text-sm text-gray-600">{{ destination.country }}</p>
            </div>
        `);
    {% endif %}
});
</script>

<!-- Favorites Script -->
<script src="{% static 'js/favorites.js' %}"></script>
<script>
// Initialize favorites manager
document.addEventListener('DOMContentLoaded', function() {
    new FavoritesManager();
});
</script>
{% endblock %}
