{% extends 'base.html' %}
{% load static %}

{% block title %}Your Favorites - Nestria{% endblock %}

{% block content %}
<div class="min-h-screen bg-gray-50 dark:bg-gray-900 py-8">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-gray-900 dark:text-white mb-2">Your Favorites</h1>
            <p class="text-gray-600 dark:text-gray-400">Properties you've saved for later</p>
        </div>

        {% if favorites %}
            <!-- Favorites Grid -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                {% for favorite in favorites %}
                    <div class="property-card bg-white dark:bg-gray-800 rounded-lg overflow-hidden shadow-sm hover:shadow-lg transition-all duration-300">
                        <a href="{{ favorite.room.get_absolute_url }}" class="block">
                            <div class="relative">
                                {% if favorite.room.main_image %}
                                    <img src="{{ favorite.room.main_image.url }}" 
                                         alt="{{ favorite.room.title }}" 
                                         class="w-full h-48 object-cover">
                                {% else %}
                                    <div class="w-full h-48 bg-gray-200 dark:bg-gray-700 flex items-center justify-center">
                                        <i class="fas fa-home text-gray-400 text-3xl"></i>
                                    </div>
                                {% endif %}
                                
                                <!-- Favorite Heart Button -->
                                <button class="absolute top-3 right-3 p-2 rounded-full bg-white/80 hover:bg-white transition-colors heart-icon" 
                                        onclick="event.preventDefault(); toggleFavorite({{ favorite.room.id }}, this)">
                                    <i class="fas fa-heart text-red-500 text-sm liked"></i>
                                </button>
                            </div>
                            
                            <div class="p-4">
                                <div class="flex items-center justify-between mb-2">
                                    <h3 class="font-semibold text-gray-900 dark:text-white truncate">
                                        {{ favorite.room.city }}, {{ favorite.room.country }}
                                    </h3>
                                    {% if favorite.room.average_rating %}
                                        <div class="flex items-center">
                                            <i class="fas fa-star text-yellow-400 text-sm mr-1"></i>
                                            <span class="text-sm text-gray-600 dark:text-gray-400">
                                                {{ favorite.room.average_rating|floatformat:1 }}
                                            </span>
                                        </div>
                                    {% endif %}
                                </div>
                                
                                <p class="text-gray-600 dark:text-gray-400 text-sm mb-2 line-clamp-2">
                                    {{ favorite.room.title }}
                                </p>
                                
                                <div class="flex items-center justify-between">
                                    <div class="text-gray-500 dark:text-gray-400 text-sm">
                                        {{ favorite.room.max_guests }} guest{{ favorite.room.max_guests|pluralize }}
                                    </div>
                                    <div class="text-right">
                                        <span class="font-semibold text-gray-900 dark:text-white">
                                            ${{ favorite.room.price_per_night|floatformat:0 }}
                                        </span>
                                        <span class="text-gray-500 dark:text-gray-400 text-sm"> / night</span>
                                    </div>
                                </div>
                                
                                <div class="mt-2 text-xs text-gray-400 dark:text-gray-500">
                                    Added {{ favorite.created_at|date:"M d, Y" }}
                                </div>
                            </div>
                        </a>
                    </div>
                {% endfor %}
            </div>
        {% else %}
            <!-- Empty State -->
            <div class="text-center py-16">
                <div class="mb-6">
                    <i class="fas fa-heart text-gray-300 dark:text-gray-600 text-6xl"></i>
                </div>
                <h2 class="text-2xl font-semibold text-gray-900 dark:text-white mb-4">
                    No favorites yet
                </h2>
                <p class="text-gray-600 dark:text-gray-400 mb-8 max-w-md mx-auto">
                    Start exploring and save properties you love by clicking the heart icon.
                </p>
                <a href="{% url 'core:home' %}" 
                   class="inline-flex items-center px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg transition-colors">
                    <i class="fas fa-search mr-2"></i>
                    Start Exploring
                </a>
            </div>
        {% endif %}
    </div>
</div>

<!-- Include the favorite toggle functionality -->
<script>
async function toggleFavorite(propertyId, button) {
    const icon = button.querySelector('i');
    
    try {
        const response = await fetch(`/api/properties/${propertyId}/toggle-favorite/`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
            }
        });

        if (response.ok) {
            const data = await response.json();
            if (data.is_favorite) {
                icon.classList.remove('far');
                icon.classList.add('fas', 'liked');
            } else {
                // Remove the card from favorites page
                button.closest('.property-card').remove();
                
                // Check if no more favorites
                const remainingCards = document.querySelectorAll('.property-card');
                if (remainingCards.length === 0) {
                    location.reload(); // Reload to show empty state
                }
            }
        }
    } catch (error) {
        console.error('Error toggling favorite:', error);
    }
}
</script>
{% endblock %}
