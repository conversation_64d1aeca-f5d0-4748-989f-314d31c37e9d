#!/usr/bin/env python
"""
Script pour nettoyer les messages de session Django
"""
import os
import django

# Configuration Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'airbnb_clone.settings')
django.setup()

from django.contrib.sessions.models import Session
from django.contrib.messages.storage.session import SessionStorage

def clear_all_messages():
    """Nettoyer tous les messages de session"""
    try:
        # Supprimer toutes les sessions
        Session.objects.all().delete()
        print("✅ All session messages cleared successfully!")
        print("🔄 Please refresh your browser to see the clean pages.")
    except Exception as e:
        print(f"❌ Error clearing messages: {e}")

if __name__ == '__main__':
    clear_all_messages()
