"""
Système d'email professionnel pour Nestria
Système d'envoi d'emails réutilisable avec support SMTP complet
"""

import smtplib
import ssl
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from email.mime.image import MIMEImage
from email.utils import formataddr
import os
import base64
from typing import Optional, List, Dict, Any
import logging

# Configuration du logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class EmailConfig:
    """Configuration SMTP pour l'envoi d'emails"""
    
    def __init__(self, 
                 host: str = 'smtp.gmail.com',
                 port: int = 587,
                 username: str = '',
                 password: str = '',
                 use_tls: bool = True,
                 use_ssl: bool = False,
                 from_name: str = 'Nestria',
                 from_email: str = ''):
        self.host = host
        self.port = port
        self.username = username
        self.password = password
        self.use_tls = use_tls
        self.use_ssl = use_ssl
        self.from_name = from_name
        self.from_email = from_email or username

class NestricaEmailSender:
    """Système d'envoi d'emails professionnel pour Nestria"""
    
    def __init__(self, config: EmailConfig):
        self.config = config
        
    def send_mail(self, 
                  to: str | List[str], 
                  subject: str, 
                  text: str = '', 
                  html: str = '',
                  attachments: Optional[List[str]] = None,
                  cc: Optional[List[str]] = None,
                  bcc: Optional[List[str]] = None) -> Dict[str, Any]:
        """
        Envoie un email avec support texte et HTML
        
        Args:
            to: Destinataire(s) - string ou liste
            subject: Sujet de l'email
            text: Corps du message en texte brut
            html: Corps du message en HTML
            attachments: Liste des chemins vers les fichiers à attacher
            cc: Liste des destinataires en copie
            bcc: Liste des destinataires en copie cachée
            
        Returns:
            Dict avec le statut de l'envoi
        """
        try:
            # Validation des paramètres
            if not to:
                return {'success': False, 'error': 'Aucun destinataire spécifié'}
            
            if not subject:
                return {'success': False, 'error': 'Sujet manquant'}
                
            if not text and not html:
                return {'success': False, 'error': 'Contenu du message manquant'}
            
            # Normalisation des destinataires
            recipients = [to] if isinstance(to, str) else to
            cc_list = cc or []
            bcc_list = bcc or []
            
            # Création du message
            msg = MIMEMultipart('alternative')
            msg['Subject'] = subject
            msg['From'] = formataddr((self.config.from_name, self.config.from_email))
            msg['To'] = ', '.join(recipients)
            
            if cc_list:
                msg['Cc'] = ', '.join(cc_list)
            
            # Ajout du contenu texte
            if text:
                text_part = MIMEText(text, 'plain', 'utf-8')
                msg.attach(text_part)
            
            # Ajout du contenu HTML
            if html:
                html_part = MIMEText(html, 'html', 'utf-8')
                msg.attach(html_part)
            
            # Ajout des pièces jointes
            if attachments:
                for file_path in attachments:
                    if os.path.exists(file_path):
                        with open(file_path, 'rb') as f:
                            attachment = MIMEImage(f.read())
                            attachment.add_header('Content-Disposition', 
                                                f'attachment; filename={os.path.basename(file_path)}')
                            msg.attach(attachment)
            
            # Envoi de l'email
            all_recipients = recipients + cc_list + bcc_list
            
            if self.config.use_ssl:
                context = ssl.create_default_context()
                with smtplib.SMTP_SSL(self.config.host, self.config.port, context=context) as server:
                    server.login(self.config.username, self.config.password)
                    server.sendmail(self.config.from_email, all_recipients, msg.as_string())
            else:
                with smtplib.SMTP(self.config.host, self.config.port) as server:
                    if self.config.use_tls:
                        context = ssl.create_default_context()
                        server.starttls(context=context)
                    server.login(self.config.username, self.config.password)
                    server.sendmail(self.config.from_email, all_recipients, msg.as_string())
            
            logger.info(f"Email envoyé avec succès à {', '.join(recipients)}")
            return {
                'success': True, 
                'message': f'Email envoyé avec succès à {len(recipients)} destinataire(s)',
                'recipients': recipients
            }
            
        except smtplib.SMTPAuthenticationError:
            error_msg = "Erreur d'authentification SMTP. Vérifiez vos identifiants."
            logger.error(error_msg)
            return {'success': False, 'error': error_msg}
            
        except smtplib.SMTPConnectError:
            error_msg = f"Impossible de se connecter au serveur SMTP {self.config.host}:{self.config.port}"
            logger.error(error_msg)
            return {'success': False, 'error': error_msg}
            
        except smtplib.SMTPException as e:
            error_msg = f"Erreur SMTP : {str(e)}"
            logger.error(error_msg)
            return {'success': False, 'error': error_msg}
            
        except Exception as e:
            error_msg = f"Impossible d'envoyer l'email : {str(e)}"
            logger.error(error_msg)
            return {'success': False, 'error': error_msg}

def create_welcome_email_html(user_name: str = "Voyageur") -> str:
    """Crée un email de bienvenue HTML professionnel avec le logo Nestria"""
    
    # Logo Nestria en base64 (version simplifiée)
    nestria_logo_svg = """
    <svg width="120" height="40" viewBox="0 0 120 40" xmlns="http://www.w3.org/2000/svg">
        <defs>
            <linearGradient id="logoGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                <stop offset="0%" style="stop-color:#3B82F6;stop-opacity:1" />
                <stop offset="100%" style="stop-color:#8B5CF6;stop-opacity:1" />
            </linearGradient>
        </defs>
        <rect width="120" height="40" rx="8" fill="url(#logoGradient)"/>
        <text x="60" y="25" font-family="Arial, sans-serif" font-size="16" font-weight="bold" 
              text-anchor="middle" fill="white">NESTRIA</text>
    </svg>
    """
    
    html_template = f"""
    <!DOCTYPE html>
    <html lang="fr">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Bienvenue sur Nestria 🚀</title>
        <style>
            body {{
                margin: 0;
                padding: 0;
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                background-color: #f8fafc;
                line-height: 1.6;
            }}
            .container {{
                max-width: 600px;
                margin: 0 auto;
                background-color: #ffffff;
                box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            }}
            .header {{
                background: linear-gradient(135deg, #3B82F6 0%, #8B5CF6 100%);
                padding: 30px 20px;
                text-align: center;
                color: white;
            }}
            .logo {{
                margin-bottom: 20px;
            }}
            .content {{
                padding: 40px 30px;
            }}
            .welcome-title {{
                color: #1f2937;
                font-size: 28px;
                font-weight: bold;
                margin-bottom: 20px;
                text-align: center;
            }}
            .welcome-text {{
                color: #4b5563;
                font-size: 16px;
                margin-bottom: 30px;
                text-align: center;
            }}
            .features {{
                background-color: #f9fafb;
                border-radius: 12px;
                padding: 30px;
                margin: 30px 0;
            }}
            .feature-item {{
                display: flex;
                align-items: center;
                margin-bottom: 20px;
                padding: 15px;
                background-color: white;
                border-radius: 8px;
                box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            }}
            .feature-icon {{
                font-size: 24px;
                margin-right: 15px;
                width: 40px;
                text-align: center;
            }}
            .cta-button {{
                display: inline-block;
                background: linear-gradient(135deg, #3B82F6 0%, #8B5CF6 100%);
                color: white;
                padding: 15px 30px;
                text-decoration: none;
                border-radius: 8px;
                font-weight: bold;
                margin: 20px auto;
                text-align: center;
                display: block;
                max-width: 200px;
            }}
            .footer {{
                background-color: #1f2937;
                color: #9ca3af;
                padding: 30px;
                text-align: center;
                font-size: 14px;
            }}
            .social-links {{
                margin: 20px 0;
            }}
            .social-links a {{
                color: #3B82F6;
                text-decoration: none;
                margin: 0 10px;
            }}
        </style>
    </head>
    <body>
        <div class="container">
            <!-- Header avec logo -->
            <div class="header">
                <div class="logo">
                    {nestria_logo_svg}
                </div>
                <h1 style="margin: 0; font-size: 24px;">Bienvenue sur Nestria ! 🚀</h1>
                <p style="margin: 10px 0 0 0; opacity: 0.9;">Votre aventure commence maintenant</p>
            </div>
            
            <!-- Contenu principal -->
            <div class="content">
                <h2 class="welcome-title">Bonjour {user_name} !</h2>
                <p class="welcome-text">
                    Merci d'avoir rejoint notre communauté de voyageurs passionnés. 
                    Nestria vous ouvre les portes d'un monde d'expériences uniques et de destinations extraordinaires.
                </p>
                
                <!-- Fonctionnalités -->
                <div class="features">
                    <h3 style="color: #1f2937; margin-bottom: 20px; text-align: center;">
                        Découvrez ce qui vous attend
                    </h3>
                    
                    <div class="feature-item">
                        <div class="feature-icon">🏠</div>
                        <div>
                            <strong>Hébergements uniques</strong><br>
                            Des logements exceptionnels sélectionnés avec soin dans le monde entier
                        </div>
                    </div>
                    
                    <div class="feature-item">
                        <div class="feature-icon">🎭</div>
                        <div>
                            <strong>Expériences authentiques</strong><br>
                            Vivez des moments inoubliables avec des guides locaux passionnés
                        </div>
                    </div>
                    
                    <div class="feature-item">
                        <div class="feature-icon">💻</div>
                        <div>
                            <strong>Expériences virtuelles</strong><br>
                            Explorez le monde depuis chez vous avec nos visites en ligne
                        </div>
                    </div>
                    
                    <div class="feature-item">
                        <div class="feature-icon">🌍</div>
                        <div>
                            <strong>Destinations mondiales</strong><br>
                            Plus de 100 destinations dans 50 pays vous attendent
                        </div>
                    </div>
                </div>
                
                <!-- Call to action -->
                <a href="https://nestria.com" class="cta-button">
                    Commencer l'exploration
                </a>
                
                <p style="text-align: center; color: #6b7280; margin-top: 30px;">
                    Besoin d'aide ? Notre équipe est là pour vous accompagner 24h/24 et 7j/7.
                </p>
            </div>
            
            <!-- Footer -->
            <div class="footer">
                <p><strong>Nestria</strong> - Travel to Survive</p>
                <div class="social-links">
                    <a href="#">Facebook</a> |
                    <a href="#">Instagram</a> |
                    <a href="#">Twitter</a> |
                    <a href="#">LinkedIn</a>
                </div>
                <p>
                    📧 <EMAIL> | 📞 +33 1 23 45 67 89<br>
                    123 Avenue des Voyageurs, 75001 Paris, France
                </p>
                <p style="font-size: 12px; margin-top: 20px;">
                    Vous recevez cet email car vous vous êtes inscrit sur Nestria.<br>
                    <a href="#" style="color: #3B82F6;">Se désabonner</a> | 
                    <a href="#" style="color: #3B82F6;">Préférences email</a>
                </p>
            </div>
        </div>
    </body>
    </html>
    """
    
    return html_template

# Configuration par défaut pour les tests
DEFAULT_CONFIG = EmailConfig(
    host='smtp.gmail.com',
    port=587,
    username='<EMAIL>',  # À remplacer
    password='votre-mot-de-passe-app',  # À remplacer par un mot de passe d'application
    use_tls=True,
    from_name='Nestria',
    from_email='<EMAIL>'
)
