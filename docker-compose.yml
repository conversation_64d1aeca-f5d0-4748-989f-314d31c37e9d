version: '3.8'

services:
  # PostgreSQL Database
  db:
    image: postgres:15
    restart: always
    environment:
      POSTGRES_DB: airbnb_clone
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: your_db_password  # Change this!
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - airbnb_network

  # Redis Cache
  redis:
    image: redis:7-alpine
    restart: always
    command: redis-server --appendonly yes
    volumes:
      - redis_data:/data
    networks:
      - airbnb_network

  # Django Application
  web:
    build: .
    restart: always
    environment:
      - DJANGO_SETTINGS_MODULE=airbnb_clone.settings_production
      - SECRET_KEY=your_secret_key_here  # Change this!
      - DB_NAME=airbnb_clone
      - DB_USER=postgres
      - DB_PASSWORD=your_db_password  # Change this!
      - DB_HOST=db
      - DB_PORT=5432
      - REDIS_URL=redis://redis:6379/1
    volumes:
      - static_volume:/app/staticfiles
      - media_volume:/app/media
    ports:
      - "8000:8000"
    depends_on:
      - db
      - redis
    networks:
      - airbnb_network
    command: >
      sh -c "python manage.py migrate &&
             python manage.py collectstatic --noinput &&
             python manage.py populate_data &&
             python manage.py populate_destinations &&
             gunicorn --bind 0.0.0.0:8000 --workers 3 wsgi_production:application"

  # Nginx Reverse Proxy
  nginx:
    image: nginx:alpine
    restart: always
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/conf.d/default.conf
      - static_volume:/app/staticfiles
      - media_volume:/app/media
      - ./ssl:/etc/nginx/ssl  # Mount SSL certificates here
    depends_on:
      - web
    networks:
      - airbnb_network

volumes:
  postgres_data:
  redis_data:
  static_volume:
  media_volume:

networks:
  airbnb_network:
    driver: bridge
