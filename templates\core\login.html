{% extends 'base.html' %}
{% load static %}

{% block title %}Login - Nestria{% endblock %}

{% block content %}
<div class="min-h-screen flex items-center justify-center bg-gradient-to-br from-primary-50 to-secondary-50 dark:from-dark-900 dark:to-dark-800 py-12 px-4 sm:px-6 lg:px-8 transition-colors duration-300">
    <div class="max-w-md w-full space-y-8">
        <div class="text-center">
            <!-- Logo Nestria -->
            <div class="mx-auto mb-6">
                <img src="{% static 'images/nestria-logo-transparent.png' %}"
                     alt="Nestria Logo"
                     class="w-20 h-20 object-contain mx-auto nestria-logo-img">
            </div>

            <h2 class="text-4xl font-display font-bold text-gray-900 dark:text-gray-100 mb-2">
                Welcome back to
                <span class="block text-gray-900 dark:text-white">
                    NESTRIA
                </span>
            </h2>
            <p class="text-gray-600 dark:text-gray-400 mb-8">
                Welcome back to your adventure
            </p>
        </div>
        
        <form class="mt-8 space-y-6" method="post">
            {% csrf_token %}
            <div class="rounded-md shadow-sm -space-y-px">
                <div>
                    <label for="id_username" class="sr-only">Username</label>
                    <input 
                        id="id_username" 
                        name="username" 
                        type="text" 
                        required 
                        class="appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-t-md focus:outline-none focus:ring-airbnb-red focus:border-airbnb-red focus:z-10 sm:text-sm" 
                        placeholder="Username"
                    >
                </div>
                <div>
                    <label for="id_password" class="sr-only">Password</label>
                    <input 
                        id="id_password" 
                        name="password" 
                        type="password" 
                        required 
                        class="appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-b-md focus:outline-none focus:ring-airbnb-red focus:border-airbnb-red focus:z-10 sm:text-sm" 
                        placeholder="Password"
                    >
                </div>
            </div>

            {% if form.errors %}
                <div class="rounded-md bg-red-50 p-4">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <i class="fas fa-exclamation-circle text-red-400"></i>
                        </div>
                        <div class="ml-3">
                            <h3 class="text-sm font-medium text-red-800">
                                Please correct the following errors:
                            </h3>
                            <div class="mt-2 text-sm text-red-700">
                                <ul class="list-disc pl-5 space-y-1">
                                    {% for field, errors in form.errors.items %}
                                        {% for error in errors %}
                                            <li>{{ error }}</li>
                                        {% endfor %}
                                    {% endfor %}
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            {% endif %}

            <div>
                <button 
                    type="submit" 
                    class="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-airbnb-red hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-airbnb-red btn-primary"
                >
                    Sign in
                </button>
            </div>
        </form>
    </div>
</div>
{% endblock %}
