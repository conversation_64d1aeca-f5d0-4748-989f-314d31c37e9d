from django.core.management.base import BaseCommand
from django.core.files.base import ContentFile
from rooms.models import Room, RoomImage
import requests
import random


class Command(BaseCommand):
    help = 'Add professional photos to existing properties'

    def handle(self, *args, **options):
        self.stdout.write('Adding professional photos to properties...')
        
        # Professional property photos by type
        property_photos = {
            'luxury': [
                'https://images.unsplash.com/photo-1600596542815-ffad4c1539a9?w=800&h=600&fit=crop',  # Luxury living room
                'https://images.unsplash.com/photo-1600607687939-ce8a6c25118c?w=800&h=600&fit=crop',  # Modern kitchen
                'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=800&h=600&fit=crop',  # Luxury bedroom
                'https://images.unsplash.com/photo-1600566753190-17f0baa2a6c3?w=800&h=600&fit=crop',  # Bathroom
                'https://images.unsplash.com/photo-1600607687644-aac4c3eac7f4?w=800&h=600&fit=crop',  # Balcony view
            ],
            'apartment': [
                'https://images.unsplash.com/photo-1522708323590-d24dbb6b0267?w=800&h=600&fit=crop',  # Modern apartment
                'https://images.unsplash.com/photo-1560448204-e02f11c3d0e2?w=800&h=600&fit=crop',  # Living space
                'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=800&h=600&fit=crop',  # Bedroom
                'https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=800&h=600&fit=crop',  # Kitchen
                'https://images.unsplash.com/photo-1571624436279-b272aff752b5?w=800&h=600&fit=crop',  # Dining area
            ],
            'house': [
                'https://images.unsplash.com/photo-1600585154340-be6161a56a0c?w=800&h=600&fit=crop',  # House exterior
                'https://images.unsplash.com/photo-1600566753086-00f18fb6b3ea?w=800&h=600&fit=crop',  # Living room
                'https://images.unsplash.com/photo-1600607687920-4e2a09cf159d?w=800&h=600&fit=crop',  # Kitchen
                'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=800&h=600&fit=crop',  # Master bedroom
                'https://images.unsplash.com/photo-1600566752355-35792bedcfea?w=800&h=600&fit=crop',  # Garden
            ],
            'villa': [
                'https://images.unsplash.com/photo-1600585154526-990dced4db0d?w=800&h=600&fit=crop',  # Villa exterior
                'https://images.unsplash.com/photo-1600566753190-17f0baa2a6c3?w=800&h=600&fit=crop',  # Pool area
                'https://images.unsplash.com/photo-1600596542815-ffad4c1539a9?w=800&h=600&fit=crop',  # Luxury interior
                'https://images.unsplash.com/photo-1600607687939-ce8a6c25118c?w=800&h=600&fit=crop',  # Gourmet kitchen
                'https://images.unsplash.com/photo-1600566752355-35792bedcfea?w=800&h=600&fit=crop',  # Terrace
            ],
            'cabin': [
                'https://images.unsplash.com/photo-1449824913935-59a10b8d2000?w=800&h=600&fit=crop',  # Mountain cabin
                'https://images.unsplash.com/photo-1441974231531-c6227db76b6e?w=800&h=600&fit=crop',  # Forest view
                'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=800&h=600&fit=crop',  # Cozy interior
                'https://images.unsplash.com/photo-1571896349842-33c89424de2d?w=800&h=600&fit=crop',  # Fireplace
                'https://images.unsplash.com/photo-1464822759844-d150baec0494?w=800&h=600&fit=crop',  # Mountain landscape
            ],
            'loft': [
                'https://images.unsplash.com/photo-1560448204-e02f11c3d0e2?w=800&h=600&fit=crop',  # Industrial loft
                'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=800&h=600&fit=crop',  # Loft bedroom
                'https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=800&h=600&fit=crop',  # Open kitchen
                'https://images.unsplash.com/photo-1571624436279-b272aff752b5?w=800&h=600&fit=crop',  # Dining space
                'https://images.unsplash.com/photo-1600566753086-00f18fb6b3ea?w=800&h=600&fit=crop',  # Living area
            ]
        }
        
        # Photo captions
        captions = [
            'Spacious living area with modern furnishings',
            'Fully equipped gourmet kitchen',
            'Comfortable bedroom with premium bedding',
            'Luxurious bathroom with modern amenities',
            'Beautiful outdoor space with stunning views',
            'Elegant dining area perfect for entertaining',
            'Cozy reading nook with natural light',
            'Private balcony with city/nature views'
        ]
        
        # Get all rooms without images
        rooms = Room.objects.all()
        
        for room in rooms:
            # Skip if room already has images
            if room.images.exists():
                self.stdout.write(f'Room "{room.title}" already has images, skipping...')
                continue
            
            self.stdout.write(f'Processing room: {room.title}')
            
            # Determine photo set based on room title/type
            photo_set = 'apartment'  # default
            title_lower = room.title.lower()
            
            if 'luxury' in title_lower or 'penthouse' in title_lower:
                photo_set = 'luxury'
            elif 'house' in title_lower or 'brownstone' in title_lower:
                photo_set = 'house'
            elif 'villa' in title_lower or 'beachfront' in title_lower:
                photo_set = 'villa'
            elif 'cabin' in title_lower or 'mountain' in title_lower:
                photo_set = 'cabin'
            elif 'loft' in title_lower:
                photo_set = 'loft'
            
            photos = property_photos.get(photo_set, property_photos['apartment'])
            
            # Add 3-5 photos per room
            num_photos = random.randint(3, 5)
            selected_photos = random.sample(photos, min(num_photos, len(photos)))
            
            for i, photo_url in enumerate(selected_photos):
                try:
                    response = requests.get(photo_url, timeout=30)
                    if response.status_code == 200:
                        # Create filename
                        filename = f"room_{room.id}_{i+1}.jpg"
                        
                        # Create RoomImage
                        room_image = RoomImage(
                            room=room,
                            caption=random.choice(captions),
                            order=i+1
                        )
                        
                        # Save image
                        room_image.image.save(
                            filename,
                            ContentFile(response.content),
                            save=True
                        )
                        
                        # Set as main image if it's the first one
                        if i == 0 and not room.main_image:
                            room.main_image.save(
                                f"room_{room.id}_main.jpg",
                                ContentFile(response.content),
                                save=True
                            )
                        
                        self.stdout.write(f'  ✅ Added photo {i+1}: {room_image.caption}')
                    else:
                        self.stdout.write(f'  ❌ Failed to download photo {i+1}')
                
                except Exception as e:
                    self.stdout.write(f'  ❌ Error processing photo {i+1}: {str(e)}')
        
        self.stdout.write(self.style.SUCCESS('\n🎉 Property photos added successfully!'))
        
        # Update room counts
        total_rooms = Room.objects.count()
        rooms_with_images = Room.objects.filter(images__isnull=False).distinct().count()
        
        self.stdout.write(f'📊 Statistics:')
        self.stdout.write(f'  Total rooms: {total_rooms}')
        self.stdout.write(f'  Rooms with images: {rooms_with_images}')
        self.stdout.write(f'  Total images: {RoomImage.objects.count()}')
        
        self.stdout.write('\nYou can now view the properties with photos at /')
