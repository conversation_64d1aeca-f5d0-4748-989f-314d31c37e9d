"""
Service WhatsApp utilisant l'API Meta/Facebook directe
Alternative gratuite à Twilio
"""

import requests
import logging
from django.conf import settings

logger = logging.getLogger(__name__)


class MetaWhatsAppService:
    """Service WhatsApp utilisant l'API Meta/Facebook"""
    
    def __init__(self):
        self.access_token = getattr(settings, 'META_WHATSAPP_ACCESS_TOKEN', None)
        self.phone_number_id = getattr(settings, 'META_WHATSAPP_PHONE_NUMBER_ID', None)
        self.base_url = "https://graph.facebook.com/v18.0"
        
    def send_verification_code(self, phone_number, verification_code):
        """
        Envoie un code de vérification via WhatsApp Business API Meta
        
        Args:
            phone_number (str): Numéro de téléphone (+212603999557)
            verification_code (str): Code à 6 chiffres
            
        Returns:
            dict: Résultat de l'envoi
        """
        
        if not self.access_token or not self.phone_number_id:
            # Mode simulation
            logger.info(f"🔐 WhatsApp Meta verification code for {phone_number}: {verification_code}")
            logger.info(f"📱 Message sent: 'Your Nestria verification code is: {verification_code}. This code will expire in 5 minutes.'")
            return {
                'success': True,
                'message_id': 'simulated_meta_message_id',
                'status': 'simulated'
            }
        
        # Nettoyer le numéro
        clean_phone = self._clean_phone_number(phone_number)
        
        # Préparer le message
        message_data = {
            "messaging_product": "whatsapp",
            "to": clean_phone,
            "type": "text",
            "text": {
                "body": f"🏠 *Nestria Verification*\n\nYour verification code is: *{verification_code}*\n\nThis code will expire in 5 minutes.\nDo not share this code with anyone.\n\nWelcome to Nestria! 🌟"
            }
        }
        
        headers = {
            "Authorization": f"Bearer {self.access_token}",
            "Content-Type": "application/json"
        }
        
        try:
            url = f"{self.base_url}/{self.phone_number_id}/messages"
            response = requests.post(url, json=message_data, headers=headers)
            
            if response.status_code == 200:
                result = response.json()
                logger.info(f"✅ WhatsApp Meta message sent successfully to {clean_phone}")
                logger.info(f"📱 Message ID: {result.get('messages', [{}])[0].get('id')}")
                
                return {
                    'success': True,
                    'message_id': result.get('messages', [{}])[0].get('id'),
                    'status': 'sent'
                }
            else:
                error_data = response.json()
                logger.error(f"❌ WhatsApp Meta error for {clean_phone}: {error_data}")
                return {
                    'success': False,
                    'error': error_data.get('error', {}).get('message', 'Unknown error'),
                    'error_code': error_data.get('error', {}).get('code')
                }
                
        except Exception as e:
            logger.error(f"❌ Unexpected error sending WhatsApp Meta to {clean_phone}: {str(e)}")
            return {
                'success': False,
                'error': 'Unexpected error occurred'
            }
    
    def _clean_phone_number(self, phone_number):
        """Nettoie le numéro de téléphone"""
        if not phone_number:
            return None
            
        clean = phone_number.strip().replace(' ', '').replace('-', '').replace('(', '').replace(')', '')
        
        if not clean.startswith('+'):
            clean = '+' + clean
            
        return clean


# Instance globale
meta_whatsapp_service = MetaWhatsAppService()


def send_whatsapp_verification_meta(phone_number, verification_code):
    """
    Fonction helper pour envoyer via Meta WhatsApp API
    """
    result = meta_whatsapp_service.send_verification_code(phone_number, verification_code)
    return result.get('success', False)
