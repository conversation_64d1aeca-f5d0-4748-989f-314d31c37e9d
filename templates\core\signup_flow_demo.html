<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Airbnb Signup Flow Demo</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'airbnb-pink': '#E91E63',
                    }
                }
            }
        }
    </script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- Header -->
    <header class="bg-white shadow-sm border-b">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <div class="flex items-center">
                    <svg width="32" height="32" viewBox="0 0 32 32" class="mr-2 text-airbnb-pink">
                        <path fill="currentColor" d="M16 0C7.2 0 0 7.2 0 16s7.2 16 16 16 16-7.2 16-16S24.8 0 16 0zm0 29C8.8 29 3 23.2 3 16S8.8 3 16 3s13 5.8 13 13-5.8 13-13 13z"/>
                        <path fill="currentColor" d="M16 8c-4.4 0-8 3.6-8 8s3.6 8 8 8 8-3.6 8-8-3.6-8-8-8zm0 13c-2.8 0-5-2.2-5-5s2.2-5 5-5 5 2.2 5 5-2.2 5-5 5z"/>
                    </svg>
                    <span class="text-2xl font-bold text-airbnb-pink">airbnb</span>
                </div>
                <div class="text-sm text-gray-600">
                    Signup Flow Demo
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <div class="max-w-6xl mx-auto px-4 py-12">
        <div class="text-center mb-12">
            <h1 class="text-4xl font-bold text-gray-900 mb-4">Airbnb Signup Flow</h1>
            <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                Experience the complete signup journey with social authentication, 
                contact information, profile setup, and community commitment.
            </p>
        </div>

        <!-- Flow Steps -->
        <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
            <!-- Step 1: Social Login -->
            <div class="bg-white rounded-lg p-6 shadow-sm border hover:shadow-md transition-shadow">
                <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-4">
                    <i class="fas fa-users text-blue-600 text-xl"></i>
                </div>
                <h3 class="text-lg font-semibold text-gray-900 mb-2">1. Social Authentication</h3>
                <p class="text-gray-600 mb-4">Choose from Google, Facebook, Apple, or Email signup options.</p>
                <a href="/signup/" class="inline-block bg-airbnb-pink text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-pink-600 transition-colors">
                    Try Signup
                </a>
            </div>

            <!-- Step 2: Contact Info -->
            <div class="bg-white rounded-lg p-6 shadow-sm border hover:shadow-md transition-shadow">
                <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mb-4">
                    <i class="fas fa-envelope text-green-600 text-xl"></i>
                </div>
                <h3 class="text-lg font-semibold text-gray-900 mb-2">2. Contact Information</h3>
                <p class="text-gray-600 mb-4">Provide email and agree to terms and conditions.</p>
                <a href="/contact-info/" class="inline-block bg-airbnb-pink text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-pink-600 transition-colors">
                    View Page
                </a>
            </div>

            <!-- Step 3: Profile Setup -->
            <div class="bg-white rounded-lg p-6 shadow-sm border hover:shadow-md transition-shadow">
                <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mb-4">
                    <i class="fas fa-user-edit text-purple-600 text-xl"></i>
                </div>
                <h3 class="text-lg font-semibold text-gray-900 mb-2">3. Profile Setup</h3>
                <p class="text-gray-600 mb-4">Complete profile with name, birthdate, and contact details.</p>
                <a href="/finish-signup/" class="inline-block bg-airbnb-pink text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-pink-600 transition-colors">
                    View Page
                </a>
            </div>

            <!-- Step 4: Community Commitment -->
            <div class="bg-white rounded-lg p-6 shadow-sm border hover:shadow-md transition-shadow">
                <div class="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center mb-4">
                    <i class="fas fa-heart text-red-600 text-xl"></i>
                </div>
                <h3 class="text-lg font-semibold text-gray-900 mb-2">4. Community Values</h3>
                <p class="text-gray-600 mb-4">Commit to Airbnb's community values and inclusion principles.</p>
                <a href="/community-commitment/" class="inline-block bg-airbnb-pink text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-pink-600 transition-colors">
                    View Page
                </a>
            </div>

            <!-- Step 5: Onboarding -->
            <div class="bg-white rounded-lg p-6 shadow-sm border hover:shadow-md transition-shadow">
                <div class="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center mb-4">
                    <i class="fas fa-graduation-cap text-yellow-600 text-xl"></i>
                </div>
                <h3 class="text-lg font-semibold text-gray-900 mb-2">5. Onboarding</h3>
                <p class="text-gray-600 mb-4">Learn how to use Airbnb with interactive tutorials.</p>
                <a href="/onboarding/" class="inline-block bg-airbnb-pink text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-pink-600 transition-colors">
                    Start Tour
                </a>
            </div>

            <!-- Step 6: Home -->
            <div class="bg-white rounded-lg p-6 shadow-sm border hover:shadow-md transition-shadow">
                <div class="w-12 h-12 bg-teal-100 rounded-lg flex items-center justify-center mb-4">
                    <i class="fas fa-home text-teal-600 text-xl"></i>
                </div>
                <h3 class="text-lg font-semibold text-gray-900 mb-2">6. Explore Airbnb</h3>
                <p class="text-gray-600 mb-4">Start browsing and booking amazing places to stay.</p>
                <a href="/" class="inline-block bg-airbnb-pink text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-pink-600 transition-colors">
                    Go Home
                </a>
            </div>
        </div>

        <!-- Features -->
        <div class="bg-white rounded-lg p-8 shadow-sm border">
            <h2 class="text-2xl font-semibold text-gray-900 mb-6 text-center">Key Features Implemented</h2>
            <div class="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
                <div class="text-center">
                    <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-3">
                        <i class="fas fa-shield-alt text-blue-600 text-2xl"></i>
                    </div>
                    <h4 class="font-semibold text-gray-900 mb-2">Social Auth</h4>
                    <p class="text-sm text-gray-600">Google, Facebook, Apple & Email login options</p>
                </div>

                <div class="text-center">
                    <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-3">
                        <i class="fas fa-mobile-alt text-green-600 text-2xl"></i>
                    </div>
                    <h4 class="font-semibold text-gray-900 mb-2">Responsive</h4>
                    <p class="text-sm text-gray-600">Mobile-first design that works on all devices</p>
                </div>

                <div class="text-center">
                    <div class="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-3">
                        <i class="fas fa-check-circle text-purple-600 text-2xl"></i>
                    </div>
                    <h4 class="font-semibold text-gray-900 mb-2">Validation</h4>
                    <p class="text-sm text-gray-600">Real-time form validation and error handling</p>
                </div>

                <div class="text-center">
                    <div class="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-3">
                        <i class="fas fa-heart text-red-600 text-2xl"></i>
                    </div>
                    <h4 class="font-semibold text-gray-900 mb-2">Modern UI</h4>
                    <p class="text-sm text-gray-600">Clean, modern design following Airbnb's style</p>
                </div>
            </div>
        </div>

        <!-- Quick Start -->
        <div class="text-center mt-12">
            <h2 class="text-2xl font-semibold text-gray-900 mb-4">Ready to Experience the Flow?</h2>
            <p class="text-gray-600 mb-6">Start the complete signup journey from the beginning</p>
            <a href="/signup/" class="inline-block bg-airbnb-pink text-white px-8 py-3 rounded-lg font-semibold hover:bg-pink-600 transition-colors text-lg">
                Start Signup Flow
            </a>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-gray-100 border-t mt-16">
        <div class="max-w-6xl mx-auto px-4 py-8">
            <div class="text-center text-gray-600">
                <p>&copy; 2025 Airbnb Clone. All rights reserved.</p>
                <p class="mt-2 text-sm">This is a demonstration of the signup flow implementation.</p>
            </div>
        </div>
    </footer>
</body>
</html>
