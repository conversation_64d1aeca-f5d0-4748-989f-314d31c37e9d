<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Log in - Nestria</title>
    {% load static %}
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'nestria-red': '#FF5A5F',
                    }
                }
            }
        }
    </script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="{% static 'css/social-auth.css' %}">
</head>
<body class="bg-gray-50 min-h-screen flex items-center justify-center p-4">
    <div class="bg-white rounded-2xl shadow-xl w-full max-w-md p-8 relative">
        <!-- Close Button -->
        <button onclick="window.history.back()" class="absolute top-4 left-4 text-gray-400 hover:text-gray-600 text-xl">
            <i class="fas fa-times"></i>
        </button>

        <!-- Header -->
        <div class="text-center mb-8">
            <h1 class="text-2xl font-semibold text-gray-900 mb-2">Log in</h1>
        </div>

        <!-- Logo -->
        <div class="flex justify-center mb-8">
            <img src="{% static 'images/nestria-logo-official.svg' %}"
                 alt="Nestria Logo"
                 class="w-32 h-32 object-contain">
        </div>

        <div class="text-center mb-6">
            <h2 class="text-xl font-medium text-gray-800">Welcome to Nestria</h2>
        </div>

        <!-- Phone Number Form -->
        <form action="{% url 'core:phone_signup' %}" method="post" class="space-y-4 mb-6">
            {% csrf_token %}
            
            <!-- Country Code -->
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Country code</label>
                <select class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-nestria-red focus:border-transparent">
                    <option value="+212">Morocco (+212)</option>
                    <option value="+33">France (+33)</option>
                    <option value="+1">United States (+1)</option>
                    <option value="+44">United Kingdom (+44)</option>
                </select>
            </div>

            <!-- Phone Number -->
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Phone number</label>
                <input type="tel"
                       name="phone"
                       placeholder="+212 603999557"
                       required
                       class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-nestria-red focus:border-transparent">
            </div>

            <p class="text-xs text-gray-500">
                We'll call or text you to confirm your number. Standard message and data rates apply.
                <a href="#" class="underline">Privacy Policy</a>
            </p>

            <!-- Continue Button -->
            <button type="submit" class="w-full bg-gradient-to-r from-nestria-red to-red-600 text-white py-3 rounded-lg font-semibold hover:from-red-600 hover:to-red-700 transition-all">
                Continue
            </button>
        </form>

        <!-- Divider -->
        <div class="relative my-6">
            <div class="absolute inset-0 flex items-center">
                <div class="w-full border-t border-gray-300"></div>
            </div>
            <div class="relative flex justify-center text-sm">
                <span class="px-2 bg-white text-gray-500">or</span>
            </div>
        </div>

        <!-- Social Login Buttons -->
        <div class="space-y-3">
            <!-- Google -->
            <button onclick="loginWithGoogle()" class="w-full flex items-center justify-center px-4 py-3 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors">
                <img src="https://developers.google.com/identity/images/g-logo.png" alt="Google" class="w-5 h-5 mr-3">
                <span class="font-medium text-gray-700">Continue with Google</span>
            </button>

            <!-- Apple -->
            <button onclick="loginWithApple()" class="w-full flex items-center justify-center px-4 py-3 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors">
                <i class="fab fa-apple text-xl mr-3"></i>
                <span class="font-medium text-gray-700">Continue with Apple</span>
            </button>

            <!-- Email -->
            <button onclick="loginWithEmail()" class="w-full flex items-center justify-center px-4 py-3 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors">
                <i class="fas fa-envelope text-gray-600 mr-3"></i>
                <span class="font-medium text-gray-700">Continue with email</span>
            </button>

            <!-- Facebook -->
            <button onclick="loginWithFacebook()" class="w-full flex items-center justify-center px-4 py-3 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors">
                <i class="fab fa-facebook-f text-blue-600 text-xl mr-3"></i>
                <span class="font-medium text-gray-700">Continue with Facebook</span>
            </button>
        </div>
    </div>

    <script>
        function loginWithGoogle() {
            // Redirection vers l'authentification Google
            window.location.href = '{% url "core:google_auth" %}';
        }

        function loginWithApple() {
            // Redirection vers l'authentification Apple
            window.location.href = '{% url "core:apple_auth" %}';
        }

        function loginWithEmail() {
            // Redirection vers formulaire email
            window.location.href = '{% url "core:email_login" %}';
        }

        function loginWithFacebook() {
            // Redirection vers l'authentification Facebook
            window.location.href = '{% url "core:facebook_auth" %}';
        }
    </script>
    <script src="{% static 'js/social-auth.js' %}"></script>
</body>
</html>
