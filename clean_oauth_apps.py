#!/usr/bin/env python
"""
Script pour nettoyer les applications OAuth en double
"""
import os
import django

# Configuration Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'airbnb_clone.settings')
django.setup()

from allauth.socialaccount.models import SocialApp
from django.contrib.sites.models import Site

def clean_oauth_apps():
    """Nettoie les applications OAuth en double"""
    
    print("🧹 Nettoyage des applications OAuth...")
    
    # Supprimer toutes les applications OAuth existantes
    deleted_count = SocialApp.objects.all().delete()[0]
    print(f"🗑️  {deleted_count} applications supprimées")
    
    # Récupérer le site
    site = Site.objects.get(pk=1)
    
    # Recréer les applications OAuth proprement
    providers = [
        {
            'provider': 'google',
            'name': 'Google OAuth',
            'client_id': '************-demo_google_client_id.apps.googleusercontent.com',
            'secret': 'GOCSPX-demo_google_secret_key'
        },
        {
            'provider': 'facebook',
            'name': 'Facebook OAuth',
            'client_id': 'demo_facebook_app_id',
            'secret': 'demo_facebook_app_secret'
        },
        {
            'provider': 'apple',
            'name': 'Apple Sign In',
            'client_id': 'com.nestria.web',
            'secret': 'demo_apple_secret'
        }
    ]
    
    for provider_config in providers:
        app = SocialApp.objects.create(**provider_config)
        app.sites.add(site)
        print(f"✅ {provider_config['provider'].title()} OAuth créé")
    
    print("\n🎯 Applications OAuth nettoyées et recréées !")
    print("Maintenant vous pouvez configurer les vraies clés dans l'admin Django.")

if __name__ == '__main__':
    clean_oauth_apps()
