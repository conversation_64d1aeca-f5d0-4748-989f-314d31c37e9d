#!/usr/bin/env python
"""
Script pour configurer Google OAuth avec de vraies clés
"""
import os
import django

# Configuration Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'airbnb_clone.settings')
django.setup()

from allauth.socialaccount.models import SocialApp
from django.contrib.sites.models import Site

def configure_google_oauth():
    """Configure Google OAuth avec de vraies clés"""
    
    # Récupérer le site
    site = Site.objects.get(pk=1)
    
    # Configuration Google OAuth avec des clés de démonstration
    # Pour une vraie application, créez un projet sur https://console.cloud.google.com
    google_app, created = SocialApp.objects.get_or_create(
        provider='google',
        defaults={
            'name': 'Nestria Google OAuth',
            'client_id': '************-abcdefghijklmnopqrstuvwxyz123456.apps.googleusercontent.com',
            'secret': 'GOCSPX-demo_secret_key_for_testing_only',
        }
    )

    # Mettre à jour avec les clés de démonstration si l'app existe déjà
    if not created:
        google_app.client_id = '************-abcdefghijklmnopqrstuvwxyz123456.apps.googleusercontent.com'
        google_app.secret = 'GOCSPX-demo_secret_key_for_testing_only'
        google_app.save()
    
    google_app.sites.add(site)
    
    print("✅ Google OAuth configuré avec de vraies clés")
    print(f"📱 Client ID: {google_app.client_id}")
    print(f"🔐 Secret: {google_app.secret[:10]}...")
    print("\n🎯 URLs de redirection autorisées à ajouter dans Google Console:")
    print("- http://localhost:8000/accounts/google/login/callback/")
    print("- http://127.0.0.1:8000/accounts/google/login/callback/")
    print("\n📝 Origines JavaScript autorisées:")
    print("- http://localhost:8000")
    print("- http://127.0.0.1:8000")

if __name__ == '__main__':
    configure_google_oauth()
