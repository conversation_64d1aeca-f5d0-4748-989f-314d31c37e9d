{% extends 'base.html' %}
{% load static %}

{% block title %}Search Results - Airbnb Clone{% endblock %}

{% block content %}
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Search Summary -->
    <div class="mb-8">
        <h1 class="text-2xl font-bold text-gray-900 mb-2">
            {% if search_params.location %}
                Stays in {{ search_params.location }}
            {% else %}
                All stays
            {% endif %}
        </h1>
        <p class="text-gray-600">
            {{ page_obj.paginator.count }} result{{ page_obj.paginator.count|pluralize }} found
            {% if search_params.check_in and search_params.check_out %}
                for {{ search_params.check_in }} - {{ search_params.check_out }}
            {% endif %}
        </p>
    </div>

    <!-- Filters -->
    <div class="mb-8" x-data="searchFilters()">
        <div class="flex flex-wrap gap-4 mb-6">
            <!-- Price Filter -->
            <div class="relative" x-data="{ open: false }">
                <button @click="open = !open" class="filter-chip flex items-center space-x-2">
                    <span>Price</span>
                    <i class="fas fa-chevron-down text-sm"></i>
                </button>
                <div x-show="open" @click.away="open = false" x-transition class="absolute top-full left-0 mt-2 bg-white border border-gray-200 rounded-lg shadow-lg z-10 p-4 w-80">
                    <div class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Price range per night</label>
                            <div class="flex items-center space-x-4">
                                <div>
                                    <input type="number" placeholder="Min" class="w-20 px-3 py-2 border border-gray-300 rounded-md text-sm">
                                </div>
                                <span class="text-gray-500">-</span>
                                <div>
                                    <input type="number" placeholder="Max" class="w-20 px-3 py-2 border border-gray-300 rounded-md text-sm">
                                </div>
                            </div>
                        </div>
                        <button class="w-full bg-airbnb-red text-white py-2 rounded-md text-sm font-medium hover:bg-red-600 transition-colors">
                            Apply
                        </button>
                    </div>
                </div>
            </div>

            <!-- Room Type Filter -->
            <div class="relative" x-data="{ open: false }">
                <button @click="open = !open" class="filter-chip flex items-center space-x-2">
                    <span>Room type</span>
                    <i class="fas fa-chevron-down text-sm"></i>
                </button>
                <div x-show="open" @click.away="open = false" x-transition class="absolute top-full left-0 mt-2 bg-white border border-gray-200 rounded-lg shadow-lg z-10 p-4 w-64">
                    <div class="space-y-3">
                        <label class="flex items-center">
                            <input type="checkbox" class="rounded border-gray-300 text-airbnb-red focus:ring-airbnb-red">
                            <span class="ml-2 text-sm">Entire place</span>
                        </label>
                        <label class="flex items-center">
                            <input type="checkbox" class="rounded border-gray-300 text-airbnb-red focus:ring-airbnb-red">
                            <span class="ml-2 text-sm">Private room</span>
                        </label>
                        <label class="flex items-center">
                            <input type="checkbox" class="rounded border-gray-300 text-airbnb-red focus:ring-airbnb-red">
                            <span class="ml-2 text-sm">Shared room</span>
                        </label>
                    </div>
                </div>
            </div>

            <!-- Amenities Filter -->
            <div class="relative" x-data="{ open: false }">
                <button @click="open = !open" class="filter-chip flex items-center space-x-2">
                    <span>Amenities</span>
                    <i class="fas fa-chevron-down text-sm"></i>
                </button>
                <div x-show="open" @click.away="open = false" x-transition class="absolute top-full left-0 mt-2 bg-white border border-gray-200 rounded-lg shadow-lg z-10 p-4 w-64">
                    <div class="space-y-3">
                        <label class="flex items-center">
                            <input type="checkbox" class="rounded border-gray-300 text-airbnb-red focus:ring-airbnb-red">
                            <span class="ml-2 text-sm">WiFi</span>
                        </label>
                        <label class="flex items-center">
                            <input type="checkbox" class="rounded border-gray-300 text-airbnb-red focus:ring-airbnb-red">
                            <span class="ml-2 text-sm">Kitchen</span>
                        </label>
                        <label class="flex items-center">
                            <input type="checkbox" class="rounded border-gray-300 text-airbnb-red focus:ring-airbnb-red">
                            <span class="ml-2 text-sm">Pool</span>
                        </label>
                        <label class="flex items-center">
                            <input type="checkbox" class="rounded border-gray-300 text-airbnb-red focus:ring-airbnb-red">
                            <span class="ml-2 text-sm">Air conditioning</span>
                        </label>
                        <label class="flex items-center">
                            <input type="checkbox" class="rounded border-gray-300 text-airbnb-red focus:ring-airbnb-red">
                            <span class="ml-2 text-sm">Parking</span>
                        </label>
                    </div>
                </div>
            </div>

            <!-- More Filters -->
            <button class="filter-chip flex items-center space-x-2">
                <i class="fas fa-sliders-h"></i>
                <span>More filters</span>
            </button>
        </div>
    </div>

    <!-- Results Grid -->
    {% if rooms %}
        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 property-grid">
            {% for room in rooms %}
                <div class="property-card bg-white rounded-lg overflow-hidden shadow-sm hover:shadow-lg transition-shadow">
                    <a href="{{ room.get_absolute_url }}" class="block">
                        <div class="relative">
                            {% if room.main_image %}
                                <img src="{{ room.main_image.url }}" alt="{{ room.title }}" class="w-full h-48 object-cover">
                            {% else %}
                                <div class="w-full h-48 bg-gray-200 flex items-center justify-center">
                                    <i class="fas fa-home text-gray-400 text-3xl"></i>
                                </div>
                            {% endif %}
                            
                            <!-- Favorite Button -->
                            <button class="absolute top-3 right-3 favorite-btn heart-icon" data-property-id="{{ room.id }}">
                                <i class="far fa-heart text-white text-xl"></i>
                            </button>
                        </div>
                        
                        <div class="p-4">
                            <div class="flex items-center justify-between mb-2">
                                <h3 class="font-semibold text-gray-900 truncate">{{ room.title }}</h3>
                                {% if room.average_rating > 0 %}
                                    <div class="flex items-center">
                                        <i class="fas fa-star text-airbnb-red text-sm"></i>
                                        <span class="text-sm text-gray-600 ml-1">{{ room.average_rating|floatformat:1 }}</span>
                                    </div>
                                {% endif %}
                            </div>
                            
                            <p class="text-gray-600 text-sm mb-2">{{ room.city }}, {{ room.country }}</p>
                            <p class="text-gray-500 text-sm mb-3">{{ room.max_guests }} guest{{ room.max_guests|pluralize }} · {{ room.bedrooms }} bedroom{{ room.bedrooms|pluralize }}</p>
                            
                            <div class="flex items-center justify-between">
                                <div>
                                    <span class="font-semibold text-gray-900 price-display">${{ room.price_per_night|floatformat:0 }}</span>
                                    <span class="text-gray-600 text-sm">/ night</span>
                                </div>
                                {% if room.instant_book %}
                                    <span class="text-xs bg-airbnb-red text-white px-2 py-1 rounded">Instant Book</span>
                                {% endif %}
                            </div>
                        </div>
                    </a>
                </div>
            {% endfor %}
        </div>

        <!-- Pagination -->
        {% if is_paginated %}
            <div class="mt-12 flex justify-center">
                <nav class="flex items-center space-x-2">
                    {% if page_obj.has_previous %}
                        <a href="?{% for key, value in search_params.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}page={{ page_obj.previous_page_number }}" 
                           class="px-3 py-2 text-sm font-medium text-gray-500 hover:text-gray-700 border border-gray-300 rounded-md hover:bg-gray-50">
                            Previous
                        </a>
                    {% endif %}
                    
                    {% for num in page_obj.paginator.page_range %}
                        {% if page_obj.number == num %}
                            <span class="px-3 py-2 text-sm font-medium text-white bg-airbnb-red border border-airbnb-red rounded-md">
                                {{ num }}
                            </span>
                        {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                            <a href="?{% for key, value in search_params.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}page={{ num }}" 
                               class="px-3 py-2 text-sm font-medium text-gray-500 hover:text-gray-700 border border-gray-300 rounded-md hover:bg-gray-50">
                                {{ num }}
                            </a>
                        {% endif %}
                    {% endfor %}
                    
                    {% if page_obj.has_next %}
                        <a href="?{% for key, value in search_params.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}page={{ page_obj.next_page_number }}" 
                           class="px-3 py-2 text-sm font-medium text-gray-500 hover:text-gray-700 border border-gray-300 rounded-md hover:bg-gray-50">
                            Next
                        </a>
                    {% endif %}
                </nav>
            </div>
        {% endif %}
    {% else %}
        <!-- No Results -->
        <div class="text-center py-12">
            <i class="fas fa-search text-gray-300 text-6xl mb-4"></i>
            <h3 class="text-xl font-semibold text-gray-600 mb-2">No results found</h3>
            <p class="text-gray-500 mb-6">Try adjusting your search criteria or browse all properties.</p>
            <a href="{% url 'core:home' %}" class="inline-block bg-airbnb-red text-white px-6 py-3 rounded-lg font-medium hover:bg-red-600 transition-colors btn-primary">
                Browse all properties
            </a>
        </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_js %}
<script src="{% static 'js/favorites.js' %}"></script>
<script>
function searchFilters() {
    return {
        priceMin: '',
        priceMax: '',
        selectedRoomTypes: [],
        selectedAmenities: [],

        applyFilters() {
            // Build query string with current filters
            const params = new URLSearchParams(window.location.search);

            if (this.priceMin) params.set('price_min', this.priceMin);
            if (this.priceMax) params.set('price_max', this.priceMax);

            // Update URL and reload
            window.location.search = params.toString();
        }
    }
}

// Initialize favorites manager
document.addEventListener('DOMContentLoaded', () => {
    new FavoritesManager();
});
</script>
{% endblock %}
