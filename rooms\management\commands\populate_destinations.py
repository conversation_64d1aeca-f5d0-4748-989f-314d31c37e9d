from django.core.management.base import BaseCommand
from django.utils.text import slugify
from rooms.models import Destination, DestinationImage
from decimal import Decimal
import os


class Command(BaseCommand):
    help = 'Populate the database with destination data'

    def handle(self, *args, **options):
        self.stdout.write('Creating destination data...')
        
        destinations_data = [
            {
                'name': 'Paris',
                'country': 'France',
                'description': 'The City of Light, known for its art, fashion, gastronomy, and culture. Paris is home to iconic landmarks like the Eiffel Tower, Louvre Museum, and Notre-Dame Cathedral.',
                'historical_info': '''Paris has been a major European city for over two millennia. Originally a Celtic settlement called Lutetia, it was conquered by the Romans in 52 BC. The city became the capital of France in 987 AD under <PERSON>.

During the Middle Ages, Paris grew rapidly and became one of Europe's largest cities. The Gothic architecture of Notre-Dame Cathedral (begun in 1163) exemplifies this period. The Renaissance brought Italian influences, while the 17th and 18th centuries saw the construction of grand palaces like Versailles.

The French Revolution (1789-1799) transformed Paris into the center of revolutionary fervor. The 19th century brought <PERSON>'s urban renovation, creating the wide boulevards and uniform architecture that characterize modern Paris.

The Belle Époque (1871-1914) saw Paris become the cultural capital of the world, attracting artists like <PERSON>, <PERSON><PERSON>, and Reno<PERSON>. The Eiffel Tower, built for the 1889 World's Fair, became the city's most recognizable symbol.

Today, Paris remains a global center for art, fashion, gastronomy, and culture, welcoming over 30 million visitors annually.''',
                'latitude': Decimal('48.8566'),
                'longitude': Decimal('2.3522'),
                'slug': 'paris-france',
                'meta_description': 'Discover Paris, the City of Light. Explore iconic landmarks, world-class museums, and charming neighborhoods in France\'s romantic capital.',
                'is_featured': True,
            },
            {
                'name': 'Tokyo',
                'country': 'Japan',
                'description': 'A vibrant metropolis blending ultra-modern technology with traditional culture. Experience ancient temples, cutting-edge architecture, and world-renowned cuisine.',
                'historical_info': '''Tokyo, originally named Edo, was founded in 1457 as a small fishing village. It became the seat of the Tokugawa shogunate in 1603, growing into one of the world's largest cities by the 18th century.

The Meiji Restoration of 1868 marked Tokyo's transformation into a modern capital. The city was renamed Tokyo ("Eastern Capital") when Emperor Meiji moved the imperial court from Kyoto.

The Great Kanto Earthquake of 1923 devastated much of Tokyo, but the city was rebuilt with modern infrastructure. World War II brought further destruction, with extensive bombing campaigns reducing much of the city to rubble.

Post-war reconstruction transformed Tokyo into a modern metropolis. The 1964 Olympics showcased Japan's economic miracle, while the 1980s bubble economy created the ultra-modern cityscape we see today.

Tokyo has evolved into a global financial center and cultural hub, seamlessly blending traditional Japanese culture with cutting-edge technology and international influences.''',
                'latitude': Decimal('35.6762'),
                'longitude': Decimal('139.6503'),
                'slug': 'tokyo-japan',
                'meta_description': 'Experience Tokyo\'s unique blend of tradition and modernity. From ancient temples to futuristic skyscrapers, discover Japan\'s dynamic capital.',
                'is_featured': True,
            },
            {
                'name': 'New York',
                'country': 'United States',
                'description': 'The city that never sleeps, featuring world-famous landmarks, Broadway shows, diverse neighborhoods, and unparalleled energy.',
                'historical_info': '''New York began as New Amsterdam, a Dutch colonial settlement founded in 1624 on Manhattan Island. The British captured the city in 1664, renaming it New York after the Duke of York.

The city played a crucial role in the American Revolution and became the first capital of the United States in 1789. The 19th century brought massive immigration, with Ellis Island serving as the gateway for millions seeking the American Dream.

The construction of the Erie Canal in 1825 established New York as America's premier port. The late 19th and early 20th centuries saw the rise of skyscrapers, with the Empire State Building (1931) becoming an iconic symbol.

The city became a global financial center with Wall Street at its heart. The 20th century brought cultural prominence, with Broadway, jazz clubs, and art galleries making New York a cultural capital.

Today, New York remains America's largest city and a global hub for finance, arts, fashion, and culture, embodying the diversity and ambition that define the American spirit.''',
                'latitude': Decimal('40.7128'),
                'longitude': Decimal('-74.0060'),
                'slug': 'new-york-usa',
                'meta_description': 'Explore New York City, the Big Apple. From Times Square to Central Park, experience the energy and diversity of America\'s greatest city.',
                'is_featured': True,
            },
            {
                'name': 'Rome',
                'country': 'Italy',
                'description': 'The Eternal City, where ancient history meets modern life. Explore the Colosseum, Vatican City, and countless archaeological treasures.',
                'historical_info': '''Rome, according to legend, was founded in 753 BC by Romulus and Remus. Archaeological evidence suggests the area was inhabited much earlier, with settlements dating back to the 8th century BC.

The Roman Republic (509-27 BC) saw Rome expand across the Mediterranean. The Roman Empire (27 BC-476 AD) made Rome the capital of the known world, with a population exceeding one million inhabitants.

After the fall of the Western Roman Empire, Rome became the center of the Catholic Church. The Pope's temporal power shaped the city's development throughout the Middle Ages and Renaissance.

The Renaissance brought artistic masterpieces by Michelangelo, Raphael, and Bernini. St. Peter's Basilica and the Sistine Chapel exemplify this golden age of art and architecture.

Modern Rome, capital of unified Italy since 1871, preserves its ancient heritage while serving as a contemporary European capital. The city's layers of history, from Roman ruins to Baroque churches, create an unparalleled urban archaeological site.''',
                'latitude': Decimal('41.9028'),
                'longitude': Decimal('12.4964'),
                'slug': 'rome-italy',
                'meta_description': 'Discover Rome, the Eternal City. Walk through 2,000 years of history among ancient ruins, Renaissance art, and vibrant Italian culture.',
                'is_featured': True,
            },
            {
                'name': 'Barcelona',
                'country': 'Spain',
                'description': 'A Mediterranean jewel known for Gaudí\'s architecture, vibrant culture, beautiful beaches, and exceptional cuisine.',
                'historical_info': '''Barcelona was founded by the Romans around 15 BC as Barcino. The city became an important trading port in the Mediterranean, with remnants of Roman walls still visible today.

During the Middle Ages, Barcelona became the capital of the County of Barcelona and later the Crown of Aragon. The Gothic Quarter preserves many buildings from this period, including the stunning Cathedral.

The 19th century brought industrialization and the Renaixença, a cultural renaissance that revived Catalan language and culture. This period saw the construction of the Eixample district with its distinctive grid pattern.

The early 20th century marked Barcelona's architectural golden age with Antoni Gaudí's masterpieces: Sagrada Família, Park Güell, and Casa Batlló. These works exemplify Catalan Modernism.

The 1992 Olympics transformed Barcelona into a modern European city, improving infrastructure and opening the city to the sea. Today, Barcelona balances its rich history with contemporary innovation, remaining a major cultural and economic center in the Mediterranean.''',
                'latitude': Decimal('41.3851'),
                'longitude': Decimal('2.1734'),
                'slug': 'barcelona-spain',
                'meta_description': 'Experience Barcelona\'s unique charm. From Gaudí\'s architectural wonders to Mediterranean beaches, discover Catalonia\'s vibrant capital.',
                'is_featured': True,
            },
            {
                'name': 'London',
                'country': 'United Kingdom',
                'description': 'A historic capital blending royal heritage with modern innovation. Home to Big Ben, Buckingham Palace, and world-class museums.',
                'historical_info': '''London was founded by the Romans around 47 AD as Londinium. The city became an important trading center, with the Roman amphitheater and walls forming the foundation of the modern City of London.

After the Norman Conquest in 1066, London grew in importance. The Tower of London, built by William the Conqueror, symbolized royal power. Westminster Abbey became the coronation church of English monarchs.

The Great Fire of 1666 destroyed much of medieval London, leading to Christopher Wren's rebuilding program, including St. Paul's Cathedral. The 18th and 19th centuries saw London become the world's largest city and capital of the British Empire.

The Victorian era brought massive expansion, with the Underground railway system (1863) and iconic landmarks like Tower Bridge (1894). London survived the Blitz during World War II, emerging as a symbol of resilience.

Modern London is a global financial center and cultural hub, hosting the 2012 Olympics and continuing to evolve while preserving its rich heritage. The city remains the political heart of the United Kingdom and a major international metropolis.''',
                'latitude': Decimal('51.5074'),
                'longitude': Decimal('-0.1278'),
                'slug': 'london-uk',
                'meta_description': 'Explore London, a city where history meets modernity. From royal palaces to cutting-edge culture, discover Britain\'s magnificent capital.',
                'is_featured': True,
            },
        ]
        
        for dest_data in destinations_data:
            destination, created = Destination.objects.get_or_create(
                slug=dest_data['slug'],
                defaults=dest_data
            )
            
            if created:
                self.stdout.write(f'Created destination: {destination.name}, {destination.country}')
            else:
                self.stdout.write(f'Destination already exists: {destination.name}, {destination.country}')
        
        self.stdout.write(self.style.SUCCESS('Successfully populated destinations!'))
        self.stdout.write(f'Total destinations: {Destination.objects.count()}')
        self.stdout.write('Note: Add images manually through the admin interface at /admin/')
