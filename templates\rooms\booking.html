{% extends 'base.html' %}
{% load static %}

{% block title %}Book {{ room.title }} - Nestria{% endblock %}

{% block content %}
<div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <div class="text-center">
        <h1 class="text-3xl font-bold text-gray-900 mb-4">Book {{ room.title }}</h1>
        <p class="text-gray-600 mb-8">You're being redirected to our secure checkout...</p>
        
        <div class="bg-white rounded-lg shadow-lg p-8 max-w-md mx-auto">
            <div class="mb-6">
                {% if room.images.first %}
                    <img src="{{ room.images.first.image.url }}" alt="{{ room.title }}" class="w-full h-48 object-cover rounded-lg mb-4">
                {% endif %}
                <h2 class="text-xl font-semibold text-gray-900">{{ room.title }}</h2>
                <p class="text-gray-600">{{ room.city }}, {{ room.country }}</p>
                <p class="text-2xl font-bold text-airbnb-red mt-2">${{ room.price_per_night|floatformat:0 }} / night</p>
            </div>
            
            <a href="{% url 'rooms:room_detail' room.pk %}" class="inline-block bg-airbnb-red text-white px-6 py-3 rounded-lg font-medium hover:bg-red-600 transition-colors btn-primary">
                <i class="fas fa-arrow-left mr-2"></i>
                Go to Property Details
            </a>
        </div>
        
        <p class="text-sm text-gray-500 mt-6">
            Use the booking form on the property details page to make your reservation.
        </p>
    </div>
</div>

<script>
// Auto-redirect to room detail page after 3 seconds
setTimeout(function() {
    window.location.href = "{% url 'rooms:room_detail' room.pk %}";
}, 3000);
</script>
{% endblock %}
