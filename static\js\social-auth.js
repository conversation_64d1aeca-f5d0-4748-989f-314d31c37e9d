/**
 * Social Authentication Handler
 * Gère l'authentification avec Google, Apple, Facebook, etc.
 */

class SocialAuth {
    constructor() {
        this.initializeProviders();
        this.bindEvents();
    }

    initializeProviders() {
        // Configuration des providers
        this.providers = {
            google: {
                clientId: 'YOUR_GOOGLE_CLIENT_ID',
                scope: 'email profile'
            },
            facebook: {
                appId: 'YOUR_FACEBOOK_APP_ID',
                version: 'v18.0'
            },
            apple: {
                clientId: 'YOUR_APPLE_CLIENT_ID',
                scope: 'name email'
            }
        };
    }

    bindEvents() {
        // Bind click events to social buttons
        document.addEventListener('DOMContentLoaded', () => {
            this.bindSocialButtons();
        });
    }

    bindSocialButtons() {
        // Google
        const googleButtons = document.querySelectorAll('[onclick*="loginWithGoogle"], [onclick*="signupWithGoogle"]');
        googleButtons.forEach(button => {
            button.addEventListener('click', (e) => {
                e.preventDefault();
                this.handleGoogleAuth();
            });
        });

        // Apple
        const appleButtons = document.querySelectorAll('[onclick*="loginWithApple"], [onclick*="signupWithApple"]');
        appleButtons.forEach(button => {
            button.addEventListener('click', (e) => {
                e.preventDefault();
                this.handleAppleAuth();
            });
        });

        // Facebook
        const facebookButtons = document.querySelectorAll('[onclick*="loginWithFacebook"], [onclick*="signupWithFacebook"]');
        facebookButtons.forEach(button => {
            button.addEventListener('click', (e) => {
                e.preventDefault();
                this.handleFacebookAuth();
            });
        });

        // Email
        const emailButtons = document.querySelectorAll('[onclick*="loginWithEmail"], [onclick*="signupWithEmail"]');
        emailButtons.forEach(button => {
            button.addEventListener('click', (e) => {
                e.preventDefault();
                const isSignup = button.getAttribute('onclick').includes('signup');
                this.handleEmailAuth(isSignup);
            });
        });
    }

    async handleGoogleAuth() {
        try {
            this.showLoading('google');
            
            // Vérifier si Google Identity Services est chargé
            if (typeof google === 'undefined') {
                await this.loadGoogleScript();
            }

            // Initialiser Google Sign-In
            google.accounts.id.initialize({
                client_id: this.providers.google.clientId,
                callback: this.handleGoogleCallback.bind(this)
            });

            // Afficher le popup de connexion
            google.accounts.id.prompt();

        } catch (error) {
            console.error('Google auth error:', error);
            this.showError('google', 'Erreur lors de la connexion avec Google');
        } finally {
            this.hideLoading('google');
        }
    }

    async handleAppleAuth() {
        try {
            this.showLoading('apple');
            
            // Vérifier si Apple ID SDK est chargé
            if (typeof AppleID === 'undefined') {
                await this.loadAppleScript();
            }

            // Configuration Apple Sign In
            AppleID.auth.init({
                clientId: this.providers.apple.clientId,
                scope: this.providers.apple.scope,
                redirectURI: window.location.origin + '/auth/apple/callback/',
                usePopup: true
            });

            // Déclencher l'authentification
            const data = await AppleID.auth.signIn();
            this.handleAppleCallback(data);

        } catch (error) {
            console.error('Apple auth error:', error);
            this.showError('apple', 'Erreur lors de la connexion avec Apple');
        } finally {
            this.hideLoading('apple');
        }
    }

    async handleFacebookAuth() {
        try {
            this.showLoading('facebook');
            
            // Vérifier si Facebook SDK est chargé
            if (typeof FB === 'undefined') {
                await this.loadFacebookScript();
            }

            // Initialiser Facebook SDK
            FB.init({
                appId: this.providers.facebook.appId,
                cookie: true,
                xfbml: true,
                version: this.providers.facebook.version
            });

            // Déclencher la connexion
            FB.login((response) => {
                if (response.authResponse) {
                    this.handleFacebookCallback(response);
                } else {
                    this.showError('facebook', 'Connexion Facebook annulée');
                }
            }, { scope: 'email,public_profile' });

        } catch (error) {
            console.error('Facebook auth error:', error);
            this.showError('facebook', 'Erreur lors de la connexion avec Facebook');
        } finally {
            this.hideLoading('facebook');
        }
    }

    handleEmailAuth(isSignup) {
        const url = isSignup ? '/email-signup/' : '/email-login/';
        window.location.href = url;
    }

    // Callbacks pour traiter les réponses des providers
    handleGoogleCallback(credentialResponse) {
        // Envoyer le token au backend Django
        this.sendTokenToBackend('google', {
            credential: credentialResponse.credential
        });
    }

    handleAppleCallback(data) {
        // Envoyer les données Apple au backend
        this.sendTokenToBackend('apple', {
            authorization: data.authorization,
            user: data.user
        });
    }

    handleFacebookCallback(response) {
        // Récupérer les informations utilisateur
        FB.api('/me', { fields: 'name,email,picture' }, (userInfo) => {
            this.sendTokenToBackend('facebook', {
                accessToken: response.authResponse.accessToken,
                userInfo: userInfo
            });
        });
    }

    async sendTokenToBackend(provider, data) {
        try {
            const response = await fetch(`/auth/${provider}/`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': this.getCSRFToken()
                },
                body: JSON.stringify(data)
            });

            const result = await response.json();
            
            if (result.success) {
                // Rediriger vers la page d'accueil ou la page demandée
                window.location.href = result.redirect_url || '/';
            } else {
                this.showError(provider, result.error || 'Erreur d\'authentification');
            }
        } catch (error) {
            console.error('Backend auth error:', error);
            this.showError(provider, 'Erreur de communication avec le serveur');
        }
    }

    // Utilitaires
    getCSRFToken() {
        return document.querySelector('[name=csrfmiddlewaretoken]')?.value || '';
    }

    showLoading(provider) {
        const button = document.querySelector(`[onclick*="${provider}"]`);
        if (button) {
            button.classList.add('loading');
            button.disabled = true;
        }
    }

    hideLoading(provider) {
        const button = document.querySelector(`[onclick*="${provider}"]`);
        if (button) {
            button.classList.remove('loading');
            button.disabled = false;
        }
    }

    showError(provider, message) {
        const button = document.querySelector(`[onclick*="${provider}"]`);
        if (button) {
            button.classList.add('error');
            // Afficher le message d'erreur
            const errorDiv = document.createElement('div');
            errorDiv.className = 'text-red-500 text-sm mt-2';
            errorDiv.textContent = message;
            button.parentNode.insertBefore(errorDiv, button.nextSibling);
            
            // Supprimer l'erreur après 5 secondes
            setTimeout(() => {
                button.classList.remove('error');
                errorDiv.remove();
            }, 5000);
        }
    }

    // Chargement dynamique des scripts
    loadGoogleScript() {
        return new Promise((resolve, reject) => {
            const script = document.createElement('script');
            script.src = 'https://accounts.google.com/gsi/client';
            script.onload = resolve;
            script.onerror = reject;
            document.head.appendChild(script);
        });
    }

    loadAppleScript() {
        return new Promise((resolve, reject) => {
            const script = document.createElement('script');
            script.src = 'https://appleid.cdn-apple.com/appleauth/static/jsapi/appleid/1/en_US/appleid.auth.js';
            script.onload = resolve;
            script.onerror = reject;
            document.head.appendChild(script);
        });
    }

    loadFacebookScript() {
        return new Promise((resolve, reject) => {
            const script = document.createElement('script');
            script.src = 'https://connect.facebook.net/en_US/sdk.js';
            script.onload = resolve;
            script.onerror = reject;
            document.head.appendChild(script);
        });
    }
}

// Initialiser l'authentification sociale
const socialAuth = new SocialAuth();
