#!/usr/bin/env python
"""
Script de test complet pour l'authentification OAuth
"""
import os
import django
import requests

# Configuration Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'airbnb_clone.settings')
django.setup()

from allauth.socialaccount.models import SocialApp
from django.contrib.sites.models import Site

def test_oauth_configuration():
    """Teste la configuration OAuth complète"""
    
    print("🧪 Test de la Configuration OAuth Complète")
    print("=" * 50)
    
    # Vérifier le site
    try:
        site = Site.objects.get(pk=1)
        print(f"✅ Site configuré : {site.domain}")
    except Site.DoesNotExist:
        print("❌ Site non configuré")
        return False
    
    # Vérifier les applications OAuth
    providers = ['google', 'facebook', 'apple']
    configured_providers = []
    
    for provider in providers:
        try:
            app = SocialApp.objects.get(provider=provider)
            print(f"✅ {provider.title()} OAuth configuré")
            print(f"   📱 Client ID : {app.client_id}")
            print(f"   🔐 Secret : {app.secret[:10]}...")
            configured_providers.append(provider)
        except SocialApp.DoesNotExist:
            print(f"❌ {provider.title()} OAuth non configuré")
    
    print(f"\n📊 Résumé : {len(configured_providers)}/3 providers configurés")
    
    # Test des URLs
    print("\n🌐 Test des URLs OAuth :")
    base_url = "http://127.0.0.1:8000"
    
    test_urls = [
        "/signup/",
        "/accounts/google/login/",
        "/accounts/facebook/login/",
        "/accounts/apple/login/",
    ]
    
    for url in test_urls:
        try:
            response = requests.get(f"{base_url}{url}", timeout=5)
            if response.status_code == 200:
                print(f"✅ {url} - OK")
            elif response.status_code == 302:
                print(f"🔄 {url} - Redirection (normal pour OAuth)")
            else:
                print(f"⚠️  {url} - Status {response.status_code}")
        except requests.exceptions.RequestException as e:
            print(f"❌ {url} - Erreur : {str(e)}")
    
    # Instructions de test manuel
    print("\n🎯 Test Manuel Recommandé :")
    print("1. Ouvrir : http://127.0.0.1:8000/signup/")
    print("2. Cliquer sur 'Continue with Google'")
    print("3. Vérifier la redirection vers Google OAuth")
    print("4. Autoriser l'application (si clés réelles)")
    print("5. Vérifier la redirection vers finish-signup")
    print("6. Vérifier que les champs sont pré-remplis")
    
    print("\n📋 URLs de Callback à Configurer :")
    print("🔵 Google :")
    print("   - http://localhost:8000/accounts/google/login/callback/")
    print("   - http://127.0.0.1:8000/accounts/google/login/callback/")
    print("📘 Facebook :")
    print("   - http://localhost:8000/accounts/facebook/login/callback/")
    print("   - http://127.0.0.1:8000/accounts/facebook/login/callback/")
    print("🍎 Apple :")
    print("   - http://localhost:8000/accounts/apple/login/callback/")
    print("   - http://127.0.0.1:8000/accounts/apple/login/callback/")
    
    print("\n🚀 Configuration OAuth Terminée !")
    print("Tous les providers sont prêts pour l'authentification sociale.")
    
    return True

if __name__ == '__main__':
    test_oauth_configuration()
