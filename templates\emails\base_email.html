<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}{{ site_name }}{% endblock %}</title>
    <style>
        /* Reset styles */
        body, table, td, p, a, li, blockquote {
            -webkit-text-size-adjust: 100%;
            -ms-text-size-adjust: 100%;
        }
        table, td {
            mso-table-lspace: 0pt;
            mso-table-rspace: 0pt;
        }
        img {
            -ms-interpolation-mode: bicubic;
            border: 0;
            height: auto;
            line-height: 100%;
            outline: none;
            text-decoration: none;
        }

        /* Base styles */
        body {
            margin: 0 !important;
            padding: 0 !important;
            background-color: #f8fafc;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #374151;
        }

        .email-container {
            max-width: 600px;
            margin: 0 auto;
            background-color: #ffffff;
        }

        .email-header {
            background: linear-gradient(135deg, #FF5A5F 0%, #E31C5F 100%);
            padding: 40px 30px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .email-header::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
            opacity: 0.3;
            z-index: 1;
        }

        .email-logo-container {
            position: relative;
            z-index: 2;
        }

        .email-logo {
            color: #ffffff;
            font-size: 36px;
            font-weight: 900;
            text-decoration: none;
            margin-bottom: 8px;
            display: inline-block;
            letter-spacing: 2px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.2);
        }

        .email-logo-icon {
            display: inline-block;
            width: 40px;
            height: 40px;
            margin-right: 12px;
            vertical-align: middle;
            filter: drop-shadow(0 2px 4px rgba(0,0,0,0.2));
        }

        .email-tagline {
            color: rgba(255, 255, 255, 0.95);
            font-size: 15px;
            margin: 0;
            font-weight: 500;
            letter-spacing: 1px;
            text-shadow: 0 1px 2px rgba(0,0,0,0.2);
        }

        .email-content {
            padding: 40px 30px;
        }

        .email-title {
            font-size: 24px;
            font-weight: bold;
            color: #1f2937;
            margin: 0 0 20px 0;
            text-align: center;
        }

        .email-text {
            font-size: 16px;
            line-height: 1.6;
            color: #374151;
            margin: 0 0 20px 0;
        }

        .email-button {
            display: inline-block;
            padding: 16px 32px;
            background: linear-gradient(135deg, #FF5A5F 0%, #E31C5F 100%);
            color: #ffffff !important;
            text-decoration: none;
            border-radius: 12px;
            font-weight: 700;
            font-size: 16px;
            text-align: center;
            margin: 24px 0;
            box-shadow: 0 4px 12px rgba(255, 90, 95, 0.3);
            transition: all 0.3s ease;
            letter-spacing: 0.5px;
        }

        .email-button:hover {
            background: linear-gradient(135deg, #E31C5F 0%, #C41E3A 100%);
            box-shadow: 0 6px 16px rgba(255, 90, 95, 0.4);
            transform: translateY(-2px);
        }

        .email-button-secondary {
            background: linear-gradient(135deg, #6B7280 0%, #4B5563 100%);
            box-shadow: 0 4px 12px rgba(107, 114, 128, 0.3);
        }

        .email-button-secondary:hover {
            background: linear-gradient(135deg, #4B5563 0%, #374151 100%);
            box-shadow: 0 6px 16px rgba(107, 114, 128, 0.4);
        }

        .email-footer {
            background-color: #f9fafb;
            padding: 30px;
            text-align: center;
            border-top: 1px solid #e5e7eb;
        }

        .email-footer-text {
            font-size: 14px;
            color: #6b7280;
            margin: 0 0 10px 0;
        }

        .email-footer-links {
            margin: 20px 0;
        }

        .email-footer-link {
            color: #3b82f6;
            text-decoration: none;
            margin: 0 15px;
            font-size: 14px;
        }

        .email-footer-link:hover {
            text-decoration: underline;
        }

        .email-divider {
            height: 1px;
            background-color: #e5e7eb;
            margin: 30px 0;
        }

        /* Responsive styles */
        @media only screen and (max-width: 600px) {
            .email-container {
                width: 100% !important;
            }
            .email-header,
            .email-content,
            .email-footer {
                padding: 20px !important;
            }
            .email-title {
                font-size: 20px !important;
            }
            .email-text {
                font-size: 14px !important;
            }
        }
    </style>
</head>
<body>
    <div class="email-container">
        <!-- Header -->
        <div class="email-header">
            <div class="email-logo-container">
                <a href="{{ site_url }}" class="email-logo" style="display: flex; align-items: center; justify-content: center; text-decoration: none;">
                    {% load static %}
                    <img src="{{ request.build_absolute_uri }}{% static 'images/nestria-logo-new.png' %}"
                         alt="Nestria Logo"
                         style="height: 80px; width: auto; margin-bottom: 10px; filter: drop-shadow(0 2px 4px rgba(0,0,0,0.2));"
                         onerror="this.style.display='none'; this.nextElementSibling.style.display='inline';">
                    <span style="display: none;">🏠</span>
                </a>
                <p class="email-tagline">✈️ Travel to Survive 🌍</p>
            </div>
        </div>

        <!-- Content -->
        <div class="email-content">
            {% block content %}
            <h1 class="email-title">{% block email_title %}Welcome to Nestria{% endblock %}</h1>
            <p class="email-text">{% block email_text %}Thank you for joining our community!{% endblock %}</p>
            {% endblock %}
        </div>

        <!-- Footer -->
        <div class="email-footer">
            <p class="email-footer-text">
                This email was sent by {{ site_name }}
            </p>
            <div class="email-footer-links">
                <a href="{{ site_url }}" class="email-footer-link">Visit Website</a>
                <a href="{{ site_url }}/support/" class="email-footer-link">Support</a>
                <a href="{{ site_url }}/unsubscribe/" class="email-footer-link">Unsubscribe</a>
            </div>
            <p class="email-footer-text">
                © {% now "Y" %} {{ site_name }}. All rights reserved.
            </p>
        </div>
    </div>
</body>
</html>
