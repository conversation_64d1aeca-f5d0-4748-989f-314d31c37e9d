{% extends 'base.html' %}
{% load static %}

{% block title %}Forgot Password - Nestria{% endblock %}

{% block extra_css %}
<style>
    .forgot-container {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
    }
    
    .forgot-card {
        backdrop-filter: blur(10px);
        background: rgba(255, 255, 255, 0.95);
        border: 1px solid rgba(255, 255, 255, 0.2);
    }
    
    .input-field {
        transition: all 0.3s ease;
        border: 2px solid #e5e7eb;
    }
    
    .input-field:focus {
        border-color: #3b82f6;
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    }
    
    .nestria-logo {
        animation: float 3s ease-in-out infinite;
    }
    
    @keyframes float {
        0%, 100% { transform: translateY(0px); }
        50% { transform: translateY(-10px); }
    }
</style>
{% endblock %}

{% block content %}
<div class="forgot-container flex items-center justify-center p-4">
    <div class="w-full max-w-md">
        <!-- Logo et titre -->
        <div class="text-center mb-8">
            <div class="nestria-logo mb-4">
                <img src="{% static 'images/nestria-logo-transparent.png' %}" 
                     alt="Nestria" 
                     class="w-20 h-20 mx-auto">
            </div>
            <h1 class="text-3xl font-bold text-white mb-2">Forgot your password?</h1>
            <p class="text-white/80">Enter your email and we'll send you a temporary password</p>
        </div>

        <!-- Carte de réinitialisation -->
        <div class="forgot-card rounded-2xl p-8 shadow-2xl">
            <!-- Formulaire -->
            <form method="post" class="space-y-6">
                {% csrf_token %}
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-envelope mr-2"></i>Email Address
                    </label>
                    <input type="email" 
                           name="email" 
                           required
                           class="input-field w-full px-4 py-3 rounded-xl focus:outline-none"
                           placeholder="Enter your email address">
                </div>
                
                <button type="submit" 
                        class="w-full bg-gradient-to-r from-blue-500 to-purple-600 text-white py-3 rounded-xl font-medium hover:from-blue-600 hover:to-purple-700 transition-all duration-200 transform hover:scale-105">
                    <i class="fas fa-paper-plane mr-2"></i>
                    Send Temporary Password
                </button>
            </form>

            <!-- Messages d'erreur/succès -->
            {% if messages %}
                <div class="mt-6 space-y-3">
                    {% for message in messages %}
                        <div class="p-4 rounded-lg {% if message.tags == 'error' %}bg-red-100 text-red-700 border border-red-200{% elif message.tags == 'success' %}bg-green-100 text-green-700 border border-green-200{% else %}bg-blue-100 text-blue-700 border border-blue-200{% endif %}">
                            <div class="flex items-start">
                                {% if message.tags == 'error' %}
                                    <i class="fas fa-exclamation-circle mt-0.5 mr-3"></i>
                                {% elif message.tags == 'success' %}
                                    <i class="fas fa-check-circle mt-0.5 mr-3"></i>
                                {% else %}
                                    <i class="fas fa-info-circle mt-0.5 mr-3"></i>
                                {% endif %}
                                <div>{{ message }}</div>
                            </div>
                        </div>
                    {% endfor %}
                </div>
            {% endif %}

            <!-- Liens de retour -->
            <div class="mt-8 text-center space-y-4">
                <div class="flex items-center justify-center space-x-4">
                    <a href="{% url 'core:modern_login' %}" 
                       class="text-blue-600 hover:text-blue-800 font-medium flex items-center">
                        <i class="fas fa-arrow-left mr-2"></i>
                        Back to Login
                    </a>
                </div>
                
                <div class="text-sm text-gray-600">
                    Don't have an account? 
                    <a href="{% url 'core:modern_login' %}" class="text-blue-600 hover:text-blue-800 font-medium">
                        Sign up here
                    </a>
                </div>
            </div>

            <!-- Instructions -->
            <div class="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-xl">
                <div class="flex items-start">
                    <i class="fas fa-info-circle text-blue-600 mt-1 mr-3"></i>
                    <div class="text-sm text-blue-700">
                        <h4 class="font-semibold mb-2">How it works:</h4>
                        <ul class="space-y-1">
                            <li>• Enter your email address</li>
                            <li>• We'll send you a temporary password</li>
                            <li>• Use it to log in and change your password</li>
                            <li>• For security, change your password after logging in</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Auto-focus sur le champ email
document.addEventListener('DOMContentLoaded', function() {
    const emailInput = document.querySelector('input[name="email"]');
    if (emailInput) {
        emailInput.focus();
    }
});
</script>
{% endblock %}
