// City Autocomplete functionality for search
class CityAutocomplete {
    constructor(inputElement, dropdownElement) {
        this.input = inputElement;
        this.dropdown = dropdownElement;
        this.cities = [];
        this.selectedIndex = -1;
        this.isOpen = false;
        
        this.init();
    }
    
    init() {
        this.createDropdown();
        this.bindEvents();
    }
    
    createDropdown() {
        if (!this.dropdown) {
            this.dropdown = document.createElement('div');
            this.dropdown.className = 'city-dropdown absolute top-full left-0 right-0 bg-white border border-gray-200 rounded-lg shadow-lg z-50 max-h-60 overflow-y-auto hidden';
            this.input.parentElement.appendChild(this.dropdown);
            this.input.parentElement.style.position = 'relative';
        }
    }
    
    bindEvents() {
        // Input events
        this.input.addEventListener('input', this.debounce(this.handleInput.bind(this), 300));
        this.input.addEventListener('focus', this.handleFocus.bind(this));
        this.input.addEventListener('keydown', this.handleKeydown.bind(this));
        
        // Click outside to close
        document.addEventListener('click', (e) => {
            if (!this.input.parentElement.contains(e.target)) {
                this.hideDropdown();
            }
        });
    }
    
    async handleInput(e) {
        const query = e.target.value.trim();
        
        if (query.length < 2) {
            this.hideDropdown();
            return;
        }
        
        try {
            const response = await fetch(`/search/api/cities/?q=${encodeURIComponent(query)}`);
            const data = await response.json();
            this.cities = data.cities || [];
            this.renderDropdown();
        } catch (error) {
            console.error('Error fetching cities:', error);
            this.hideDropdown();
        }
    }
    
    handleFocus() {
        if (this.cities.length > 0) {
            this.showDropdown();
        }
    }
    
    handleKeydown(e) {
        if (!this.isOpen) return;
        
        switch (e.key) {
            case 'ArrowDown':
                e.preventDefault();
                this.selectedIndex = Math.min(this.selectedIndex + 1, this.cities.length - 1);
                this.updateSelection();
                break;
            case 'ArrowUp':
                e.preventDefault();
                this.selectedIndex = Math.max(this.selectedIndex - 1, -1);
                this.updateSelection();
                break;
            case 'Enter':
                e.preventDefault();
                if (this.selectedIndex >= 0) {
                    this.selectCity(this.cities[this.selectedIndex]);
                }
                break;
            case 'Escape':
                this.hideDropdown();
                break;
        }
    }
    
    renderDropdown() {
        if (this.cities.length === 0) {
            this.hideDropdown();
            return;
        }
        
        this.dropdown.innerHTML = this.cities.map((city, index) => `
            <div class="city-option px-4 py-3 hover:bg-gray-50 cursor-pointer border-b border-gray-100 last:border-b-0 ${index === this.selectedIndex ? 'bg-blue-50' : ''}" 
                 data-index="${index}">
                <div class="font-medium text-gray-900">${city.city}</div>
                <div class="text-sm text-gray-500">${city.country}</div>
            </div>
        `).join('');
        
        // Add click events to options
        this.dropdown.querySelectorAll('.city-option').forEach((option, index) => {
            option.addEventListener('click', () => {
                this.selectCity(this.cities[index]);
            });
        });
        
        this.showDropdown();
    }
    
    selectCity(city) {
        this.input.value = city.display;
        this.hideDropdown();
        
        // Trigger change event
        this.input.dispatchEvent(new Event('change', { bubbles: true }));
    }
    
    updateSelection() {
        this.dropdown.querySelectorAll('.city-option').forEach((option, index) => {
            if (index === this.selectedIndex) {
                option.classList.add('bg-blue-50');
                option.scrollIntoView({ block: 'nearest' });
            } else {
                option.classList.remove('bg-blue-50');
            }
        });
    }
    
    showDropdown() {
        this.dropdown.classList.remove('hidden');
        this.isOpen = true;
    }
    
    hideDropdown() {
        this.dropdown.classList.add('hidden');
        this.isOpen = false;
        this.selectedIndex = -1;
    }
    
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }
}

// Initialize city autocomplete when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    const locationInput = document.getElementById('location-input');
    if (locationInput) {
        new CityAutocomplete(locationInput);
    }
});
