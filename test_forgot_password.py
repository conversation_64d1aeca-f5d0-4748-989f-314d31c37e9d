#!/usr/bin/env python
"""
Script pour tester la fonctionnalité "Forgot Password"
"""
import os
import django
import requests

# Configuration Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'airbnb_clone.settings')
django.setup()

from django.contrib.auth.models import User

def test_forgot_password_system():
    """Tester le système de mot de passe oublié"""
    
    print("🔐 Test du système 'Forgot Password'\n")
    
    # Test 1: Vérifier que la page est accessible
    print("1️⃣ Test d'accessibilité de la page...")
    try:
        response = requests.get("http://127.0.0.1:8000/forgot-password/")
        if response.status_code == 200:
            print("   ✅ Page 'Forgot Password' accessible")
        else:
            print(f"   ❌ Erreur {response.status_code}")
    except Exception as e:
        print(f"   ❌ Erreur de connexion: {e}")
    
    # Test 2: Vérifier les comptes de test
    print(f"\n2️⃣ Vérification des comptes de test...")
    test_emails = [
        "<EMAIL>",
        "<EMAIL>", 
        "<EMAIL>"
    ]
    
    available_accounts = []
    for email in test_emails:
        try:
            user = User.objects.get(email=email)
            available_accounts.append(email)
            print(f"   ✅ {email} - Disponible")
        except User.DoesNotExist:
            print(f"   ❌ {email} - Non trouvé")
    
    # Test 3: Simuler une réinitialisation de mot de passe
    if available_accounts:
        test_email = available_accounts[0]
        print(f"\n3️⃣ Test de réinitialisation pour {test_email}...")
        
        try:
            user = User.objects.get(email=test_email)
            old_password_hash = user.password
            
            # Simuler la génération d'un mot de passe temporaire
            import random
            import string
            temp_password = ''.join(random.choices(string.ascii_letters + string.digits, k=8))
            
            # Mettre à jour le mot de passe
            user.set_password(temp_password)
            user.save()
            
            # Vérifier que le mot de passe a changé
            user.refresh_from_db()
            if user.password != old_password_hash:
                print(f"   ✅ Mot de passe mis à jour avec succès")
                print(f"   🔑 Mot de passe temporaire: {temp_password}")
                
                # Remettre l'ancien mot de passe pour les tests
                user.set_password('password123')
                user.save()
                print(f"   🔄 Mot de passe restauré à 'password123'")
            else:
                print(f"   ❌ Le mot de passe n'a pas été mis à jour")
                
        except Exception as e:
            print(f"   ❌ Erreur lors du test: {e}")
    
    # Test 4: Vérifier le lien dans la page de login
    print(f"\n4️⃣ Vérification du lien dans la page de login...")
    try:
        response = requests.get("http://127.0.0.1:8000/modern-login/")
        if "forgot-password" in response.text:
            print("   ✅ Lien 'Forgot Password' présent dans la page de login")
        else:
            print("   ❌ Lien 'Forgot Password' manquant")
    except Exception as e:
        print(f"   ❌ Erreur: {e}")
    
    # Résumé
    print(f"\n" + "="*50)
    print(f"🎯 FONCTIONNALITÉS IMPLÉMENTÉES")
    print(f"="*50)
    print(f"✅ Page 'Forgot Password' avec design moderne")
    print(f"✅ Génération de mot de passe temporaire")
    print(f"✅ Mise à jour automatique du mot de passe utilisateur")
    print(f"✅ Template d'email pour l'envoi du mot de passe")
    print(f"✅ Lien intégré dans la page de login")
    print(f"✅ Gestion d'erreur et messages utilisateur")
    print(f"✅ Mode développement avec affichage du mot de passe")
    
    print(f"\n🎮 COMMENT TESTER:")
    print(f"1. Aller sur: http://127.0.0.1:8000/modern-login/")
    print(f"2. Cliquer sur 'Forgot your password?'")
    print(f"3. Entrer un email de test: {available_accounts[0] if available_accounts else '<EMAIL>'}")
    print(f"4. Le mot de passe temporaire s'affichera (mode développement)")
    print(f"5. Utiliser ce mot de passe pour se connecter")
    
    print(f"\n💡 SÉCURITÉ:")
    print(f"- Les mots de passe temporaires sont générés aléatoirement")
    print(f"- L'ancien mot de passe est immédiatement remplacé")
    print(f"- Messages de sécurité pour ne pas révéler si un email existe")
    print(f"- Instructions claires pour changer le mot de passe après connexion")

if __name__ == '__main__':
    test_forgot_password_system()
