#!/usr/bin/env python
"""
Script pour configurer les applications OAuth dans Django Allauth
"""
import os
import django

# Configuration Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'airbnb_clone.settings')
django.setup()

from allauth.socialaccount.models import SocialApp
from django.contrib.sites.models import Site

def setup_oauth_apps():
    """Configure les applications OAuth pour Google, Facebook et Apple"""
    
    # Récupérer le site par défaut
    site = Site.objects.get(pk=1)
    site.domain = 'localhost:8000'
    site.name = 'Nestria Local'
    site.save()
    
    print(f"✅ Site configuré: {site.domain}")
    
    # Configuration Google OAuth
    google_app, created = SocialApp.objects.get_or_create(
        provider='google',
        defaults={
            'name': 'Google OAuth',
            'client_id': 'your-google-client-id.apps.googleusercontent.com',
            'secret': 'your-google-client-secret',
        }
    )
    google_app.sites.add(site)
    
    if created:
        print("✅ Application Google OAuth créée")
    else:
        print("ℹ️  Application Google OAuth existe déjà")
    
    # Configuration Facebook OAuth
    facebook_app, created = SocialApp.objects.get_or_create(
        provider='facebook',
        defaults={
            'name': 'Facebook OAuth',
            'client_id': 'your-facebook-app-id',
            'secret': 'your-facebook-app-secret',
        }
    )
    facebook_app.sites.add(site)
    
    if created:
        print("✅ Application Facebook OAuth créée")
    else:
        print("ℹ️  Application Facebook OAuth existe déjà")
    
    # Configuration Apple OAuth
    apple_app, created = SocialApp.objects.get_or_create(
        provider='apple',
        defaults={
            'name': 'Apple Sign In',
            'client_id': 'com.nestria.web',
            'secret': 'your-apple-secret-key',
        }
    )
    apple_app.sites.add(site)
    
    if created:
        print("✅ Application Apple OAuth créée")
    else:
        print("ℹ️  Application Apple OAuth existe déjà")
    
    print("\n🎯 Configuration OAuth terminée!")
    print("\n📝 Prochaines étapes:")
    print("1. Aller sur https://console.developers.google.com pour créer une app Google")
    print("2. Aller sur https://developers.facebook.com pour créer une app Facebook")
    print("3. Aller sur https://developer.apple.com pour configurer Apple Sign In")
    print("4. Mettre à jour les client_id et secrets dans l'admin Django")
    print("5. URL de callback Google: http://localhost:8000/accounts/google/login/callback/")
    print("6. URL de callback Facebook: http://localhost:8000/accounts/facebook/login/callback/")
    print("7. URL de callback Apple: http://localhost:8000/accounts/apple/login/callback/")

if __name__ == '__main__':
    setup_oauth_apps()
