#!/usr/bin/env python
"""
Test de la redirection vers la page de signup par téléphone
"""
import requests

def test_phone_signup_redirect():
    """Tester que les URLs redirigent vers la page de signup par téléphone"""
    
    print("📱 Test de redirection vers la page de signup par téléphone\n")
    
    base_url = "http://127.0.0.1:8000"
    
    # URLs à tester
    test_urls = [
        ("/login/", "Page de login"),
        ("/signup/", "Page de signup"),
        ("/phone-signup/", "Page de signup par téléphone directe"),
    ]
    
    print("1️⃣ Test d'accessibilité des pages...")
    for url, description in test_urls:
        try:
            response = requests.get(f"{base_url}{url}")
            if response.status_code == 200:
                # Vérifier que la page contient les éléments de signup par téléphone
                content = response.text
                has_phone_input = "phone" in content.lower()
                has_country_code = "country code" in content.lower()
                has_continue_btn = "continue" in content.lower()
                
                print(f"   ✅ {description}: Accessible")
                if has_phone_input and has_country_code and has_continue_btn:
                    print(f"      ✅ Contient les éléments de signup par téléphone")
                else:
                    print(f"      ⚠️  Éléments manquants: Phone={has_phone_input}, Country={has_country_code}, Continue={has_continue_btn}")
            else:
                print(f"   ❌ {description}: Erreur {response.status_code}")
        except Exception as e:
            print(f"   ❌ {description}: Erreur - {e}")
    
    # Test de la page d'accueil
    print(f"\n2️⃣ Test de la page d'accueil...")
    try:
        response = requests.get(f"{base_url}/")
        if response.status_code == 200:
            print(f"   ✅ Page d'accueil accessible")
        else:
            print(f"   ❌ Page d'accueil: Erreur {response.status_code}")
    except Exception as e:
        print(f"   ❌ Page d'accueil: Erreur - {e}")
    
    # Test des pages de démonstration
    print(f"\n3️⃣ Test des pages de démonstration...")
    demo_urls = [
        ("/signup-demo/", "Page de démonstration signup"),
        ("/test-signup/", "Page de test signup"),
    ]
    
    for url, description in demo_urls:
        try:
            response = requests.get(f"{base_url}{url}")
            if response.status_code == 200:
                print(f"   ✅ {description}: Accessible")
            else:
                print(f"   ❌ {description}: Erreur {response.status_code}")
        except Exception as e:
            print(f"   ❌ {description}: Erreur - {e}")
    
    # Résumé
    print(f"\n" + "="*50)
    print(f"🎯 CONFIGURATION ACTUELLE")
    print(f"="*50)
    print(f"✅ /login/ → Page de signup par téléphone")
    print(f"✅ /signup/ → Page de signup par téléphone")
    print(f"✅ /phone-signup/ → Page de signup par téléphone")
    print(f"✅ Design avec logo Nestria et formulaire téléphone")
    print(f"✅ Sélecteur de code pays (Morocco +212)")
    print(f"✅ Champ de saisie du numéro de téléphone")
    print(f"✅ Bouton 'Continue' pour la vérification")
    
    print(f"\n🎮 COMMENT TESTER:")
    print(f"1. Aller sur: {base_url}/signup/")
    print(f"2. Sélectionner le code pays (Morocco +212)")
    print(f"3. Saisir un numéro de téléphone: +212 603999557")
    print(f"4. Cliquer sur 'Continue'")
    print(f"5. Vous serez redirigé vers la page de vérification")
    
    print(f"\n📱 FONCTIONNALITÉS:")
    print(f"- Interface moderne avec logo Nestria")
    print(f"- Sélecteur de code pays avec options multiples")
    print(f"- Validation du numéro de téléphone")
    print(f"- Redirection vers la vérification WhatsApp")
    print(f"- Messages d'erreur en cas de problème")
    
    print(f"\n🚀 La redirection vers la page de signup par téléphone est configurée !")

if __name__ == '__main__':
    test_phone_signup_redirect()
