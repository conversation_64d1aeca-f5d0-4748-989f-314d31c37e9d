"""
Service de vérification simple et efficace
Affiche clairement le code pour les tests
"""

import logging

logger = logging.getLogger(__name__)


class SimpleVerificationService:
    """Service de vérification simple pour développement"""
    
    def send_verification_code(self, phone_number, verification_code):
        """
        Envoie un code de vérification (simulation claire)
        
        Args:
            phone_number (str): Numéro de téléphone
            verification_code (str): Code à 6 chiffres
            
        Returns:
            bool: Toujours True (simulation)
        """
        
        # Affichage console très visible
        print("\n" + "🟢" * 50)
        print("📱 WHATSAPP MESSAGE SENT!")
        print("🟢" * 50)
        print(f"📞 TO: {phone_number}")
        print(f"🔐 CODE: {verification_code}")
        print(f"💬 MESSAGE:")
        print(f"   🏠 Nestria Verification")
        print(f"   ")
        print(f"   Your verification code is: {verification_code}")
        print(f"   ")
        print(f"   This code will expire in 5 minutes.")
        print(f"   Do not share this code with anyone.")
        print(f"   ")
        print(f"   Welcome to Nestria! 🌟")
        print("🟢" * 50)
        print("✅ MESSAGE DELIVERED SUCCESSFULLY!")
        print("🟢" * 50 + "\n")
        
        # Log pour Django
        logger.info(f"📱 WhatsApp verification code sent to {phone_number}: {verification_code}")
        
        return True


# Instance globale
simple_verification_service = SimpleVerificationService()


def send_verification_simple(phone_number, verification_code):
    """
    Fonction helper simple
    """
    return simple_verification_service.send_verification_code(phone_number, verification_code)
