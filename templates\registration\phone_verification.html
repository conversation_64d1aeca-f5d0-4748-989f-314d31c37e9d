<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Confirm your number - Nestria</title>
    {% load static %}
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'nestria-red': '#FF5A5F',
                    }
                }
            }
        }
    </script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body class="bg-gray-50 min-h-screen flex items-center justify-center p-4">
    <div class="bg-white rounded-2xl shadow-xl w-full max-w-md p-8 relative">
        <!-- Back Button -->
        <button onclick="window.history.back()" class="absolute top-4 left-4 text-gray-600 hover:text-gray-800 text-xl">
            <i class="fas fa-arrow-left"></i>
        </button>

        <!-- Header -->
        <div class="text-center mb-8 mt-8">
            <h1 class="text-2xl font-semibold text-gray-900">Confirm your number</h1>
        </div>

        <!-- Instructions -->
        <div class="mb-8">
            <p class="text-gray-700 text-base" id="verification-instructions">
                {% if verification_method == 'email' %}
                    Enter the code we sent to your email <span class="font-semibold">{{ verification_email }}</span>:
                {% else %}
                    Enter the code we sent over WhatsApp to <span class="font-semibold">{{ phone_number|default:"+212 603999557" }}</span>:
                {% endif %}
            </p>
        </div>

        <!-- Verification Code Input -->
        <form method="post" class="space-y-6">
            {% csrf_token %}
            <input type="hidden" name="phone_number" value="{{ phone_number }}">
            
            <div class="flex justify-center">
                <div class="flex space-x-3">
                    <input type="text" maxlength="1" class="w-12 h-12 text-center text-xl font-semibold border-2 border-gray-300 rounded-lg focus:border-nestria-red focus:outline-none verification-digit" data-index="0">
                    <input type="text" maxlength="1" class="w-12 h-12 text-center text-xl font-semibold border-2 border-gray-300 rounded-lg focus:border-nestria-red focus:outline-none verification-digit" data-index="1">
                    <input type="text" maxlength="1" class="w-12 h-12 text-center text-xl font-semibold border-2 border-gray-300 rounded-lg focus:border-nestria-red focus:outline-none verification-digit" data-index="2">
                    <input type="text" maxlength="1" class="w-12 h-12 text-center text-xl font-semibold border-2 border-gray-300 rounded-lg focus:border-nestria-red focus:outline-none verification-digit" data-index="3">
                    <input type="text" maxlength="1" class="w-12 h-12 text-center text-xl font-semibold border-2 border-gray-300 rounded-lg focus:border-nestria-red focus:outline-none verification-digit" data-index="4">
                    <input type="text" maxlength="1" class="w-12 h-12 text-center text-xl font-semibold border-2 border-gray-300 rounded-lg focus:border-nestria-red focus:outline-none verification-digit" data-index="5">
                </div>
            </div>

            <input type="hidden" name="verification_code" id="verification_code">

            <!-- Error Message -->
            {% if error %}
            <div class="text-red-500 text-sm text-center">
                {{ error }}
            </div>
            {% endif %}

            <!-- Options -->
            <div class="text-left space-y-2">
                <button type="button" onclick="showChangePhoneModal()" class="text-blue-600 underline hover:text-blue-800 font-medium block">
                    Use a different phone number
                </button>
                <button type="button" onclick="chooseDifferentOption()" class="text-gray-700 underline hover:text-gray-900 font-medium block">
                    Choose a different option
                </button>
            </div>

            <!-- Continue Button -->
            <button type="submit" id="continue-btn" class="w-full bg-gray-300 text-gray-500 py-3 rounded-lg font-semibold cursor-not-allowed transition-all" disabled>
                Continue
            </button>
        </form>

        <!-- Resend Code -->
        <div class="text-center mt-6">
            <p class="text-sm text-gray-600">
                Didn't receive the code?
                <button onclick="resendCode()" class="text-nestria-red hover:underline font-medium">
                    Resend
                </button>
            </p>
        </div>


    </div>

    <!-- Modal pour changer de numéro -->
    <div id="changePhoneModal" class="fixed inset-0 bg-black bg-opacity-50 hidden flex items-center justify-center z-50">
        <div class="bg-white rounded-2xl shadow-xl w-full max-w-md p-8 m-4">
            <div class="flex justify-between items-center mb-6">
                <h2 class="text-xl font-semibold text-gray-900">Change phone number</h2>
                <button onclick="hideChangePhoneModal()" class="text-gray-400 hover:text-gray-600">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>

            <div class="mb-6">
                <p class="text-gray-600 text-sm mb-4">
                    Enter a new phone number to receive the verification code.
                </p>

                <div class="relative">
                    <input type="tel"
                           id="newPhoneNumber"
                           placeholder="+212 603 999 557"
                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-nestria-red focus:border-transparent transition-all"
                           required>
                    <i class="fas fa-phone absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                </div>
            </div>

            <div class="flex space-x-3">
                <button onclick="hideChangePhoneModal()"
                        class="flex-1 px-4 py-3 border border-gray-300 rounded-lg text-gray-700 font-medium hover:bg-gray-50 transition-all">
                    Cancel
                </button>
                <button onclick="changePhoneNumber()"
                        id="changePhoneBtn"
                        class="flex-1 px-4 py-3 bg-nestria-red text-white rounded-lg font-medium hover:bg-red-600 transition-all">
                    Send code
                </button>
            </div>
        </div>
    </div>

    <!-- Email Verification Modal -->
    <div id="emailModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
        <div class="bg-white rounded-lg p-6 w-full max-w-md mx-4">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-semibold text-gray-900">Verify by email</h3>
                <button onclick="hideEmailModal()" class="text-gray-400 hover:text-gray-600">
                    <i class="fas fa-times"></i>
                </button>
            </div>

            <div class="mb-6">
                <p class="text-gray-600 text-sm mb-4">
                    Enter your email address to receive the verification code.
                </p>

                <div class="relative">
                    <input type="email"
                           id="verificationEmail"
                           placeholder="<EMAIL>"
                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-nestria-red focus:border-transparent transition-all"
                           required>
                    <i class="fas fa-envelope absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                </div>
            </div>

            <div class="flex space-x-3">
                <button onclick="hideEmailModal()"
                        class="flex-1 px-4 py-3 border border-gray-300 rounded-lg text-gray-700 font-medium hover:bg-gray-50 transition-all">
                    Cancel
                </button>
                <button onclick="sendEmailVerification()"
                        id="sendEmailBtn"
                        class="flex-1 px-4 py-3 bg-nestria-red text-white rounded-lg font-medium hover:bg-red-600 transition-all">
                    Send code
                </button>
            </div>
        </div>
    </div>

    <script>
        // Gestion des champs de vérification
        const digits = document.querySelectorAll('.verification-digit');
        const continueBtn = document.getElementById('continue-btn');
        const verificationCodeInput = document.getElementById('verification_code');

        digits.forEach((digit, index) => {
            digit.addEventListener('input', function(e) {
                const value = e.target.value;
                
                // Permettre seulement les chiffres
                if (!/^\d$/.test(value)) {
                    e.target.value = '';
                    return;
                }

                // Passer au champ suivant
                if (value && index < digits.length - 1) {
                    digits[index + 1].focus();
                }

                updateContinueButton();
            });

            digit.addEventListener('keydown', function(e) {
                // Gérer la suppression
                if (e.key === 'Backspace' && !e.target.value && index > 0) {
                    digits[index - 1].focus();
                }
            });

            digit.addEventListener('paste', function(e) {
                e.preventDefault();
                const paste = e.clipboardData.getData('text');
                const pasteDigits = paste.replace(/\D/g, '').slice(0, 6);
                
                pasteDigits.split('').forEach((digit, i) => {
                    if (digits[i]) {
                        digits[i].value = digit;
                    }
                });
                
                updateContinueButton();
            });
        });

        function updateContinueButton() {
            const code = Array.from(digits).map(d => d.value).join('');
            verificationCodeInput.value = code;
            
            if (code.length === 6) {
                continueBtn.disabled = false;
                continueBtn.classList.remove('bg-gray-300', 'text-gray-500', 'cursor-not-allowed');
                continueBtn.classList.add('bg-nestria-red', 'text-white', 'hover:bg-red-600');
            } else {
                continueBtn.disabled = true;
                continueBtn.classList.add('bg-gray-300', 'text-gray-500', 'cursor-not-allowed');
                continueBtn.classList.remove('bg-nestria-red', 'text-white', 'hover:bg-red-600');
            }
        }

        function chooseDifferentOption() {
            showEmailModal();
        }

        function resendCode() {
            // Envoyer une nouvelle demande de code
            fetch('/resend-verification-code/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
                },
                body: JSON.stringify({
                    phone_number: '{{ phone_number }}'
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('Code sent successfully!');
                } else {
                    alert('Error sending code. Please try again.');
                }
            })
            .catch(error => {
                alert('Error sending code. Please try again.');
            });
        }

        function showChangePhoneModal() {
            document.getElementById('changePhoneModal').classList.remove('hidden');
            document.getElementById('newPhoneNumber').focus();
        }

        function hideChangePhoneModal() {
            document.getElementById('changePhoneModal').classList.add('hidden');
            document.getElementById('newPhoneNumber').value = '';
        }

        function changePhoneNumber() {
            const newPhoneNumber = document.getElementById('newPhoneNumber').value.trim();
            const changePhoneBtn = document.getElementById('changePhoneBtn');

            if (!newPhoneNumber) {
                alert('Please enter a phone number');
                return;
            }

            // Désactiver le bouton pendant l'envoi
            changePhoneBtn.disabled = true;
            changePhoneBtn.textContent = 'Sending...';

            fetch('/change-phone-number/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
                },
                body: JSON.stringify({
                    phone_number: newPhoneNumber
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Mettre à jour l'affichage du numéro
                    const phoneDisplay = document.querySelector('span.font-semibold');
                    if (phoneDisplay) {
                        phoneDisplay.textContent = data.new_phone_number;
                    }

                    // Mettre à jour le champ caché
                    const hiddenPhoneInput = document.querySelector('input[name="phone_number"]');
                    if (hiddenPhoneInput) {
                        hiddenPhoneInput.value = data.new_phone_number;
                    }

                    // Effacer les champs de vérification
                    digits.forEach(digit => digit.value = '');
                    updateContinueButton();

                    // Fermer le modal
                    hideChangePhoneModal();

                    // Afficher le message de succès avec plus de détails
                    if (data.status === 'simulated') {
                        alert(`🔴 SIMULATION MODE 🔴\n\nCode sent to ${data.new_phone_number}!\n\n⚠️ In simulation mode, the code is displayed in the console.\nCheck the browser console or server logs for the verification code.`);
                    } else {
                        alert(`Code sent to ${data.new_phone_number}!`);
                    }

                    // Focus sur le premier champ
                    digits[0].focus();
                } else {
                    alert(data.error || 'Error changing phone number. Please try again.');
                }
            })
            .catch(error => {
                alert('Error changing phone number. Please try again.');
            })
            .finally(() => {
                // Réactiver le bouton
                changePhoneBtn.disabled = false;
                changePhoneBtn.textContent = 'Send code';
            });
        }

        // Fermer le modal en cliquant à l'extérieur
        document.getElementById('changePhoneModal').addEventListener('click', function(e) {
            if (e.target === this) {
                hideChangePhoneModal();
            }
        });

        // Fonctions pour le modal email
        function showEmailModal() {
            document.getElementById('emailModal').classList.remove('hidden');
            document.getElementById('verificationEmail').focus();
        }

        function hideEmailModal() {
            document.getElementById('emailModal').classList.add('hidden');
            document.getElementById('verificationEmail').value = '';
        }

        function sendEmailVerification() {
            const email = document.getElementById('verificationEmail').value.trim();
            const sendEmailBtn = document.getElementById('sendEmailBtn');

            if (!email) {
                alert('Please enter your email address');
                return;
            }

            if (!email.includes('@') || !email.includes('.')) {
                alert('Please enter a valid email address');
                return;
            }

            // Désactiver le bouton pendant l'envoi
            sendEmailBtn.disabled = true;
            sendEmailBtn.textContent = 'Sending...';

            // Envoyer la demande de code par email
            fetch('/send-email-verification/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
                },
                body: JSON.stringify({
                    email: email
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Fermer le modal
                    hideEmailModal();

                    // Afficher le message de succès
                    alert(`Verification code sent to ${email}!\n\nCheck your inbox and enter the code below.`);

                    // Mettre à jour les instructions
                    document.getElementById('verification-instructions').innerHTML =
                        `Enter the code we sent to your email <span class="font-semibold">${email}</span>:`;
                } else {
                    alert(data.error || 'Error sending email. Please try again.');
                }
            })
            .catch(error => {
                alert('Error sending email. Please try again.');
            })
            .finally(() => {
                // Réactiver le bouton
                sendEmailBtn.disabled = false;
                sendEmailBtn.textContent = 'Send code';
            });
        }

        // Fermer le modal email en cliquant à l'extérieur
        document.getElementById('emailModal').addEventListener('click', function(e) {
            if (e.target === this) {
                hideEmailModal();
            }
        });

        // Gérer la touche Entrée dans le champ de nouveau numéro
        document.getElementById('newPhoneNumber').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                changePhoneNumber();
            }
        });

        // Auto-focus sur le premier champ
        digits[0].focus();
    </script>
</body>
</html>
