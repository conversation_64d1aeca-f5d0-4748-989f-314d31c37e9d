{% extends 'base.html' %}
{% load static %}

{% block title %}Experiences - Nestria{% endblock %}

{% block content %}
<div class="min-h-screen bg-gray-50 dark:bg-gray-900">
    <!-- Hero Section -->
    <div class="relative bg-gradient-to-r from-blue-600 to-purple-600 py-24">
        <div class="absolute inset-0 bg-black/20"></div>
        <div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h1 class="text-4xl md:text-6xl font-bold text-white mb-6">
                Unique Experiences
            </h1>
            <p class="text-xl text-white/90 mb-8 max-w-3xl mx-auto">
                Discover amazing activities hosted by local experts. From cooking classes to adventure tours, 
                create unforgettable memories during your travels.
            </p>
            <div class="flex flex-col sm:flex-row gap-4 justify-center">
                <a href="{% url 'core:browse_experiences' %}" class="inline-block px-8 py-3 bg-white text-blue-600 font-semibold rounded-lg hover:bg-gray-100 transition-colors">
                    Browse All Experiences
                </a>
                <a href="{% url 'core:host_experience' %}" class="inline-block px-8 py-3 border-2 border-white text-white font-semibold rounded-lg hover:bg-white hover:text-blue-600 transition-colors">
                    Host an Experience
                </a>
            </div>
        </div>
    </div>

    <!-- Categories -->
    <div class="py-16">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <h2 class="text-3xl font-bold text-gray-900 dark:text-white mb-8 text-center">
                Experience Categories
            </h2>
            
            <div class="grid grid-cols-2 md:grid-cols-4 gap-6 mb-16">
                <div class="text-center group cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700 p-4 rounded-lg transition-all duration-300 category-item" onclick="window.location.href='{% url 'core:food_drink_experiences' %}'"
                    <div class="w-16 h-16 bg-gradient-to-br from-blue-400 to-blue-600 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300 shadow-lg">
                        <i class="fas fa-utensils text-white text-2xl"></i>
                    </div>
                    <h3 class="font-semibold text-gray-900 dark:text-white">Food & Drink</h3>
                    <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">Culinary adventures</p>
                </div>

                <div class="text-center group cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700 p-4 rounded-lg transition-all duration-300 category-item" onclick="window.location.href='{% url 'core:adventure_experiences' %}'"
                    <div class="w-16 h-16 bg-gradient-to-br from-green-400 to-green-600 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300 shadow-lg">
                        <i class="fas fa-mountain text-white text-2xl"></i>
                    </div>
                    <h3 class="font-semibold text-gray-900 dark:text-white">Adventure</h3>
                    <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">Thrilling experiences</p>
                </div>

                <div class="text-center group cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700 p-4 rounded-lg transition-all duration-300 category-item" onclick="window.location.href='{% url 'core:arts_culture_experiences' %}'"
                    <div class="w-16 h-16 bg-gradient-to-br from-purple-400 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300 shadow-lg">
                        <i class="fas fa-palette text-white text-2xl"></i>
                    </div>
                    <h3 class="font-semibold text-gray-900 dark:text-white">Arts & Culture</h3>
                    <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">Creative & cultural</p>
                </div>

                <div class="text-center group cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700 p-4 rounded-lg transition-all duration-300 category-item" onclick="window.location.href='{% url 'core:photography_experiences' %}'"
                    <div class="w-16 h-16 bg-gradient-to-br from-orange-400 to-orange-600 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300 shadow-lg">
                        <i class="fas fa-camera text-white text-2xl"></i>
                    </div>
                    <h3 class="font-semibold text-gray-900 dark:text-white">Photography</h3>
                    <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">Capture moments</p>
                </div>
            </div>

            <!-- Featured Experiences -->
            <h2 class="text-3xl font-bold text-gray-900 dark:text-white mb-8">
                Featured Experiences
            </h2>
            
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                {% for experience in experiences %}
                    <div class="bg-white dark:bg-gray-800 rounded-lg overflow-hidden shadow-sm hover:shadow-lg transition-all duration-300 group">
                        <div class="relative">
                            {% if experience.main_image %}
                                <img src="{{ experience.main_image.url }}" 
                                     alt="{{ experience.name }}" 
                                     class="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300">
                            {% else %}
                                <div class="w-full h-48 bg-gradient-to-br from-blue-400 to-purple-500 flex items-center justify-center">
                                    <i class="fas fa-star text-white text-3xl"></i>
                                </div>
                            {% endif %}
                            <div class="absolute top-3 left-3">
                                <span class="bg-white/90 text-gray-900 px-2 py-1 rounded-full text-xs font-medium">
                                    Experience
                                </span>
                            </div>
                        </div>
                        
                        <div class="p-6">
                            <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-2">
                                {{ experience.name }} Cultural Tour
                            </h3>
                            <p class="text-gray-600 dark:text-gray-400 mb-4 line-clamp-2">
                                {{ experience.description|truncatewords:15 }}
                            </p>
                            
                            <div class="flex items-center justify-between mb-4">
                                <div class="flex items-center">
                                    <i class="fas fa-star text-yellow-400 mr-1"></i>
                                    <span class="text-sm text-gray-600 dark:text-gray-400">4.8 (124 reviews)</span>
                                </div>
                                <div class="flex items-center text-sm text-gray-500 dark:text-gray-400">
                                    <i class="fas fa-clock mr-1"></i>
                                    3 hours
                                </div>
                            </div>
                            
                            <div class="flex items-center justify-between">
                                <div>
                                    <span class="text-2xl font-bold text-gray-900 dark:text-white">$89</span>
                                    <span class="text-gray-500 dark:text-gray-400 text-sm"> per person</span>
                                </div>
                                <button onclick="bookExperience('{{ experience.name }} Cultural Tour', '$89')" class="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg transition-colors">
                                    Book Now
                                </button>
                            </div>
                        </div>
                    </div>
                {% empty %}
                    <!-- Sample experiences if none exist -->
                    <div class="bg-white dark:bg-gray-800 rounded-lg overflow-hidden shadow-sm hover:shadow-lg transition-all duration-300">
                        <div class="w-full h-48 bg-gradient-to-br from-blue-400 to-purple-500 flex items-center justify-center">
                            <i class="fas fa-utensils text-white text-3xl"></i>
                        </div>
                        <div class="p-6">
                            <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-2">
                                Cooking Class with Local Chef
                            </h3>
                            <p class="text-gray-600 dark:text-gray-400 mb-4">
                                Learn to cook authentic local dishes with a professional chef in their home kitchen.
                            </p>
                            <div class="flex items-center justify-between mb-4">
                                <div class="flex items-center">
                                    <i class="fas fa-star text-yellow-400 mr-1"></i>
                                    <span class="text-sm text-gray-600 dark:text-gray-400">4.9 (87 reviews)</span>
                                </div>
                                <div class="flex items-center text-sm text-gray-500 dark:text-gray-400">
                                    <i class="fas fa-clock mr-1"></i>
                                    4 hours
                                </div>
                            </div>
                            <div class="flex items-center justify-between">
                                <div>
                                    <span class="text-2xl font-bold text-gray-900 dark:text-white">$125</span>
                                    <span class="text-gray-500 dark:text-gray-400 text-sm"> per person</span>
                                </div>
                                <button onclick="bookExperience('Adventure Experience', '$125')" class="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg transition-colors">
                                    Book Now
                                </button>
                            </div>
                        </div>
                    </div>
                {% endfor %}
            </div>
        </div>
    </div>
</div>

<script>
function bookExperience(name, price) {
    if (confirm(`Book "${name}" for ${price} per person?\n\nThis will redirect you to the booking page.`)) {
        // Show booking confirmation
        showNotification(`Booking request sent for "${name}"! You will receive a confirmation email shortly.`, 'success', 5000);

        // Simulate booking process
        setTimeout(() => {
            showNotification('Booking confirmed! Check your email for details.', 'success');
        }, 2000);

        // In a real app, you would redirect to payment page
        console.log('Booking data:', { experience: name, price: price, date: new Date() });
    }
}

// Add click handlers to category cards
document.addEventListener('DOMContentLoaded', function() {
    const categories = document.querySelectorAll('.text-center.group.cursor-pointer');
    categories.forEach((category, index) => {
        category.addEventListener('click', function() {
            const categoryNames = ['Food & Drink', 'Adventure', 'Arts & Culture', 'Photography'];
            alert(`Showing ${categoryNames[index]} experiences...\n\nFiltering feature coming soon!`);
        });
    });
});
</script>
{% endblock %}
