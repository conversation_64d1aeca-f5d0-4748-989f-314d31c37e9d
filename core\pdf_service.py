"""
Service de génération de PDF pour les confirmations de réservation
"""

import io
import os
from datetime import datetime
from django.http import HttpResponse
from django.conf import settings
from reportlab.lib.pagesizes import letter, A4
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch, cm
from reportlab.lib.colors import HexColor, black, white, Color
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle, Image, PageBreak, KeepTogether
from reportlab.lib.enums import TA_CENTER, TA_LEFT, TA_RIGHT, TA_JUSTIFY
from reportlab.lib import colors
from reportlab.graphics.shapes import Drawing, Rect, Line
from reportlab.graphics import renderPDF
from reportlab.platypus.flowables import Flowable


class ColoredBox(Flowable):
    """Crée une boîte colorée pour séparer les sections"""
    def __init__(self, width, height, color):
        Flowable.__init__(self)
        self.width = width
        self.height = height
        self.color = color

    def draw(self):
        self.canv.setFillColor(self.color)
        self.canv.rect(0, 0, self.width, self.height, fill=1, stroke=0)


class GradientBox(Flowable):
    """Crée un effet de dégradé pour l'en-tête"""
    def __init__(self, width, height):
        Flowable.__init__(self)
        self.width = width
        self.height = height

    def draw(self):
        # Dégradé du rouge Nestria vers un rouge plus clair
        for i in range(int(self.height)):
            alpha = 1 - (i / self.height) * 0.7
            color = Color(1, 0.35, 0.37, alpha=alpha)  # Rouge Nestria avec transparence
            self.canv.setFillColor(color)
            self.canv.rect(0, i, self.width, 1, fill=1, stroke=0)


class ReservationPDFGenerator:
    """Générateur de PDF pour les confirmations de réservation"""
    
    def __init__(self):
        self.styles = getSampleStyleSheet()

        # Palette de couleurs professionnelle et minimaliste
        self.primary_color = HexColor('#FF5A5F')      # Rouge Nestria (utilisé avec parcimonie)
        self.text_primary = HexColor('#1A1A1A')       # Noir professionnel
        self.text_secondary = HexColor('#4A4A4A')     # Gris foncé
        self.text_light = HexColor('#6B7280')         # Gris moyen
        self.border_color = HexColor('#E5E7EB')       # Gris bordure subtile
        self.background_light = HexColor('#F9FAFB')   # Gris très clair
        self.white = HexColor('#FFFFFF')              # Blanc pur

        # Maintenant on peut configurer les styles qui utilisent les couleurs
        self.setup_custom_styles()
        
    def setup_custom_styles(self):
        """Configuration des styles professionnels et minimalistes"""

        # Style pour le titre principal - élégant et sobre
        self.styles.add(ParagraphStyle(
            name='CustomTitle',
            parent=self.styles['Heading1'],
            fontSize=24,
            spaceAfter=20,
            spaceBefore=10,
            textColor=self.text_primary,
            alignment=TA_CENTER,
            fontName='Helvetica-Bold',
            leading=28
        ))

        # Style pour les sous-titres - professionnel
        self.styles.add(ParagraphStyle(
            name='CustomHeading',
            parent=self.styles['Heading2'],
            fontSize=14,
            spaceAfter=12,
            spaceBefore=20,
            textColor=self.text_primary,
            fontName='Helvetica-Bold',
            leading=18
        ))

        # Style pour le texte normal - lisible
        self.styles.add(ParagraphStyle(
            name='CustomNormal',
            parent=self.styles['Normal'],
            fontSize=10,
            spaceAfter=6,
            textColor=self.text_secondary,
            fontName='Helvetica',
            leading=13
        ))

        # Style pour les informations importantes - sobre
        self.styles.add(ParagraphStyle(
            name='Important',
            parent=self.styles['Normal'],
            fontSize=11,
            spaceAfter=8,
            spaceBefore=4,
            textColor=self.primary_color,
            fontName='Helvetica-Bold',
            leading=14
        ))

        # Style pour les montants financiers
        self.styles.add(ParagraphStyle(
            name='Financial',
            parent=self.styles['Normal'],
            fontSize=11,
            textColor=self.text_primary,
            fontName='Helvetica-Bold',
            alignment=TA_RIGHT
        ))

        # Style pour le footer
        self.styles.add(ParagraphStyle(
            name='Footer',
            parent=self.styles['Normal'],
            fontSize=8,
            textColor=self.text_light,
            fontName='Helvetica',
            alignment=TA_CENTER,
            leading=10
        ))

        # Style pour les labels de tableau
        self.styles.add(ParagraphStyle(
            name='TableLabel',
            parent=self.styles['Normal'],
            fontSize=10,
            textColor=self.text_secondary,
            fontName='Helvetica-Bold'
        ))

        # Style pour les valeurs de tableau
        self.styles.add(ParagraphStyle(
            name='TableValue',
            parent=self.styles['Normal'],
            fontSize=10,
            textColor=self.text_primary,
            fontName='Helvetica'
        ))
    
    def generate_reservation_pdf(self, reservation):
        """Génère un PDF de confirmation de réservation"""
        buffer = io.BytesIO()
        
        # Configuration du document
        doc = SimpleDocTemplate(
            buffer,
            pagesize=A4,
            rightMargin=72,
            leftMargin=72,
            topMargin=72,
            bottomMargin=18
        )
        
        # Contenu du PDF
        story = []
        
        # En-tête professionnel avec logo
        header_elements = self._create_header()
        for element in header_elements:
            story.append(element)
        story.append(Spacer(1, 25))

        # Titre principal professionnel
        title = Paragraph("Confirmation de Réservation", self.styles['CustomTitle'])
        story.append(title)
        story.append(Spacer(1, 20))

        # Numéro de confirmation professionnel
        confirmation_text = f"""
        <para align="center">
            <font size="12" color="#1A1A1A"><b>Numéro de confirmation:</b></font><br/>
            <font size="16" color="#FF5A5F"><b>{reservation.confirmation_number}</b></font>
        </para>
        """
        story.append(Paragraph(confirmation_text, self.styles['Important']))
        story.append(Spacer(1, 30))
        
        # Informations du client
        story.extend(self._create_guest_info_section(reservation))
        story.append(Spacer(1, 20))

        # Détails de la propriété
        story.extend(self._create_property_section(reservation))
        story.append(Spacer(1, 20))

        # Détails du séjour
        story.extend(self._create_stay_details_section(reservation))
        story.append(Spacer(1, 20))

        # Résumé financier
        story.extend(self._create_financial_summary(reservation))
        story.append(Spacer(1, 30))

        # Informations importantes
        story.extend(self._create_important_info_section())
        story.append(Spacer(1, 20))
        
        # Pied de page professionnel
        footer_elements = self._create_footer()
        for element in footer_elements:
            story.append(element)
        
        # Génération du PDF
        doc.build(story)
        
        # Préparation de la réponse HTTP
        buffer.seek(0)
        response = HttpResponse(buffer.getvalue(), content_type='application/pdf')
        response['Content-Disposition'] = f'attachment; filename="confirmation_reservation_{reservation.confirmation_number}.pdf"'
        
        return response
    
    def _create_header(self):
        """Crée l'en-tête professionnel avec le logo Nestria"""
        header_elements = []

        # Essayer d'ajouter le nouveau logo
        try:
            logo_path = os.path.join(settings.BASE_DIR, 'static', 'images', 'nestria-logo-new.png')
            if os.path.exists(logo_path):
                # Logo centré et bien visible
                logo = Image(logo_path, width=2.5*inch, height=2.5*inch)
                logo.hAlign = 'CENTER'
                header_elements.append(logo)
                header_elements.append(Spacer(1, 10))
            else:
                # Fallback avec texte professionnel
                logo_text = """
                <para align="center">
                    <font size="28" color="#FF5A5F"><b>NESTRIA</b></font><br/>
                    <font size="12" color="#4A4A4A">Travel to Survive</font>
                </para>
                """
                header_elements.append(Paragraph(logo_text, self.styles['Normal']))
                header_elements.append(Spacer(1, 15))
        except Exception:
            # Fallback en cas d'erreur
            logo_text = """
            <para align="center">
                <font size="28" color="#FF5A5F"><b>NESTRIA</b></font><br/>
                <font size="12" color="#4A4A4A">Travel to Survive</font>
            </para>
            """
            header_elements.append(Paragraph(logo_text, self.styles['Normal']))
            header_elements.append(Spacer(1, 15))

        # Ligne de séparation professionnelle
        header_elements.append(ColoredBox(7.5*inch, 1, self.border_color))
        header_elements.append(Spacer(1, 20))

        return header_elements
    
    def _create_guest_info_section(self, reservation):
        """Section informations du client - design professionnel"""
        heading = Paragraph("Informations du Client", self.styles['CustomHeading'])

        guest_data = [
            ['Nom:', f"{reservation.guest_first_name} {reservation.guest_last_name}"],
            ['Email:', reservation.guest_email],
            ['Téléphone:', reservation.guest_phone or 'Non renseigné'],
            ['Date de réservation:', reservation.created_at.strftime('%d/%m/%Y à %H:%M')]
        ]

        table = Table(guest_data, colWidths=[2*inch, 4.5*inch])
        table.setStyle(TableStyle([
            ('FONTNAME', (0, 0), (-1, -1), 'Helvetica'),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('FONTNAME', (0, 0), (0, -1), 'Helvetica-Bold'),
            ('TEXTCOLOR', (0, 0), (0, -1), self.text_secondary),
            ('TEXTCOLOR', (1, 0), (1, -1), self.text_primary),
            ('VALIGN', (0, 0), (-1, -1), 'TOP'),
            ('LEFTPADDING', (0, 0), (-1, -1), 8),
            ('RIGHTPADDING', (0, 0), (-1, -1), 8),
            ('TOPPADDING', (0, 0), (-1, -1), 6),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 6),
            ('LINEBELOW', (0, 0), (-1, -2), 0.5, self.border_color),
            ('LINEBELOW', (0, -1), (-1, -1), 1, self.border_color),
        ]))

        return [heading, table]
    
    def _create_property_section(self, reservation):
        """Section détails de la propriété - design épuré"""
        heading = Paragraph("Détails de la Propriété", self.styles['CustomHeading'])

        property_data = [
            ['Propriété:', reservation.room.title],
            ['Type:', reservation.room.room_type.name],
            ['Adresse:', reservation.room.address or 'Non spécifiée'],
            ['Ville:', f"{reservation.room.city}, {reservation.room.country}"],
            ['Hôte:', f"{reservation.room.host.first_name} {reservation.room.host.last_name}"]
        ]

        table = Table(property_data, colWidths=[2*inch, 4.5*inch])
        table.setStyle(TableStyle([
            ('FONTNAME', (0, 0), (-1, -1), 'Helvetica'),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('FONTNAME', (0, 0), (0, -1), 'Helvetica-Bold'),
            ('TEXTCOLOR', (0, 0), (0, -1), self.text_secondary),
            ('TEXTCOLOR', (1, 0), (1, -1), self.text_primary),
            ('VALIGN', (0, 0), (-1, -1), 'TOP'),
            ('LEFTPADDING', (0, 0), (-1, -1), 8),
            ('RIGHTPADDING', (0, 0), (-1, -1), 8),
            ('TOPPADDING', (0, 0), (-1, -1), 6),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 6),
            ('LINEBELOW', (0, 0), (-1, -2), 0.5, self.border_color),
            ('LINEBELOW', (0, -1), (-1, -1), 1, self.border_color),
        ]))

        return [heading, table]
    
    def _create_stay_details_section(self, reservation):
        """Section détails du séjour - design professionnel"""
        heading = Paragraph("Détails du Séjour", self.styles['CustomHeading'])

        # Calcul du nombre de nuits
        nights = (reservation.check_out - reservation.check_in).days

        # Composition des invités
        guests_parts = []
        if reservation.adults > 0:
            guests_parts.append(f"{reservation.adults} adulte(s)")
        if reservation.children > 0:
            guests_parts.append(f"{reservation.children} enfant(s)")
        if reservation.infants > 0:
            guests_parts.append(f"{reservation.infants} bébé(s)")
        guests_text = ", ".join(guests_parts)

        stay_data = [
            ['Arrivée:', reservation.check_in.strftime('%d/%m/%Y')],
            ['Départ:', reservation.check_out.strftime('%d/%m/%Y')],
            ['Nombre de nuits:', f"{nights} nuit(s)"],
            ['Invités:', guests_text],
            ['Méthode de paiement:', reservation.payment_method.replace('_', ' ').title()]
        ]

        table = Table(stay_data, colWidths=[2*inch, 4.5*inch])
        table.setStyle(TableStyle([
            ('FONTNAME', (0, 0), (-1, -1), 'Helvetica'),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('FONTNAME', (0, 0), (0, -1), 'Helvetica-Bold'),
            ('TEXTCOLOR', (0, 0), (0, -1), self.text_secondary),
            ('TEXTCOLOR', (1, 0), (1, -1), self.text_primary),
            ('VALIGN', (0, 0), (-1, -1), 'TOP'),
            ('LEFTPADDING', (0, 0), (-1, -1), 8),
            ('RIGHTPADDING', (0, 0), (-1, -1), 8),
            ('TOPPADDING', (0, 0), (-1, -1), 6),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 6),
            ('LINEBELOW', (0, 0), (-1, -2), 0.5, self.border_color),
            ('LINEBELOW', (0, -1), (-1, -1), 1, self.border_color),
        ]))

        return [heading, table]

    def _create_financial_summary(self, reservation):
        """Section résumé financier - design professionnel et épuré"""
        heading = Paragraph("Résumé Financier", self.styles['CustomHeading'])

        # Calcul des détails financiers
        nights = (reservation.check_out - reservation.check_in).days
        base_price = reservation.base_price
        cleaning_fee = getattr(reservation.room, 'cleaning_fee', 0)
        service_fee = getattr(reservation.room, 'service_fee', 0)

        financial_data = [
            [f"Prix par nuit × {nights} nuit(s)", f"${base_price:.2f}"],
            ['Frais de ménage', f"${cleaning_fee:.2f}"],
            ['Frais de service', f"${service_fee:.2f}"],
            ['', ''],  # Ligne de séparation
            ['TOTAL', f"${reservation.total_price:.2f}"]
        ]

        table = Table(financial_data, colWidths=[4*inch, 2.5*inch])
        table.setStyle(TableStyle([
            ('FONTNAME', (0, 0), (-1, -2), 'Helvetica'),
            ('FONTNAME', (0, -1), (-1, -1), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, -2), 10),
            ('FONTSIZE', (0, -1), (-1, -1), 12),
            ('ALIGN', (1, 0), (1, -1), 'RIGHT'),
            ('TEXTCOLOR', (0, 0), (-1, -2), self.text_secondary),
            ('TEXTCOLOR', (1, 0), (1, -2), self.text_primary),
            ('TEXTCOLOR', (0, -1), (-1, -1), self.text_primary),
            ('LINEBELOW', (0, -2), (-1, -2), 1, self.border_color),
            ('VALIGN', (0, 0), (-1, -1), 'TOP'),
            ('LEFTPADDING', (0, 0), (-1, -1), 8),
            ('RIGHTPADDING', (0, 0), (-1, -1), 8),
            ('TOPPADDING', (0, 0), (-1, -1), 6),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 6),
            ('BACKGROUND', (0, -1), (-1, -1), self.background_light),
        ]))

        return [heading, table]

    def _create_important_info_section(self):
        """Section informations importantes - design professionnel"""
        heading = Paragraph("Informations Importantes", self.styles['CustomHeading'])

        info_text = """
        <b>Politique d'annulation:</b> Annulation gratuite jusqu'à 48 heures avant l'arrivée.
        Veuillez consulter les conditions spécifiques à cette propriété.<br/><br/>

        <b>Check-in:</b> L'heure d'arrivée standard est 15h00.
        Contactez votre hôte 24h avant pour organiser l'arrivée.<br/><br/>

        <b>Check-out:</b> L'heure de départ standard est 11h00.<br/><br/>

        <b>Contact d'urgence:</b> En cas de problème, contactez le support Nestria au +33 1 23 45 67 89.<br/><br/>

        <b>Documents requis:</b> Pièce d'identité valide obligatoire lors de l'arrivée.<br/><br/>

        <b>Note importante:</b> Présentez cette confirmation à votre hôte lors de l'arrivée.
        """

        info_paragraph = Paragraph(info_text, self.styles['CustomNormal'])

        return [heading, info_paragraph]

    def _create_footer(self):
        """Crée le pied de page professionnel et épuré"""
        footer_elements = []

        # Ligne de séparation
        footer_elements.append(Spacer(1, 20))
        footer_elements.append(ColoredBox(7.5*inch, 1, self.border_color))
        footer_elements.append(Spacer(1, 15))

        # Informations de contact et légales
        footer_text = """
        <para align="center">
            <font size="9" color="#6B7280">
                <b>NESTRIA</b> - Travel to Survive<br/>
                <EMAIL> | www.nestria.com | +33 1 23 45 67 89<br/>
            </font>
            <font size="8" color="#6B7280">
                Document généré le {date}<br/>
                © 2025 Nestria. Tous droits réservés.
            </font>
        </para>
        """.format(date=datetime.now().strftime('%d/%m/%Y à %H:%M'))

        footer_paragraph = Paragraph(footer_text, self.styles['Footer'])
        footer_elements.append(footer_paragraph)

        return footer_elements


def generate_reservation_pdf(reservation):
    """Fonction utilitaire pour générer un PDF de réservation"""
    generator = ReservationPDFGenerator()
    return generator.generate_reservation_pdf(reservation)
