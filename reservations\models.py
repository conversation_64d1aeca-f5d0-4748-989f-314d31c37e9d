from django.db import models
from django.contrib.auth.models import User
from django.core.validators import MinValueValidator
from rooms.models import Room


class Reservation(models.Model):
    """Booking/Reservation model"""

    STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('confirmed', 'Confirmed'),
        ('cancelled', 'Cancelled'),
        ('completed', 'Completed'),
    ]

    # Basic Information
    room = models.ForeignKey(Room, on_delete=models.CASCADE, related_name='reservations')
    guest = models.ForeignKey(User, on_delete=models.CASCADE, related_name='reservations')

    # Dates
    check_in = models.DateField()
    check_out = models.DateField()

    # Guest Details
    adults = models.PositiveIntegerField(validators=[MinValueValidator(1)])
    children = models.PositiveIntegerField(default=0)
    infants = models.PositiveIntegerField(default=0)

    # Guest Contact Information
    guest_first_name = models.CharField(max_length=100)
    guest_last_name = models.CharField(max_length=100)
    guest_email = models.EmailField()
    guest_phone = models.CharField(max_length=20, blank=True)

    # Pricing
    nights = models.PositiveIntegerField(default=1)
    price_per_night = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    base_price = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    cleaning_fee = models.DecimalField(max_digits=8, decimal_places=2, default=0)
    service_fee = models.DecimalField(max_digits=8, decimal_places=2, default=0)
    total_price = models.DecimalField(max_digits=10, decimal_places=2)

    # Payment Information
    payment_method = models.CharField(max_length=20, default='card')
    confirmation_number = models.CharField(max_length=20, unique=True, blank=True)

    # Status and Timestamps
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='confirmed')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    # Special Requests
    special_requests = models.TextField(blank=True)

    class Meta:
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.guest.username} - {self.room.title} ({self.check_in} to {self.check_out})"

    @property
    def total_guests(self):
        """Calculate total number of guests"""
        return self.adults + self.children + self.infants

    def clean(self):
        """Validate reservation data"""
        from django.core.exceptions import ValidationError

        if self.check_out <= self.check_in:
            raise ValidationError("Check-out date must be after check-in date.")

        if self.total_guests > self.room.max_guests:
            raise ValidationError(f"Total guests ({self.total_guests}) exceeds room capacity ({self.room.max_guests}).")

    def save(self, *args, **kwargs):
        # Calculate nights
        if self.check_in and self.check_out:
            self.nights = (self.check_out - self.check_in).days

        # Set price per night from room if not set
        if not self.price_per_night and self.room:
            self.price_per_night = self.room.price_per_night

        # Set fees from room if not set
        if not self.cleaning_fee and self.room:
            self.cleaning_fee = self.room.cleaning_fee
        if not self.service_fee and self.room:
            self.service_fee = self.room.service_fee

        # Calculate base price and total price
        if self.nights and self.price_per_night:
            self.base_price = self.nights * self.price_per_night
            self.total_price = self.base_price + self.cleaning_fee + self.service_fee

        # Generate confirmation number if not set
        if not self.confirmation_number:
            import uuid
            self.confirmation_number = str(uuid.uuid4())[:8].upper()

        super().save(*args, **kwargs)

    @property
    def can_cancel(self):
        """Check if reservation can be cancelled (48h before check-in)"""
        from datetime import datetime, timedelta
        from django.utils import timezone

        if self.status != 'confirmed':
            return False

        # Check if check-in is more than 48 hours away
        now = timezone.now()
        check_in_datetime = timezone.make_aware(datetime.combine(self.check_in, datetime.min.time()))
        time_until_checkin = check_in_datetime - now

        return time_until_checkin.total_seconds() > 48 * 3600  # 48 hours in seconds
