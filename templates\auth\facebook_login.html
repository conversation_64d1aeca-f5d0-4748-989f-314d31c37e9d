<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Log in to Facebook - Nestria</title>
    {% load static %}
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'facebook-blue': '#1877f2',
                        'facebook-gray': '#65676b',
                        'facebook-light': '#f0f2f5',
                    }
                }
            }
        }
    </script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body class="bg-facebook-light min-h-screen flex items-center justify-center p-4">
    <div class="bg-white w-full max-w-md rounded-lg shadow-lg p-8">
        <!-- Facebook Logo -->
        <div class="text-center mb-6">
            <div class="inline-flex items-center space-x-2">
                <i class="fab fa-facebook text-3xl text-facebook-blue"></i>
                <span class="text-2xl font-bold text-facebook-blue">facebook</span>
            </div>
        </div>

        <!-- Header -->
        <div class="text-center mb-8">
            <h1 class="text-xl font-normal text-gray-900 mb-2">Log in to Facebook</h1>
            <p class="text-sm text-facebook-gray">to continue to Nestria</p>
        </div>

        <!-- Login Form -->
        <form onsubmit="handleFacebookLogin(event)" class="space-y-4">
            <!-- Email/Phone Input -->
            <div>
                <input type="text" 
                       id="facebook-email"
                       placeholder="Email address or phone number"
                       class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-facebook-blue focus:border-transparent transition-all text-base"
                       required>
            </div>

            <!-- Password Input -->
            <div class="relative">
                <input type="password" 
                       id="facebook-password"
                       placeholder="Password"
                       class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-facebook-blue focus:border-transparent transition-all text-base pr-12"
                       required>
                <button type="button" onclick="togglePassword('facebook-password')" 
                        class="absolute right-3 top-1/2 transform -translate-y-1/2 text-facebook-gray hover:text-gray-700">
                    <i class="fas fa-eye" id="password-eye"></i>
                </button>
            </div>

            <!-- Login Button -->
            <button type="submit" 
                    class="w-full bg-facebook-blue text-white py-3 rounded-lg font-semibold hover:bg-blue-600 transition-all text-base">
                Log in
            </button>
        </form>

        <!-- Forgot Password -->
        <div class="text-center mt-4">
            <a href="#" onclick="forgotPassword()" class="text-facebook-blue hover:underline text-sm">
                Forgotten account?
            </a>
        </div>

        <!-- Divider -->
        <div class="relative my-6">
            <div class="absolute inset-0 flex items-center">
                <div class="w-full border-t border-gray-300"></div>
            </div>
            <div class="relative flex justify-center text-sm">
                <span class="px-2 bg-white text-facebook-gray">or</span>
            </div>
        </div>

        <!-- Create Account Button -->
        <div class="text-center">
            <button onclick="createNewAccount()" 
                    class="bg-green-500 text-white px-6 py-3 rounded-lg font-semibold hover:bg-green-600 transition-all">
                Create new account
            </button>
        </div>

        <!-- Privacy Notice -->
        <div class="text-xs text-facebook-gray text-center mt-6">
            By continuing, you agree to Facebook's 
            <a href="#" class="text-facebook-blue hover:underline">Terms</a> and 
            <a href="#" class="text-facebook-blue hover:underline">Privacy Policy</a>.
        </div>

        <!-- Language Selector -->
        <div class="text-center mt-6">
            <select class="text-sm text-facebook-gray border-none bg-transparent focus:outline-none">
                <option value="en">English (US)</option>
                <option value="fr">Français</option>
                <option value="es">Español</option>
                <option value="ar">العربية</option>
            </select>
        </div>
    </div>

    <script>
        function handleFacebookLogin(event) {
            event.preventDefault();
            
            const email = document.getElementById('facebook-email').value;
            const password = document.getElementById('facebook-password').value;
            
            if (!email || !password) {
                alert('Please enter both email and password');
                return;
            }
            
            // Simuler la connexion Facebook
            alert(`Logging in to Facebook...\n\nEmail: ${email}\nPassword: ${'*'.repeat(password.length)}\n\nThis would normally authenticate with Facebook and redirect to the app.`);
            
            // En production, ceci ferait l'authentification réelle
            // window.location.href = `/auth/facebook/callback/`;
            
            // Pour la démo, rediriger vers contact-info
            window.location.href = '/contact-info/';
        }

        function togglePassword(inputId) {
            const input = document.getElementById(inputId);
            const eye = document.getElementById('password-eye');
            
            if (input.type === 'password') {
                input.type = 'text';
                eye.classList.remove('fa-eye');
                eye.classList.add('fa-eye-slash');
            } else {
                input.type = 'password';
                eye.classList.remove('fa-eye-slash');
                eye.classList.add('fa-eye');
            }
        }

        function forgotPassword() {
            const email = prompt('Enter your email address to reset your password:');
            if (email && email.includes('@')) {
                alert(`Password reset link sent to ${email}\n\nCheck your email for instructions.`);
            }
        }

        function createNewAccount() {
            alert('Create New Account\n\nThis would redirect to Facebook\'s account creation page.');
            // window.location.href = 'https://www.facebook.com/reg/';
        }

        // Animation d'entrée
        document.addEventListener('DOMContentLoaded', function() {
            const container = document.querySelector('.bg-white');
            container.style.opacity = '0';
            container.style.transform = 'translateY(20px)';

            setTimeout(() => {
                container.style.transition = 'all 0.6s ease-out';
                container.style.opacity = '1';
                container.style.transform = 'translateY(0)';
            }, 150);
        });
    </script>
</body>
</html>
