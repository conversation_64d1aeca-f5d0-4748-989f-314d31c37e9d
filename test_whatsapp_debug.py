#!/usr/bin/env python
"""
Test debug pour l'envoi WhatsApp
"""

import os
import sys
import django

# Configuration Django
sys.path.append('.')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'airbnb_clone.settings')
django.setup()

from core.services.callmebot_service import send_whatsapp_verification_callmebot

def test_whatsapp_debug():
    """Tester l'envoi WhatsApp en mode debug"""
    
    print("🔍 TEST DEBUG WHATSAPP")
    print("=" * 50)
    
    # Numéro de test
    test_phone = "+212603999557"
    test_code = "123456"
    
    print(f"📱 Numéro de test: {test_phone}")
    print(f"🔐 Code de test: {test_code}")
    
    # Variables d'environnement
    api_key = os.getenv('CALLMEBOT_API_KEY', '')
    print(f"🔑 API Key: {api_key}")
    print(f"🔑 API Key valide: {api_key not in ['', 'YOUR_REAL_API_KEY_HERE', 'test_key_pending']}")
    
    # Test d'envoi
    print("\n📤 Test d'envoi...")
    try:
        result = send_whatsapp_verification_callmebot(test_phone, test_code)
        print(f"✅ Résultat: {result}")
        
        if result:
            print("🎉 Envoi réussi !")
        else:
            print("❌ Envoi échoué !")
            
    except Exception as e:
        print(f"💥 Erreur: {str(e)}")
    
    print("\n🔧 DIAGNOSTIC:")
    print("1. Vérifiez que l'API key CallMeBot est correcte")
    print("2. Vérifiez que le numéro est autorisé sur CallMeBot")
    print("3. Vérifiez la connexion internet")
    print("4. Consultez les logs du serveur")

if __name__ == '__main__':
    test_whatsapp_debug()
