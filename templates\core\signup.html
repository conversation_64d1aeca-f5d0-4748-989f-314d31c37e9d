{% extends 'base.html' %}
{% load static %}

{% block title %}Sign Up - Nestria{% endblock %}

{% block content %}
<div class="min-h-screen flex items-center justify-center bg-gradient-to-br from-primary-50 to-secondary-50 dark:from-dark-900 dark:to-dark-800 py-12 px-4 sm:px-6 lg:px-8 transition-colors duration-300">
    <div class="max-w-2xl w-full space-y-8">
        <div class="text-center">
            <!-- Logo Nestria -->
            <div class="mx-auto mb-6">
                <img src="{% static 'images/nestria-logo.png' %}"
                     alt="Nestria Logo"
                     class="w-20 h-20 object-contain mx-auto nestria-logo-img">
            </div>

            <h2 class="text-4xl font-display font-bold text-gray-900 dark:text-gray-100 mb-2">
                Join
                <span class="text-gray-900 dark:text-white">
                    NESTRIA
                </span>
            </h2>
            <p class="text-gray-600 dark:text-gray-400 mb-8">
                Start your adventure today
            </p>
            <p class="text-sm text-gray-600 dark:text-gray-400">
                Already have an account?
                <a href="{% url 'core:login' %}" class="font-medium text-primary-600 hover:text-primary-500 dark:text-primary-400 dark:hover:text-primary-300 transition-colors">
                    Sign in
                </a>
            </p>
        </div>
        
        <form method="post" class="space-y-8 bg-white dark:bg-gray-800 p-8 rounded-xl shadow-lg">
            {% csrf_token %}
            
            {% if form.errors %}
                <div class="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <i class="fas fa-exclamation-circle text-red-400"></i>
                        </div>
                        <div class="ml-3">
                            <h3 class="text-sm font-medium text-red-800 dark:text-red-200">
                                Please correct the following errors:
                            </h3>
                            <div class="mt-2 text-sm text-red-700 dark:text-red-300">
                                <ul class="list-disc list-inside space-y-1">
                                    {% for field, errors in form.errors.items %}
                                        {% for error in errors %}
                                            <li>{{ error }}</li>
                                        {% endfor %}
                                    {% endfor %}
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            {% endif %}

            <!-- Basic Information -->
            <div class="space-y-4">
                <h3 class="text-lg font-medium text-gray-900 dark:text-white border-b border-gray-200 dark:border-gray-700 pb-2">
                    Basic Information
                </h3>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label for="{{ form.first_name.id_for_label }}" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            First Name *
                        </label>
                        {{ form.first_name }}
                    </div>
                    <div>
                        <label for="{{ form.last_name.id_for_label }}" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Last Name *
                        </label>
                        {{ form.last_name }}
                    </div>
                </div>

                <div>
                    <label for="{{ form.username.id_for_label }}" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Username *
                    </label>
                    {{ form.username }}
                </div>

                <div>
                    <label for="{{ form.email.id_for_label }}" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Email Address *
                    </label>
                    {{ form.email }}
                </div>
            </div>

            <!-- Personal Details -->
            <div class="space-y-4">
                <h3 class="text-lg font-medium text-gray-900 dark:text-white border-b border-gray-200 dark:border-gray-700 pb-2">
                    Personal Details <span class="text-sm font-normal text-gray-500">(Optional)</span>
                </h3>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label for="{{ form.phone_number.id_for_label }}" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Phone Number
                        </label>
                        {{ form.phone_number }}
                    </div>
                    <div>
                        <label for="{{ form.age.id_for_label }}" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Age * <span class="text-xs text-gray-500">(18-110 years)</span>
                        </label>
                        {{ form.age }}
                        {% if form.age.errors %}
                            <div class="mt-1 text-sm text-red-600">
                                {% for error in form.age.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                        <label for="{{ form.gender.id_for_label }}" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Gender
                        </label>
                        {{ form.gender }}
                    </div>
                    <div>
                        <label for="{{ form.city.id_for_label }}" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            City
                        </label>
                        {{ form.city }}
                    </div>
                    <div>
                        <label for="{{ form.country.id_for_label }}" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Country
                        </label>
                        {{ form.country }}
                    </div>
                </div>
            </div>

            <!-- Password -->
            <div class="space-y-4">
                <h3 class="text-lg font-medium text-gray-900 dark:text-white border-b border-gray-200 dark:border-gray-700 pb-2">
                    Security
                </h3>
                
                <div>
                    <label for="{{ form.password1.id_for_label }}" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Password *
                    </label>
                    {{ form.password1 }}
                </div>

                <div>
                    <label for="{{ form.password2.id_for_label }}" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Confirm Password *
                    </label>
                    {{ form.password2 }}
                </div>
            </div>

            <!-- Preferences -->
            <div class="space-y-4">
                <h3 class="text-lg font-medium text-gray-900 dark:text-white border-b border-gray-200 dark:border-gray-700 pb-2">
                    Communication Preferences
                </h3>
                
                <div class="space-y-3">
                    <div class="flex items-center">
                        {{ form.email_notifications }}
                        <label for="{{ form.email_notifications.id_for_label }}" class="ml-2 text-sm text-gray-700 dark:text-gray-300">
                            Receive booking confirmations and important updates
                        </label>
                    </div>
                    <div class="flex items-center">
                        {{ form.marketing_emails }}
                        <label for="{{ form.marketing_emails.id_for_label }}" class="ml-2 text-sm text-gray-700 dark:text-gray-300">
                            Receive special offers and travel inspiration
                        </label>
                    </div>
                </div>
            </div>

            <div>
                <button type="submit" class="group relative w-full flex justify-center py-3 px-4 border border-transparent text-sm font-medium rounded-lg text-white bg-gradient-to-r from-primary-600 to-secondary-600 hover:from-primary-700 hover:to-secondary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-all duration-200 transform hover:scale-105">
                    <span class="absolute left-0 inset-y-0 flex items-center pl-3">
                        <i class="fas fa-user-plus text-primary-300 group-hover:text-primary-200 transition-colors duration-200"></i>
                    </span>
                    Create Account
                </button>
            </div>
        </form>
    </div>
</div>
{% endblock %}
