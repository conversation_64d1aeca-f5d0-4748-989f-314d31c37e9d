<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Community Commitment - Nestria</title>
    {% load static %}
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'nestria-red': '#FF5A5F',
                        'airbnb-pink': '#E91E63',
                    }
                }
            }
        }
    </script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body class="bg-white min-h-screen flex items-center justify-center p-4">
    <div class="bg-white w-full max-w-md p-8 relative">
        <!-- Airbnb Logo -->
        <div class="flex justify-center mb-8">
            <svg width="64" height="64" viewBox="0 0 64 64" class="text-airbnb-pink">
                <path fill="currentColor" d="M32 0C14.3 0 0 14.3 0 32s14.3 32 32 32 32-14.3 32-32S49.7 0 32 0zm0 58C17.7 58 6 46.3 6 32S17.7 6 32 6s26 11.7 26 26-11.7 26-26 26z"/>
                <path fill="currentColor" d="M32 16c-8.8 0-16 7.2-16 16s7.2 16 16 16 16-7.2 16-16-7.2-16-16-16zm0 26c-5.5 0-10-4.5-10-10s4.5-10 10-10 10 4.5 10 10-4.5 10-10 10z"/>
            </svg>
        </div>

        <!-- Header -->
        <div class="text-center mb-8">
            <p class="text-sm text-gray-600 mb-4">Our community commitment</p>
            <h1 class="text-2xl font-semibold text-gray-900 leading-tight">
                Airbnb is a community where anyone can belong
            </h1>
        </div>

        <!-- Content -->
        <div class="space-y-8">
            <p class="text-gray-700 text-base leading-relaxed">
                To ensure this, we're asking you to commit to the following:
            </p>

            <div class="space-y-6">
                <p class="text-gray-800 text-base leading-relaxed">
                    I agree to treat everyone in the Airbnb community—regardless of their
                    race, religion, national origin, ethnicity, skin color, disability, sex, gender
                    identity, sexual orientation or age—with respect, and without
                    judgment or bias.
                </p>
            </div>

            <!-- Learn More Link -->
            <div>
                <a href="#" class="text-gray-700 underline hover:text-gray-900 text-sm font-medium flex items-center">
                    Learn more
                    <i class="fas fa-chevron-right ml-1 text-xs"></i>
                </a>
            </div>

            <!-- Action Buttons -->
            <form method="post" class="space-y-4">
                {% csrf_token %}

                <!-- Agree Button -->
                <button type="submit" name="action" value="agree" class="w-full bg-airbnb-pink text-white py-4 rounded-lg font-semibold hover:bg-pink-600 transition-all text-base">
                    Agree and continue
                </button>

                <!-- Decline Button -->
                <button type="submit" name="action" value="decline" class="w-full bg-white text-gray-700 py-4 rounded-lg font-semibold border border-gray-300 hover:border-gray-400 transition-all text-base">
                    Decline
                </button>
            </form>
        </div>
    </div>

    <script>
        // Gestion des boutons
        const form = document.querySelector('form');
        
        form.addEventListener('submit', function(e) {
            const action = e.submitter.value;
            
            if (action === 'decline') {
                const confirmed = confirm('Are you sure you want to decline? You will not be able to continue with registration.');
                if (!confirmed) {
                    e.preventDefault();
                }
            }
        });

        // Animation d'entrée
        document.addEventListener('DOMContentLoaded', function() {
            const container = document.querySelector('.bg-white');
            container.style.opacity = '0';
            container.style.transform = 'translateY(20px)';

            setTimeout(() => {
                container.style.transition = 'all 0.6s ease-out';
                container.style.opacity = '1';
                container.style.transform = 'translateY(0)';
            }, 150);
        });
    </script>
</body>
</html>
