#!/usr/bin/env python
"""
Script de test pour le service WhatsApp
"""

import os
import sys
import django

# Configuration Django
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'airbnb_clone.settings')
django.setup()

from core.services.whatsapp_service import whatsapp_service

def test_whatsapp_service():
    """Test du service WhatsApp"""
    
    print("🧪 Test du Service WhatsApp Nestria")
    print("=" * 50)
    
    # Test 1: Envoi d'un code de vérification
    print("\n📱 Test 1: Envoi d'un code de vérification")
    phone_number = "+212603999557"
    verification_code = "123456"
    
    result = whatsapp_service.send_verification_code(phone_number, verification_code)
    
    print(f"Numéro: {phone_number}")
    print(f"Code: {verification_code}")
    print(f"Résultat: {result}")
    
    if result['success']:
        print("✅ Envoi réussi!")
        
        # Test 2: Vérification du statut
        print("\n📊 Test 2: Vérification du statut")
        message_sid = result.get('message_sid')
        if message_sid:
            status = whatsapp_service.get_delivery_status(message_sid)
            print(f"Statut: {status}")
        
    else:
        print("❌ Échec de l'envoi")
        print(f"Erreur: {result.get('error', 'Erreur inconnue')}")
    
    # Test 3: Nettoyage de numéro
    print("\n🧹 Test 3: Nettoyage de numéros")
    test_numbers = [
        "+212 603 999 557",
        "212603999557",
        "+212-603-999-557",
        "(212) 603 999 557"
    ]
    
    for number in test_numbers:
        cleaned = whatsapp_service._clean_phone_number(number)
        print(f"{number} → {cleaned}")
    
    print("\n" + "=" * 50)
    print("🎉 Tests terminés!")

if __name__ == "__main__":
    test_whatsapp_service()
