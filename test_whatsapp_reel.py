#!/usr/bin/env python
"""
Test rapide pour vérifier l'envoi WhatsApp réel
"""

import os
import sys
import django
from dotenv import load_dotenv

# Charger les variables d'environnement
load_dotenv()

# Configuration Django
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'airbnb_clone.settings')
django.setup()

from core.services.whatsapp_service import whatsapp_service

def test_real_whatsapp():
    """Test d'envoi WhatsApp réel"""
    
    print("🧪 Test WhatsApp Réel - Nestria")
    print("=" * 50)
    
    # Vérifier la configuration
    account_sid = os.getenv('TWILIO_ACCOUNT_SID')
    auth_token = os.getenv('TWILIO_AUTH_TOKEN')
    whatsapp_number = os.getenv('TWILIO_WHATSAPP_NUMBER')
    
    print(f"Account SID: {account_sid[:10]}..." if account_sid else "❌ Account SID manquant")
    print(f"Auth Token: {'✅ Configuré' if auth_token else '❌ Auth Token manquant'}")
    print(f"WhatsApp Number: {whatsapp_number}")
    
    if not account_sid or not auth_token:
        print("\n❌ Configuration Twilio incomplète!")
        print("Veuillez configurer vos identifiants dans le fichier .env")
        return
    
    # Test d'envoi
    print(f"\n📱 Test d'envoi vers votre numéro...")
    phone_number = input("Entrez votre numéro WhatsApp (format: +************): ")
    
    if not phone_number:
        phone_number = "+************"  # Numéro par défaut
    
    verification_code = "123456"
    
    print(f"\n🚀 Envoi du code {verification_code} vers {phone_number}...")
    
    result = whatsapp_service.send_verification_code(phone_number, verification_code)
    
    if result['success']:
        print("✅ Message envoyé avec succès!")
        print(f"📱 Message SID: {result.get('message_sid')}")
        print(f"📊 Statut: {result.get('status')}")
        print("\n🎉 Vérifiez votre WhatsApp, vous devriez recevoir le code!")
    else:
        print("❌ Échec de l'envoi")
        print(f"Erreur: {result.get('error')}")
        if 'error_code' in result:
            print(f"Code d'erreur: {result['error_code']}")

if __name__ == "__main__":
    test_real_whatsapp()
