"""
Service WhatsApp avec pywhatkit
Utilise WhatsApp Web pour envoyer des messages
"""

import logging
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)


class PyWhatsKitService:
    """Service WhatsApp utilisant pywhatkit"""
    
    def __init__(self):
        self.enabled = False  # Désactivé par défaut car nécessite configuration
        
    def send_verification_code(self, phone_number, verification_code):
        """
        Envoie un code de vérification via pywhatkit
        
        Args:
            phone_number (str): Numéro de téléphone (+212603999557)
            verification_code (str): Code à 6 chiffres
            
        Returns:
            dict: Résultat de l'envoi
        """
        
        if not self.enabled:
            return self._simulate_whatsapp_send(phone_number, verification_code)
        
        try:
            # Import pywhatkit seulement si activé
            import pywhatkit as pwk
            
            # Nettoyer le numéro
            clean_phone = self._clean_phone_number(phone_number)
            
            # Message WhatsApp
            message = f"🏠 Nestria Verification\n\nYour verification code is: {verification_code}\n\nThis code will expire in 5 minutes.\nDo not share this code with anyone.\n\nWelcome to Nestria! 🌟"
            
            # Programmer l'envoi dans 1 minute
            now = datetime.now()
            send_time = now + timedelta(minutes=1)
            
            # Envoyer le message
            pwk.sendwhatmsg(clean_phone, message, send_time.hour, send_time.minute)
            
            logger.info(f"✅ WhatsApp pywhatkit message scheduled for {clean_phone}")
            
            return {
                'success': True,
                'message_id': f'pywhatkit_{verification_code}',
                'status': 'scheduled',
                'method': 'pywhatkit'
            }
            
        except ImportError:
            logger.error("❌ pywhatkit not installed. Install with: pip install pywhatkit")
            return self._simulate_whatsapp_send(phone_number, verification_code)
            
        except Exception as e:
            logger.error(f"❌ Unexpected error sending WhatsApp pywhatkit to {clean_phone}: {str(e)}")
            return self._simulate_whatsapp_send(phone_number, verification_code)
    
    def _simulate_whatsapp_send(self, phone_number, verification_code):
        """Simulation si pas configuré"""
        
        print("\n" + "🟡" * 50)
        print("📱 WHATSAPP PYWHATKIT SIMULATION")
        print("🟡" * 50)
        print(f"📞 TO: {phone_number}")
        print(f"🔐 CODE: {verification_code}")
        print(f"💬 MESSAGE:")
        print(f"   🏠 Nestria Verification")
        print(f"   ")
        print(f"   Your verification code is: {verification_code}")
        print(f"   ")
        print(f"   This code will expire in 5 minutes.")
        print(f"   Do not share this code with anyone.")
        print(f"   ")
        print(f"   Welcome to Nestria! 🌟")
        print("🟡" * 50)
        print("⚠️  POUR RECEVOIR SUR WHATSAPP RÉEL (PYWHATKIT):")
        print("1. Installez: pip install pywhatkit")
        print("2. Activez le service dans le code")
        print("3. Connectez-vous à WhatsApp Web")
        print("🟡" * 50 + "\n")
        
        return {
            'success': True,
            'message_id': f'pywhatkit_simulation_{verification_code}',
            'status': 'simulated',
            'method': 'pywhatkit_simulation'
        }
    
    def _clean_phone_number(self, phone_number):
        """Nettoie le numéro de téléphone"""
        if not phone_number:
            return None
            
        clean = phone_number.strip().replace(' ', '').replace('-', '').replace('(', '').replace(')', '')
        
        if not clean.startswith('+'):
            clean = '+' + clean
            
        return clean


# Instance globale
pywhatkit_service = PyWhatsKitService()


def send_whatsapp_verification_pywhatkit(phone_number, verification_code):
    """
    Fonction helper pour envoyer via pywhatkit
    """
    result = pywhatkit_service.send_verification_code(phone_number, verification_code)
    return result.get('success', False)
