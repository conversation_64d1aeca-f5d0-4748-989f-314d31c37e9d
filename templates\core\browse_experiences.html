{% extends 'base.html' %}
{% load static %}

{% block title %}Browse All Experiences - Nestria{% endblock %}

{% block content %}
<div class="min-h-screen bg-gray-50 dark:bg-gray-900 py-8">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="text-center mb-12">
            <h1 class="text-4xl font-bold text-gray-900 dark:text-white mb-4">
                All Experiences
            </h1>
            <p class="text-xl text-gray-600 dark:text-gray-400 max-w-3xl mx-auto">
                Discover thousands of unique experiences hosted by local experts around the world
            </p>
        </div>

        <!-- Search and Filters -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 mb-8">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div>
                    <input type="text" 
                           placeholder="Search experiences..." 
                           class="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white">
                </div>
                <div>
                    <select class="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white">
                        <option>All Categories</option>
                        <option>Food & Drink</option>
                        <option>Adventure</option>
                        <option>Arts & Culture</option>
                        <option>Entertainment</option>
                    </select>
                </div>
                <div>
                    <select class="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white">
                        <option>Any Price</option>
                        <option>Under $50</option>
                        <option>$50 - $100</option>
                        <option>$100 - $200</option>
                        <option>Over $200</option>
                    </select>
                </div>
                <div>
                    <button class="w-full bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors">
                        <i class="fas fa-search mr-2"></i>
                        Search
                    </button>
                </div>
            </div>
        </div>

        <!-- Results Summary -->
        <div class="flex items-center justify-between mb-6">
            <p class="text-gray-600 dark:text-gray-400">
                Showing <span class="font-semibold">1-24</span> of <span class="font-semibold">156</span> experiences
            </p>
            <div class="flex items-center space-x-4">
                <span class="text-sm text-gray-600 dark:text-gray-400">Sort by:</span>
                <select class="px-3 py-1 border border-gray-300 dark:border-gray-600 rounded-lg text-sm dark:bg-gray-700 dark:text-white">
                    <option>Most Popular</option>
                    <option>Price: Low to High</option>
                    <option>Price: High to Low</option>
                    <option>Newest</option>
                    <option>Best Rated</option>
                </select>
            </div>
        </div>

        <!-- Experiences Grid -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 mb-12">
            <!-- Experience Card 1 -->
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm overflow-hidden hover:shadow-md transition-shadow cursor-pointer">
                <div class="h-48 bg-gradient-to-r from-blue-400 to-purple-500 relative">
                    <div class="absolute top-3 left-3 bg-white bg-opacity-90 px-2 py-1 rounded-full text-xs font-medium">
                        ⭐ 4.9 (127)
                    </div>
                    <div class="absolute top-3 right-3 bg-red-500 text-white px-2 py-1 rounded-full text-xs">
                        Popular
                    </div>
                </div>
                <div class="p-4">
                    <div class="flex items-center mb-2">
                        <div class="w-6 h-6 bg-gray-300 rounded-full mr-2"></div>
                        <div>
                            <span class="text-sm text-gray-600 dark:text-gray-400">Chef Maria Rossi</span>
                            <div class="text-xs text-gray-500">📞 +39 055 987 6543</div>
                        </div>
                    </div>
                    <h3 class="font-semibold text-gray-900 dark:text-white mb-2">Italian Cooking Class</h3>
                    <p class="text-gray-600 dark:text-gray-400 text-sm mb-2">Learn authentic pasta making in Florence</p>
                    <p class="text-xs text-gray-500 mb-3">📧 <EMAIL></p>
                    <div class="flex items-center justify-between">
                        <span class="text-lg font-bold text-gray-900 dark:text-white">$75</span>
                        <button onclick="bookExperience('Italian Cooking Class', '$75', '<EMAIL>')" class="bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded text-sm">
                            Book
                        </button>
                    </div>
                </div>
            </div>

            <!-- Experience Card 2 -->
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm overflow-hidden hover:shadow-md transition-shadow cursor-pointer">
                <div class="h-48 bg-gradient-to-r from-green-400 to-blue-500 relative">
                    <div class="absolute top-3 left-3 bg-white bg-opacity-90 px-2 py-1 rounded-full text-xs font-medium">
                        ⭐ 4.7 (89)
                    </div>
                </div>
                <div class="p-4">
                    <div class="flex items-center mb-2">
                        <div class="w-6 h-6 bg-gray-300 rounded-full mr-2"></div>
                        <div>
                            <span class="text-sm text-gray-600 dark:text-gray-400">Guide Jean-Pierre</span>
                            <div class="text-xs text-gray-500">📞 +33 1 5678 9012</div>
                        </div>
                    </div>
                    <h3 class="font-semibold text-gray-900 dark:text-white mb-2">Paris Walking Tour</h3>
                    <p class="text-gray-600 dark:text-gray-400 text-sm mb-2">Hidden gems and local stories</p>
                    <p class="text-xs text-gray-500 mb-3">📧 <EMAIL></p>
                    <div class="flex items-center justify-between">
                        <span class="text-lg font-bold text-gray-900 dark:text-white">$45</span>
                        <button onclick="bookExperience('Paris Walking Tour', '$45', '<EMAIL>')" class="bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded text-sm">
                            Book
                        </button>
                    </div>
                </div>
            </div>

            <!-- Experience Card 3 -->
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm overflow-hidden hover:shadow-md transition-shadow cursor-pointer">
                <div class="h-48 bg-gradient-to-r from-purple-400 to-pink-500 relative">
                    <div class="absolute top-3 left-3 bg-white bg-opacity-90 px-2 py-1 rounded-full text-xs font-medium">
                        ⭐ 4.8 (156)
                    </div>
                    <div class="absolute top-3 right-3 bg-green-500 text-white px-2 py-1 rounded-full text-xs">
                        New
                    </div>
                </div>
                <div class="p-4">
                    <div class="flex items-center mb-2">
                        <div class="w-6 h-6 bg-gray-300 rounded-full mr-2"></div>
                        <span class="text-sm text-gray-600 dark:text-gray-400">Sophie</span>
                    </div>
                    <h3 class="font-semibold text-gray-900 dark:text-white mb-2">Watercolor Workshop</h3>
                    <p class="text-gray-600 dark:text-gray-400 text-sm mb-3">Create your own masterpiece</p>
                    <div class="flex items-center justify-between">
                        <span class="text-lg font-bold text-gray-900 dark:text-white">$60</span>
                        <button onclick="bookExperience('Watercolor Workshop', '$60')" class="bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded text-sm">
                            Book
                        </button>
                    </div>
                </div>
            </div>

            <!-- Experience Card 4 -->
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm overflow-hidden hover:shadow-md transition-shadow cursor-pointer">
                <div class="h-48 bg-gradient-to-r from-yellow-400 to-orange-500 relative">
                    <div class="absolute top-3 left-3 bg-white bg-opacity-90 px-2 py-1 rounded-full text-xs font-medium">
                        ⭐ 4.6 (73)
                    </div>
                </div>
                <div class="p-4">
                    <div class="flex items-center mb-2">
                        <div class="w-6 h-6 bg-gray-300 rounded-full mr-2"></div>
                        <span class="text-sm text-gray-600 dark:text-gray-400">Carlos</span>
                    </div>
                    <h3 class="font-semibold text-gray-900 dark:text-white mb-2">Flamenco Dance</h3>
                    <p class="text-gray-600 dark:text-gray-400 text-sm mb-3">Learn traditional Spanish dance</p>
                    <div class="flex items-center justify-between">
                        <span class="text-lg font-bold text-gray-900 dark:text-white">$55</span>
                        <button onclick="bookExperience('Flamenco Dance', '$55')" class="bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded text-sm">
                            Book
                        </button>
                    </div>
                </div>
            </div>

            <!-- More cards... -->
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm overflow-hidden hover:shadow-md transition-shadow cursor-pointer">
                <div class="h-48 bg-gradient-to-r from-teal-400 to-cyan-500 relative">
                    <div class="absolute top-3 left-3 bg-white bg-opacity-90 px-2 py-1 rounded-full text-xs font-medium">
                        ⭐ 4.9 (201)
                    </div>
                </div>
                <div class="p-4">
                    <div class="flex items-center mb-2">
                        <div class="w-6 h-6 bg-gray-300 rounded-full mr-2"></div>
                        <span class="text-sm text-gray-600 dark:text-gray-400">Yuki</span>
                    </div>
                    <h3 class="font-semibold text-gray-900 dark:text-white mb-2">Sushi Making</h3>
                    <p class="text-gray-600 dark:text-gray-400 text-sm mb-3">Master the art of sushi</p>
                    <div class="flex items-center justify-between">
                        <span class="text-lg font-bold text-gray-900 dark:text-white">$85</span>
                        <button onclick="bookExperience('Sushi Making', '$85')" class="bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded text-sm">
                            Book
                        </button>
                    </div>
                </div>
            </div>

            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm overflow-hidden hover:shadow-md transition-shadow cursor-pointer">
                <div class="h-48 bg-gradient-to-r from-indigo-400 to-purple-500 relative">
                    <div class="absolute top-3 left-3 bg-white bg-opacity-90 px-2 py-1 rounded-full text-xs font-medium">
                        ⭐ 4.5 (92)
                    </div>
                </div>
                <div class="p-4">
                    <div class="flex items-center mb-2">
                        <div class="w-6 h-6 bg-gray-300 rounded-full mr-2"></div>
                        <span class="text-sm text-gray-600 dark:text-gray-400">Ahmed</span>
                    </div>
                    <h3 class="font-semibold text-gray-900 dark:text-white mb-2">Desert Safari</h3>
                    <p class="text-gray-600 dark:text-gray-400 text-sm mb-3">Adventure in the dunes</p>
                    <div class="flex items-center justify-between">
                        <span class="text-lg font-bold text-gray-900 dark:text-white">$120</span>
                        <button onclick="bookExperience('Desert Safari', '$120')" class="bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded text-sm">
                            Book
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Pagination -->
        <div class="flex items-center justify-center space-x-2">
            <button class="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg text-gray-600 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-700">
                Previous
            </button>
            <button class="px-3 py-2 bg-blue-600 text-white rounded-lg">1</button>
            <button class="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg text-gray-600 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-700">2</button>
            <button class="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg text-gray-600 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-700">3</button>
            <button class="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg text-gray-600 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-700">
                Next
            </button>
        </div>
    </div>
</div>

<script>
function bookExperience(name, price, email) {
    if (confirm(`Book "${name}" for ${price} per person?\n\nContact: ${email}\n\nThis will redirect you to the booking page.`)) {
        alert(`Booking "${name}"...\n\nYou would be contacted at:\n${email}\n\nBooking confirmation will be sent to your email.`);
    }
}
</script>
{% endblock %}
