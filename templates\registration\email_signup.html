<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sign up with email - Nestria</title>
    {% load static %}
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'nestria-red': '#FF5A5F',
                    }
                }
            }
        }
    </script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body class="bg-gray-50 min-h-screen flex items-center justify-center p-4">
    <div class="bg-white rounded-2xl shadow-xl w-full max-w-md p-8 relative">
        <!-- Close Button -->
        <button onclick="window.history.back()" class="absolute top-4 left-4 text-gray-400 hover:text-gray-600 text-xl">
            <i class="fas fa-times"></i>
        </button>

        <!-- Header -->
        <div class="text-center mb-8">
            <h1 class="text-2xl font-semibold text-gray-900 mb-2">Sign up with email</h1>
        </div>

        <!-- Logo -->
        <div class="flex justify-center mb-8">
            <img src="{% static 'images/nestria-logo-official.svg' %}"
                 alt="Nestria Logo"
                 class="w-32 h-32 object-contain">
        </div>

        <!-- Email Signup Form -->
        <form method="post" class="space-y-4">
            {% csrf_token %}
            
            <!-- First Name -->
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">First name</label>
                <input type="text" 
                       name="first_name" 
                       placeholder="Enter your first name"
                       class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-nestria-red focus:border-transparent"
                       required>
            </div>

            <!-- Last Name -->
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Last name</label>
                <input type="text" 
                       name="last_name" 
                       placeholder="Enter your last name"
                       class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-nestria-red focus:border-transparent"
                       required>
            </div>

            <!-- Email -->
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Email</label>
                <input type="email" 
                       name="email" 
                       placeholder="Enter your email"
                       class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-nestria-red focus:border-transparent"
                       required>
            </div>

            <!-- Password -->
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Password</label>
                <input type="password" 
                       name="password" 
                       placeholder="Create a password"
                       class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-nestria-red focus:border-transparent"
                       required>
            </div>

            <!-- Confirm Password -->
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Confirm password</label>
                <input type="password" 
                       name="confirm_password" 
                       placeholder="Confirm your password"
                       class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-nestria-red focus:border-transparent"
                       required>
            </div>

            <!-- Terms -->
            <div class="flex items-start">
                <input type="checkbox" 
                       name="terms" 
                       class="mt-1 mr-3 text-nestria-red focus:ring-nestria-red"
                       required>
                <p class="text-xs text-gray-600">
                    I agree to the <a href="#" class="text-nestria-red hover:underline">Terms of Service</a> 
                    and <a href="#" class="text-nestria-red hover:underline">Privacy Policy</a>
                </p>
            </div>

            <!-- Signup Button -->
            <button type="submit" class="w-full bg-gradient-to-r from-nestria-red to-red-600 text-white py-3 rounded-lg font-semibold hover:from-red-600 hover:to-red-700 transition-all">
                Sign up
            </button>
        </form>

        <!-- Login Link -->
        <div class="text-center mt-6">
            <p class="text-sm text-gray-600">
                Already have an account? 
                <a href="{% url 'core:login' %}" class="text-nestria-red hover:underline font-medium">Log in</a>
            </p>
        </div>
    </div>
</body>
</html>
