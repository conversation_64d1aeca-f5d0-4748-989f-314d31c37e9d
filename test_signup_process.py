#!/usr/bin/env python
"""
Script pour tester le processus de création de compte
"""
import os
import django
import random
import string

# Configuration Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'airbnb_clone.settings')
django.setup()

from django.contrib.auth.models import User
from core.models import UserProfile

def generate_unique_email():
    """Générer un email unique pour les tests"""
    random_string = ''.join(random.choices(string.ascii_lowercase + string.digits, k=8))
    return f"test_{random_string}@nestria.com"

def test_user_creation():
    """Tester la création d'utilisateur programmatiquement"""
    
    # Générer des données de test
    email = generate_unique_email()
    first_name = "Test"
    last_name = "User"
    password = "password123"
    
    print(f"🧪 Test de création d'utilisateur avec email: {email}")
    
    try:
        # Créer l'utilisateur
        user = User.objects.create_user(
            username=email,
            email=email,
            password=password,
            first_name=first_name,
            last_name=last_name
        )
        print(f"✅ Utilisateur créé: {user.username}")
        
        # C<PERSON>er le profil utilisateur
        profile, created = UserProfile.objects.get_or_create(user=user)
        if created:
            print("✅ Profil utilisateur créé")
        else:
            print("ℹ️  Profil utilisateur existait déjà")
        
        print(f"🎉 Création réussie ! Vous pouvez maintenant vous connecter avec:")
        print(f"   Email: {email}")
        print(f"   Password: {password}")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur lors de la création: {str(e)}")
        return False

def check_existing_users():
    """Vérifier les utilisateurs existants"""
    print("\n👥 Utilisateurs existants dans la base de données:")
    users = User.objects.all()
    for user in users:
        has_profile = hasattr(user, 'profile')
        print(f"   - {user.email} ({user.first_name} {user.last_name}) - Profil: {'✅' if has_profile else '❌'}")

if __name__ == '__main__':
    print("🔍 Vérification du système de création d'utilisateur\n")
    
    # Vérifier les utilisateurs existants
    check_existing_users()
    
    # Tester la création d'un nouvel utilisateur
    print("\n" + "="*50)
    success = test_user_creation()
    
    if success:
        print("\n✅ Le système de création d'utilisateur fonctionne correctement !")
    else:
        print("\n❌ Il y a un problème avec le système de création d'utilisateur.")
    
    print("\n💡 Pour tester via l'interface web:")
    print("   1. Allez sur http://127.0.0.1:8000/signup/")
    print("   2. Cliquez sur 'Sign up' en haut à droite")
    print("   3. Remplissez le formulaire avec un email unique")
    print("   4. Cliquez sur 'Sign up'")
