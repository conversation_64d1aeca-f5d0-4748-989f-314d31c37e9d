"""
Service pour l'envoi de codes de vérification via WhatsApp
Utilise l'API Twilio WhatsApp Business
"""

import os
import logging
from twilio.rest import Client
from twilio.base.exceptions import TwilioException
from django.conf import settings

logger = logging.getLogger(__name__)


class WhatsAppService:
    """Service pour l'envoi de messages WhatsApp via Twilio"""
    
    def __init__(self):
        # Configuration Twilio depuis les variables d'environnement
        self.account_sid = getattr(settings, 'TWILIO_ACCOUNT_SID', None)
        self.auth_token = getattr(settings, 'TWILIO_AUTH_TOKEN', None)
        self.whatsapp_number = getattr(settings, 'TWILIO_WHATSAPP_NUMBER', 'whatsapp:+***********')  # Sandbox par défaut
        
        if self.account_sid and self.auth_token:
            self.client = Client(self.account_sid, self.auth_token)
        else:
            self.client = None
            logger.warning("Twilio credentials not configured. WhatsApp messages will be simulated.")
    
    def send_verification_code(self, phone_number, verification_code):
        """
        Envoie un code de vérification via WhatsApp
        
        Args:
            phone_number (str): Numéro de téléphone au format international (+************)
            verification_code (str): Code de vérification à 6 chiffres
            
        Returns:
            dict: Résultat de l'envoi avec success (bool) et message_sid ou error
        """
        
        # Nettoyer le numéro de téléphone
        clean_phone = self._clean_phone_number(phone_number)
        
        if not self.client:
            # Mode simulation si pas de configuration Twilio
            logger.info(f"🔐 WhatsApp verification code for {clean_phone}: {verification_code}")
            logger.info(f"📱 Message sent: 'Your Nestria verification code is: {verification_code}. This code will expire in 5 minutes.'")
            return {
                'success': True,
                'message_sid': 'simulated_message_id',
                'status': 'simulated'
            }
        
        try:
            # Créer le message WhatsApp
            message_body = f"""🏠 *Nestria Verification*

Your verification code is: *{verification_code}*

This code will expire in 5 minutes.
Do not share this code with anyone.

Welcome to Nestria! 🌟"""

            # Envoyer le message via Twilio WhatsApp API
            message = self.client.messages.create(
                body=message_body,
                from_=self.whatsapp_number,
                to=f'whatsapp:{clean_phone}'
            )
            
            logger.info(f"✅ WhatsApp message sent successfully to {clean_phone}")
            logger.info(f"📱 Message SID: {message.sid}")
            
            return {
                'success': True,
                'message_sid': message.sid,
                'status': message.status
            }
            
        except TwilioException as e:
            logger.error(f"❌ Twilio WhatsApp error for {clean_phone}: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'error_code': getattr(e, 'code', None)
            }
            
        except Exception as e:
            logger.error(f"❌ Unexpected error sending WhatsApp to {clean_phone}: {str(e)}")
            return {
                'success': False,
                'error': 'Unexpected error occurred'
            }
    
    def _clean_phone_number(self, phone_number):
        """
        Nettoie et formate le numéro de téléphone
        
        Args:
            phone_number (str): Numéro brut
            
        Returns:
            str: Numéro formaté au format international
        """
        if not phone_number:
            return None
            
        # Supprimer tous les espaces, tirets, parenthèses
        clean = phone_number.strip().replace(' ', '').replace('-', '').replace('(', '').replace(')', '')
        
        # Ajouter le + si manquant
        if not clean.startswith('+'):
            clean = '+' + clean
            
        return clean
    
    def get_delivery_status(self, message_sid):
        """
        Vérifie le statut de livraison d'un message WhatsApp
        
        Args:
            message_sid (str): ID du message Twilio
            
        Returns:
            dict: Statut du message
        """
        if not self.client or message_sid == 'simulated_message_id':
            return {
                'status': 'delivered',
                'simulated': True
            }
        
        try:
            message = self.client.messages(message_sid).fetch()
            return {
                'status': message.status,
                'error_code': message.error_code,
                'error_message': message.error_message,
                'date_sent': message.date_sent,
                'date_updated': message.date_updated
            }
        except TwilioException as e:
            logger.error(f"Error fetching message status: {str(e)}")
            return {
                'status': 'unknown',
                'error': str(e)
            }


# Instance globale du service
whatsapp_service = WhatsAppService()


def send_whatsapp_verification(phone_number, verification_code):
    """
    Fonction helper pour envoyer un code de vérification WhatsApp
    
    Args:
        phone_number (str): Numéro de téléphone
        verification_code (str): Code de vérification
        
    Returns:
        bool: True si envoyé avec succès, False sinon
    """
    result = whatsapp_service.send_verification_code(phone_number, verification_code)
    return result.get('success', False)
