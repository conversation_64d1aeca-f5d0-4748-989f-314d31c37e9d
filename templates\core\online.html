{% extends 'base.html' %}
{% load static %}

{% block title %}Online Experiences - Nestria{% endblock %}

{% block content %}
<div class="min-h-screen bg-gray-50 dark:bg-gray-900">
    <!-- Hero Section -->
    <div class="relative bg-gradient-to-r from-green-600 to-blue-600 py-24">
        <div class="absolute inset-0 bg-black/20"></div>
        <div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h1 class="text-4xl md:text-6xl font-bold text-white mb-6">
                Online Experiences
            </h1>
            <p class="text-xl text-white/90 mb-8 max-w-3xl mx-auto">
                Join interactive experiences from anywhere in the world. Connect with hosts and fellow travelers 
                through live, virtual activities that bring cultures together.
            </p>
            <div class="flex flex-col sm:flex-row gap-4 justify-center">
                <a href="{% url 'core:browse_online' %}" class="inline-block px-8 py-3 bg-white text-green-600 font-semibold rounded-lg hover:bg-gray-100 transition-colors">
                    Browse Online Experiences
                </a>
                <a href="{% url 'core:host_online' %}" class="inline-block px-8 py-3 border-2 border-white text-white font-semibold rounded-lg hover:bg-white hover:text-green-600 transition-colors">
                    Host Online
                </a>
            </div>
        </div>
    </div>

    <!-- Features -->
    <div class="py-16">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
                <div class="text-center">
                    <div class="w-16 h-16 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-video text-green-600 dark:text-green-400 text-2xl"></i>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-2">Live & Interactive</h3>
                    <p class="text-gray-600 dark:text-gray-400">
                        Real-time experiences with live hosts and interactive elements
                    </p>
                </div>
                
                <div class="text-center">
                    <div class="w-16 h-16 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-globe text-blue-600 dark:text-blue-400 text-2xl"></i>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-2">From Anywhere</h3>
                    <p class="text-gray-600 dark:text-gray-400">
                        Join from the comfort of your home, no travel required
                    </p>
                </div>
                
                <div class="text-center">
                    <div class="w-16 h-16 bg-purple-100 dark:bg-purple-900 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-users text-purple-600 dark:text-purple-400 text-2xl"></i>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-2">Small Groups</h3>
                    <p class="text-gray-600 dark:text-gray-400">
                        Intimate group sizes for personalized experiences
                    </p>
                </div>
            </div>

            <!-- Online Experience Categories -->
            <h2 class="text-3xl font-bold text-gray-900 dark:text-white mb-8 text-center">
                Popular Online Experiences
            </h2>
            
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                <!-- Virtual Cooking Class -->
                <div class="bg-white dark:bg-gray-800 rounded-lg overflow-hidden shadow-sm hover:shadow-lg transition-all duration-300">
                    <div class="relative">
                        <div class="w-full h-48 bg-gradient-to-br from-orange-400 to-red-500 flex items-center justify-center">
                            <i class="fas fa-utensils text-white text-4xl"></i>
                        </div>
                        <div class="absolute top-3 left-3">
                            <span class="bg-green-500 text-white px-2 py-1 rounded-full text-xs font-medium">
                                🔴 LIVE
                            </span>
                        </div>
                    </div>
                    <div class="p-6">
                        <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-2">
                            Virtual Italian Cooking Class
                        </h3>
                        <p class="text-gray-600 dark:text-gray-400 mb-4">
                            Learn to make authentic pasta from a chef in Rome. Ingredients list provided in advance.
                        </p>
                        <div class="flex items-center justify-between mb-4">
                            <div class="flex items-center">
                                <i class="fas fa-star text-yellow-400 mr-1"></i>
                                <span class="text-sm text-gray-600 dark:text-gray-400">4.9 (156 reviews)</span>
                            </div>
                            <div class="flex items-center text-sm text-gray-500 dark:text-gray-400">
                                <i class="fas fa-clock mr-1"></i>
                                90 minutes
                            </div>
                        </div>
                        <div class="flex items-center justify-between">
                            <div>
                                <span class="text-2xl font-bold text-gray-900 dark:text-white">$45</span>
                                <span class="text-gray-500 dark:text-gray-400 text-sm"> per person</span>
                            </div>
                            <button onclick="joinOnlineExperience('Virtual Cooking Class', '$45', 'live')" class="px-4 py-2 bg-green-600 hover:bg-green-700 text-white font-medium rounded-lg transition-colors">
                                Join Live
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Virtual Museum Tour -->
                <div class="bg-white dark:bg-gray-800 rounded-lg overflow-hidden shadow-sm hover:shadow-lg transition-all duration-300">
                    <div class="relative">
                        <div class="w-full h-48 bg-gradient-to-br from-purple-400 to-blue-500 flex items-center justify-center">
                            <i class="fas fa-university text-white text-4xl"></i>
                        </div>
                        <div class="absolute top-3 left-3">
                            <span class="bg-blue-500 text-white px-2 py-1 rounded-full text-xs font-medium">
                                Interactive
                            </span>
                        </div>
                    </div>
                    <div class="p-6">
                        <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-2">
                            Virtual Louvre Museum Tour
                        </h3>
                        <p class="text-gray-600 dark:text-gray-400 mb-4">
                            Explore the world's most famous artworks with an expert art historian guide.
                        </p>
                        <div class="flex items-center justify-between mb-4">
                            <div class="flex items-center">
                                <i class="fas fa-star text-yellow-400 mr-1"></i>
                                <span class="text-sm text-gray-600 dark:text-gray-400">4.8 (203 reviews)</span>
                            </div>
                            <div class="flex items-center text-sm text-gray-500 dark:text-gray-400">
                                <i class="fas fa-clock mr-1"></i>
                                60 minutes
                            </div>
                        </div>
                        <div class="flex items-center justify-between">
                            <div>
                                <span class="text-2xl font-bold text-gray-900 dark:text-white">$25</span>
                                <span class="text-gray-500 dark:text-gray-400 text-sm"> per person</span>
                            </div>
                            <button onclick="joinOnlineExperience('Virtual Museum Tour', '$25', 'tour')" class="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg transition-colors">
                                Book Tour
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Virtual Meditation -->
                <div class="bg-white dark:bg-gray-800 rounded-lg overflow-hidden shadow-sm hover:shadow-lg transition-all duration-300">
                    <div class="relative">
                        <div class="w-full h-48 bg-gradient-to-br from-green-400 to-teal-500 flex items-center justify-center">
                            <i class="fas fa-leaf text-white text-4xl"></i>
                        </div>
                        <div class="absolute top-3 left-3">
                            <span class="bg-teal-500 text-white px-2 py-1 rounded-full text-xs font-medium">
                                Wellness
                            </span>
                        </div>
                    </div>
                    <div class="p-6">
                        <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-2">
                            Mindfulness from Bali
                        </h3>
                        <p class="text-gray-600 dark:text-gray-400 mb-4">
                            Join a peaceful meditation session guided by a monk from a temple in Bali.
                        </p>
                        <div class="flex items-center justify-between mb-4">
                            <div class="flex items-center">
                                <i class="fas fa-star text-yellow-400 mr-1"></i>
                                <span class="text-sm text-gray-600 dark:text-gray-400">4.9 (89 reviews)</span>
                            </div>
                            <div class="flex items-center text-sm text-gray-500 dark:text-gray-400">
                                <i class="fas fa-clock mr-1"></i>
                                45 minutes
                            </div>
                        </div>
                        <div class="flex items-center justify-between">
                            <div>
                                <span class="text-2xl font-bold text-gray-900 dark:text-white">$20</span>
                                <span class="text-gray-500 dark:text-gray-400 text-sm"> per person</span>
                            </div>
                            <button onclick="joinOnlineExperience('Virtual Meditation Session', '$20', 'session')" class="px-4 py-2 bg-teal-600 hover:bg-teal-700 text-white font-medium rounded-lg transition-colors">
                                Join Session
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Call to Action -->
            <div class="mt-16 text-center">
                <h2 class="text-2xl font-bold text-gray-900 dark:text-white mb-4">
                    Ready to explore the world from home?
                </h2>
                <p class="text-gray-600 dark:text-gray-400 mb-8 max-w-2xl mx-auto">
                    Discover cultures, learn new skills, and connect with people from around the globe through our online experiences.
                </p>
                <a href="{% url 'core:browse_online' %}" class="inline-block px-8 py-3 bg-green-600 hover:bg-green-700 text-white font-semibold rounded-lg transition-colors">
                    Browse All Online Experiences
                </a>
            </div>
        </div>
    </div>
</div>

<script>
function joinOnlineExperience(name, price, type) {
    let message = `Join "${name}" for ${price} per person?\n\n`;

    switch(type) {
        case 'live':
            message += "This is a live interactive session. You'll need:\n• Stable internet connection\n• Camera and microphone\n• Cooking ingredients (list will be provided)";
            break;
        case 'tour':
            message += "This is a virtual guided tour. You'll need:\n• Stable internet connection\n• Comfortable seating\n• Optional: notebook for taking notes";
            break;
        case 'session':
            message += "This is a meditation session. You'll need:\n• Quiet space\n• Comfortable clothing\n• Optional: yoga mat or cushion";
            break;
        default:
            message += "This is an online experience.";
    }

    if (confirm(message + "\n\nProceed to join?")) {
        // Show connection process
        showNotification(`Connecting to "${name}"...`, 'info', 2000);

        // Simulate connection process
        setTimeout(() => {
            showNotification(`Successfully joined "${name}"! Opening virtual room...`, 'success', 3000);

            // Create virtual room demo
            setTimeout(() => {
                createVirtualRoom(name, type);
            }, 1000);
        }, 2000);
    }
}

// Create virtual room interface
function createVirtualRoom(experienceName, type) {
    const modal = document.createElement('div');
    modal.className = 'fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50';
    modal.innerHTML = `
        <div class="bg-white rounded-lg p-8 max-w-2xl w-full mx-4">
            <div class="text-center">
                <h2 class="text-2xl font-bold text-gray-900 mb-4">${experienceName}</h2>
                <div class="bg-gray-900 rounded-lg p-8 mb-6 text-white">
                    <div class="text-6xl mb-4">📹</div>
                    <p class="text-lg">Virtual Room Active</p>
                    <p class="text-sm opacity-75">Experience type: ${type}</p>
                </div>

                <div class="grid grid-cols-3 gap-4 mb-6">
                    <button onclick="toggleMute()" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700">
                        🎤 Mute
                    </button>
                    <button onclick="toggleVideo()" class="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700">
                        📹 Video
                    </button>
                    <button onclick="shareScreen()" class="bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700">
                        🖥️ Share
                    </button>
                </div>

                <div class="text-sm text-gray-600 mb-4">
                    <p>👥 3 participants online</p>
                    <p>⏱️ Session started 5 minutes ago</p>
                </div>

                <button onclick="leaveRoom()" class="bg-red-600 text-white px-6 py-2 rounded-lg hover:bg-red-700">
                    Leave Room
                </button>
            </div>
        </div>
    `;

    document.body.appendChild(modal);

    // Auto-close demo after 15 seconds
    setTimeout(() => {
        showNotification('Demo session completed! In a real app, this would be a full virtual experience.', 'info', 4000);
    }, 15000);
}

// Virtual room controls
function toggleMute() {
    showNotification('Microphone toggled', 'info', 1000);
}

function toggleVideo() {
    showNotification('Camera toggled', 'info', 1000);
}

function shareScreen() {
    showNotification('Screen sharing started', 'success', 2000);
}

function leaveRoom() {
    const modal = document.querySelector('.fixed.inset-0.bg-black');
    if (modal) {
        modal.remove();
        showNotification('Left virtual room. Thank you for joining!', 'success', 3000);
    }
}
</script>
{% endblock %}
