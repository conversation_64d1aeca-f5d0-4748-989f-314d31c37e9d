<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Choose an account - Google</title>
    {% load static %}
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'google-blue': '#1a73e8',
                        'google-gray': '#5f6368',
                    }
                }
            }
        }
    </script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body class="bg-white min-h-screen flex items-center justify-center p-4">
    <div class="bg-white w-full max-w-md border border-gray-200 rounded-lg shadow-lg p-8">
        <!-- Google Logo -->
        <div class="text-center mb-6">
            <div class="inline-flex items-center space-x-2">
                <svg width="24" height="24" viewBox="0 0 24 24">
                    <path fill="#4285f4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                    <path fill="#34a853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                    <path fill="#fbbc05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                    <path fill="#ea4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
                </svg>
                <span class="text-xl text-gray-700">Google</span>
            </div>
        </div>

        <!-- Header -->
        <div class="text-center mb-8">
            <h1 class="text-2xl font-normal text-gray-900 mb-2">Choose an account</h1>
            <p class="text-sm text-google-gray">to continue to Nestria</p>
        </div>

        <!-- Account Options -->
        <div class="space-y-3 mb-6">
            <!-- Primary Account -->
            <button onclick="selectGoogleAccount('<EMAIL>')" 
                    class="w-full flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                <div class="w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center text-white font-medium mr-4">
                    A
                </div>
                <div class="flex-1 text-left">
                    <div class="font-medium text-gray-900">auba me</div>
                    <div class="text-sm text-google-gray"><EMAIL></div>
                </div>
            </button>

            <!-- Secondary Account -->
            <button onclick="selectGoogleAccount('<EMAIL>')" 
                    class="w-full flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                <div class="w-10 h-10 bg-green-500 rounded-full flex items-center justify-center text-white font-medium mr-4">
                    M
                </div>
                <div class="flex-1 text-left">
                    <div class="font-medium text-gray-900">Malek Aymen</div>
                    <div class="text-sm text-google-gray"><EMAIL></div>
                </div>
            </button>

            <!-- Use Another Account -->
            <button onclick="useAnotherAccount()" 
                    class="w-full flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                <div class="w-10 h-10 border-2 border-gray-300 rounded-full flex items-center justify-center mr-4">
                    <i class="fas fa-user-plus text-google-gray"></i>
                </div>
                <div class="flex-1 text-left">
                    <div class="font-medium text-google-gray">Use another account</div>
                </div>
            </button>
        </div>

        <!-- Privacy Notice -->
        <div class="text-xs text-google-gray text-center mb-6">
            To continue, Google will share your name, email address, and profile picture with Nestria. 
            Before using this app, you can review Nestria's 
            <a href="#" class="text-google-blue hover:underline">privacy policy</a> and 
            <a href="#" class="text-google-blue hover:underline">terms of service</a>.
        </div>

        <!-- Language Selector -->
        <div class="text-center">
            <select class="text-sm text-google-gray border-none bg-transparent focus:outline-none">
                <option value="en">English (United States)</option>
                <option value="fr">Français</option>
                <option value="es">Español</option>
                <option value="ar">العربية</option>
            </select>
        </div>
    </div>

    <script>
        function selectGoogleAccount(email) {
            // Simuler la sélection du compte Google
            alert(`Connecting with Google account: ${email}\n\nThis would normally authenticate with Google and redirect to the app.`);
            
            // En production, ceci ferait l'authentification réelle
            // window.location.href = `/auth/google/callback/?email=${email}`;
            
            // Pour la démo, rediriger vers contact-info
            window.location.href = '/contact-info/';
        }

        function useAnotherAccount() {
            // Simuler l'ajout d'un autre compte
            const email = prompt('Enter your Google email address:');
            if (email && email.includes('@')) {
                selectGoogleAccount(email);
            }
        }

        // Animation d'entrée
        document.addEventListener('DOMContentLoaded', function() {
            const container = document.querySelector('.bg-white.w-full');
            container.style.opacity = '0';
            container.style.transform = 'translateY(20px)';

            setTimeout(() => {
                container.style.transition = 'all 0.6s ease-out';
                container.style.opacity = '1';
                container.style.transform = 'translateY(0)';
            }, 150);
        });
    </script>
</body>
</html>
