from django.contrib import admin
from .models import Room, RoomType, Amenity, RoomImage, Review, Favorite, Destination, DestinationImage


class RoomImageInline(admin.TabularInline):
    model = RoomImage
    extra = 1
    fields = ['image', 'caption', 'order']


@admin.register(RoomType)
class RoomTypeAdmin(admin.ModelAdmin):
    list_display = ['name', 'description']
    search_fields = ['name']


@admin.register(Amenity)
class AmenityAdmin(admin.ModelAdmin):
    list_display = ['name', 'icon']
    search_fields = ['name']
    list_filter = ['name']


@admin.register(Room)
class RoomAdmin(admin.ModelAdmin):
    list_display = ['title', 'host', 'city', 'country', 'price_per_night', 'max_guests', 'is_active', 'created_at']
    list_filter = ['room_type', 'country', 'city', 'is_active', 'instant_book', 'created_at']
    search_fields = ['title', 'description', 'city', 'country', 'host__username']
    readonly_fields = ['created_at', 'updated_at', 'average_rating', 'review_count']
    filter_horizontal = ['amenities']
    inlines = [RoomImageInline]

    fieldsets = (
        ('Basic Information', {
            'fields': ('title', 'description', 'room_type', 'host')
        }),
        ('Location', {
            'fields': ('country', 'city', 'address', 'latitude', 'longitude')
        }),
        ('Capacity', {
            'fields': ('max_guests', 'bedrooms', 'beds', 'bathrooms')
        }),
        ('Pricing', {
            'fields': ('price_per_night', 'cleaning_fee', 'service_fee')
        }),
        ('Features', {
            'fields': ('amenities', 'instant_book')
        }),
        ('Status', {
            'fields': ('is_active', 'created_at', 'updated_at', 'average_rating', 'review_count')
        }),
    )

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('host', 'room_type').prefetch_related('amenities')


@admin.register(RoomImage)
class RoomImageAdmin(admin.ModelAdmin):
    list_display = ['room', 'caption', 'order']
    list_filter = ['room__city', 'room__country']
    search_fields = ['room__title', 'caption']
    ordering = ['room', 'order']


@admin.register(Review)
class ReviewAdmin(admin.ModelAdmin):
    list_display = ['room', 'user', 'rating', 'created_at']
    list_filter = ['rating', 'created_at', 'room__city']
    search_fields = ['room__title', 'user__username', 'comment']
    readonly_fields = ['created_at']
    ordering = ['-created_at']


@admin.register(Favorite)
class FavoriteAdmin(admin.ModelAdmin):
    list_display = ['user', 'room', 'created_at']
    list_filter = ['created_at', 'room__city']
    search_fields = ['user__username', 'room__title']
    ordering = ['-created_at']


class DestinationImageInline(admin.TabularInline):
    model = DestinationImage
    extra = 1
    fields = ['image', 'caption', 'order']


@admin.register(Destination)
class DestinationAdmin(admin.ModelAdmin):
    list_display = ['name', 'country', 'is_featured', 'room_count', 'created_at']
    list_filter = ['country', 'is_featured', 'created_at']
    search_fields = ['name', 'country', 'description']
    prepopulated_fields = {'slug': ('name',)}
    readonly_fields = ['room_count', 'created_at']
    inlines = [DestinationImageInline]

    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'country', 'slug', 'description', 'historical_info')
        }),
        ('Images', {
            'fields': ('main_image',)
        }),
        ('Location', {
            'fields': ('latitude', 'longitude')
        }),
        ('SEO & Display', {
            'fields': ('meta_description', 'is_featured')
        }),
        ('Statistics', {
            'fields': ('room_count', 'created_at'),
            'classes': ('collapse',)
        }),
    )


@admin.register(DestinationImage)
class DestinationImageAdmin(admin.ModelAdmin):
    list_display = ['destination', 'caption', 'order']
    list_filter = ['destination__country']
    search_fields = ['destination__name', 'caption']
    ordering = ['destination', 'order']
