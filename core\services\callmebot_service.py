"""
Service WhatsApp CallMeBot - 100% GRATUIT
Envoie de vrais messages WhatsApp
"""

import requests
import logging
import os
from django.conf import settings

logger = logging.getLogger(__name__)


class CallMeBotService:
    """Service WhatsApp utilisant CallMeBot API (gratuit)"""
    
    def __init__(self):
        self.api_key = os.getenv('CALLMEBOT_API_KEY', '')
        self.base_url = "https://api.callmebot.com/whatsapp.php"
        
    def send_verification_code(self, phone_number, verification_code):
        """
        Envoie un code de vérification via WhatsApp CallMeBot
        
        Args:
            phone_number (str): Numéro de téléphone (+212603999557)
            verification_code (str): Code à 6 chiffres
            
        Returns:
            dict: Résultat de l'envoi
        """
        
        if not self.api_key or self.api_key == 'YOUR_REAL_API_KEY_HERE' or self.api_key == 'test_key_pending':
            # Mode simulation si pas d'API key valide
            return self._simulate_whatsapp_send(phone_number, verification_code)
        
        # Nettoyer le numéro (CallMeBot veut le numéro sans +)
        clean_phone = self._clean_phone_number(phone_number)
        
        # Message WhatsApp
        message = f"🏠 *Nestria Verification*\n\nYour verification code is: *{verification_code}*\n\nThis code will expire in 5 minutes.\nDo not share this code with anyone.\n\nWelcome to Nestria! 🌟"
        
        try:
            # Paramètres pour CallMeBot
            params = {
                'phone': clean_phone,
                'text': message,
                'apikey': self.api_key
            }
            
            # Envoi de la requête
            response = requests.get(self.base_url, params=params)
            
            if response.status_code == 200 and "ERROR:" not in response.text and "Message queued" in response.text:
                logger.info(f"✅ WhatsApp CallMeBot message sent successfully to {clean_phone}")
                print(f"\n🎉 WHATSAPP RÉEL ENVOYÉ via CallMeBot!")
                print(f"📱 Numéro: {clean_phone}")
                print(f"🔐 Code: {verification_code}")
                print(f"✅ Statut: Message en file d'attente\n")

                return {
                    'success': True,
                    'message_id': f'callmebot_{verification_code}',
                    'status': 'sent',
                    'method': 'callmebot'
                }
            else:
                logger.error(f"❌ WhatsApp CallMeBot error for {clean_phone}: {response.text}")
                print(f"❌ CallMeBot Error: {response.text}")
                # Si erreur API, basculer en mode simulation
                return self._simulate_whatsapp_send(phone_number, verification_code)
                
        except Exception as e:
            logger.error(f"❌ Unexpected error sending WhatsApp CallMeBot to {clean_phone}: {str(e)}")
            # Si erreur, basculer en mode simulation
            return self._simulate_whatsapp_send(phone_number, verification_code)
    
    def _simulate_whatsapp_send(self, phone_number, verification_code):
        """Simulation si pas d'API key configurée"""
        
        print("\n" + "🟢" * 50)
        print("📱 WHATSAPP MESSAGE SIMULATION")
        print("🟢" * 50)
        print(f"📞 TO: {phone_number}")
        print(f"🔐 CODE: {verification_code}")
        print(f"💬 MESSAGE:")
        print(f"   🏠 Nestria Verification")
        print(f"   ")
        print(f"   Your verification code is: {verification_code}")
        print(f"   ")
        print(f"   This code will expire in 5 minutes.")
        print(f"   Do not share this code with anyone.")
        print(f"   ")
        print(f"   Welcome to Nestria! 🌟")
        print("🟢" * 50)
        print("⚠️  POUR RECEVOIR SUR WHATSAPP RÉEL:")
        print("1. Envoyez 'I allow callmebot to send me messages' au +34 644 59 71 67")
        print("2. Récupérez votre API key")
        print("3. Configurez CALLMEBOT_API_KEY dans .env")
        print("4. OU configurez WhatsApp Business API Meta")
        print("5. OU utilisez une autre méthode d'envoi")
        print("🟢" * 50 + "\n")
        
        return {
            'success': True,
            'message_id': f'simulation_{verification_code}',
            'status': 'simulated',
            'method': 'simulation'
        }
    
    def _clean_phone_number(self, phone_number):
        """Nettoie le numéro de téléphone pour CallMeBot"""
        if not phone_number:
            return None
            
        # Enlever tous les caractères non numériques
        clean = ''.join(filter(str.isdigit, phone_number))
        
        # CallMeBot veut le numéro sans le +
        return clean


# Instance globale
callmebot_service = CallMeBotService()


def send_whatsapp_verification_callmebot(phone_number, verification_code):
    """
    Fonction helper pour envoyer via CallMeBot
    """
    result = callmebot_service.send_verification_code(phone_number, verification_code)
    return result.get('success', False)
