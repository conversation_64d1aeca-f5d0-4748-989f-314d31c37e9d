# Environment variables for Airbnb Clone production
# Copy this file to .env and update the values

# Django settings
SECRET_KEY=your-very-secret-key-here-change-this-in-production
DEBUG=False
ALLOWED_HOSTS=localhost,127.0.0.1,your-domain.com

# Database configuration
DB_NAME=airbnb_clone
DB_USER=postgres
DB_PASSWORD=your_database_password
DB_HOST=localhost
DB_PORT=5432

# Redis configuration
REDIS_URL=redis://127.0.0.1:6379/1

# Email configuration
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your-email-password
DEFAULT_FROM_EMAIL=<EMAIL>

# Twilio WhatsApp configuration
# Get these values from your Twilio Console: https://console.twilio.com/
TWILIO_ACCOUNT_SID=your_account_sid_here
TWILIO_AUTH_TOKEN=your_auth_token_here
TWILIO_WHATSAPP_NUMBER=whatsapp:+***********

# Security settings (set to True when using HTTPS)
SECURE_SSL_REDIRECT=False
SESSION_COOKIE_SECURE=False
CSRF_COOKIE_SECURE=False

# Sentry (optional, for error tracking)
SENTRY_DSN=your-sentry-dsn-here

# AWS S3 (optional, for static/media files)
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key
AWS_STORAGE_BUCKET_NAME=your-s3-bucket-name
AWS_S3_REGION_NAME=us-east-1
