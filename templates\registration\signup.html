<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sign up - Nestria</title>
    {% load static %}
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'nestria-red': '#FF5A5F',
                    }
                }
            }
        }
    </script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="{% static 'css/social-auth.css' %}">
</head>
<body class="bg-gray-50 min-h-screen flex items-center justify-center p-4">
    <div class="bg-white rounded-2xl shadow-xl w-full max-w-md p-8 relative">
        <!-- Close Button -->
        <button onclick="window.history.back()" class="absolute top-4 left-4 text-gray-400 hover:text-gray-600 text-xl">
            <i class="fas fa-times"></i>
        </button>

        <!-- Header -->
        <div class="text-center mb-8">
            <h1 class="text-2xl font-semibold text-gray-900 mb-2">Sign up</h1>
        </div>

        <!-- Logo -->
        <div class="flex justify-center mb-8">
            <img src="{% static 'images/nestria-logo-official.svg' %}"
                 alt="Nestria Logo"
                 class="w-32 h-32 object-contain">
        </div>

        <div class="text-center mb-6">
            <h2 class="text-xl font-medium text-gray-800">Welcome to Nestria</h2>
        </div>



        <!-- Phone Number Form -->
        <form method="post" class="space-y-4">
            {% csrf_token %}
            
            <!-- Country Code -->
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Country code</label>
                <select class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-nestria-red focus:border-transparent">
                    <option value="+212">Morocco (+212)</option>
                    <option value="+33">France (+33)</option>
                    <option value="+1">United States (+1)</option>
                    <option value="+44">United Kingdom (+44)</option>
                </select>
            </div>

            <!-- Phone Number -->
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Phone number</label>
                <input type="tel" 
                       name="phone" 
                       placeholder="+212 603999557"
                       class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-nestria-red focus:border-transparent">
            </div>

            <p class="text-xs text-gray-500">
                We'll call or text you to confirm your number. Standard message and data rates apply.
                <a href="#" class="underline">Privacy Policy</a>
            </p>

            <!-- Continue Button -->
            <button type="submit" id="continueBtn" class="w-full bg-gradient-to-r from-nestria-red to-red-600 text-white py-3 rounded-lg font-semibold hover:from-red-600 hover:to-red-700 transition-all">
                <span id="btnText">Continue</span>
                <span id="btnLoading" class="hidden">
                    <i class="fas fa-spinner fa-spin mr-2"></i>Sending code...
                </span>
            </button>
        </form>

        <!-- Login Link -->
        <div class="text-center mt-6">
            <p class="text-sm text-gray-600">
                Already have an account? 
                <a href="{% url 'core:modern_login' %}" class="text-nestria-red hover:underline font-medium">Log in</a>
            </p>
        </div>
    </div>

    <script>
        // Gestion du formulaire
        document.querySelector('form').addEventListener('submit', function(e) {
            const phone = document.querySelector('input[name="phone"]').value.trim();

            if (!phone) {
                e.preventDefault();
                alert('Please enter your phone number');
                return;
            }

            // Afficher l'état de chargement
            const btnText = document.getElementById('btnText');
            const btnLoading = document.getElementById('btnLoading');
            const continueBtn = document.getElementById('continueBtn');

            btnText.classList.add('hidden');
            btnLoading.classList.remove('hidden');
            continueBtn.disabled = true;

            // Le formulaire sera soumis normalement
        });
    </script>
</body>
</html>
