<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Signup - Nestria</title>
    {% load static %}
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'nestria-red': '#FF5A5F',
                    }
                }
            }
        }
    </script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body class="bg-gray-50 min-h-screen p-8">
    <div class="max-w-4xl mx-auto">
        <div class="text-center mb-8">
            <h1 class="text-3xl font-bold text-gray-900 mb-4">Test du Système d'Inscription</h1>
            <p class="text-lg text-gray-600">Testez rapidement chaque étape du processus</p>
        </div>

        <!-- Quick Test Form -->
        <div class="bg-white rounded-lg shadow-lg p-6 mb-8">
            <h2 class="text-xl font-semibold mb-4">Test Rapide</h2>
            <form action="{% url 'core:signup' %}" method="post" class="space-y-4">
                {% csrf_token %}
                <div class="grid md:grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Numéro de téléphone</label>
                        <input type="tel" 
                               name="phone" 
                               value="+212 603999557"
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-nestria-red focus:border-transparent">
                    </div>
                    <div class="flex items-end">
                        <button type="submit" class="w-full bg-nestria-red text-white py-3 rounded-lg font-semibold hover:bg-red-600 transition-all">
                            <i class="fas fa-rocket mr-2"></i>Démarrer le Test
                        </button>
                    </div>
                </div>
            </form>
        </div>

        <!-- Test Instructions -->
        <div class="grid md:grid-cols-2 gap-6 mb-8">
            <div class="bg-blue-50 border border-blue-200 rounded-lg p-6">
                <h3 class="text-lg font-semibold text-blue-900 mb-3">
                    <i class="fas fa-info-circle mr-2"></i>Instructions de Test
                </h3>
                <ol class="space-y-2 text-blue-800 text-sm">
                    <li><strong>1.</strong> Cliquez sur "Démarrer le Test" avec le numéro pré-rempli</li>
                    <li><strong>2.</strong> Le code de vérification s'affichera dans un message vert</li>
                    <li><strong>3.</strong> Copiez le code et collez-le dans les 6 champs</li>
                    <li><strong>4.</strong> Continuez avec l'email: <EMAIL></li>
                    <li><strong>5.</strong> Utilisez une date de naissance qui donne 18+ ans</li>
                    <li><strong>6.</strong> Acceptez l'engagement communautaire</li>
                </ol>
            </div>

            <div class="bg-green-50 border border-green-200 rounded-lg p-6">
                <h3 class="text-lg font-semibold text-green-900 mb-3">
                    <i class="fas fa-check-circle mr-2"></i>Fonctionnalités Testées
                </h3>
                <ul class="space-y-2 text-green-800 text-sm">
                    <li><i class="fas fa-check mr-2"></i>Génération automatique du code 6 chiffres</li>
                    <li><i class="fas fa-check mr-2"></i>Simulation d'envoi WhatsApp</li>
                    <li><i class="fas fa-check mr-2"></i>Validation d'âge (18+ ans)</li>
                    <li><i class="fas fa-check mr-2"></i>Gestion des sessions</li>
                    <li><i class="fas fa-check mr-2"></i>Création automatique du compte</li>
                    <li><i class="fas fa-check mr-2"></i>Redirection vers onboarding</li>
                </ul>
            </div>
        </div>

        <!-- Debug Info -->
        <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-6 mb-8">
            <h3 class="text-lg font-semibold text-yellow-900 mb-3">
                <i class="fas fa-bug mr-2"></i>Informations de Debug
            </h3>
            <div class="grid md:grid-cols-2 gap-4 text-sm">
                <div>
                    <p class="text-yellow-800"><strong>Mode Debug:</strong> {{ debug|yesno:"Activé,Désactivé" }}</p>
                    <p class="text-yellow-800"><strong>Session Active:</strong> {{ request.session.session_key|default:"Aucune" }}</p>
                    <p class="text-yellow-800"><strong>Utilisateur:</strong> {{ request.user.username|default:"Anonyme" }}</p>
                </div>
                <div>
                    <p class="text-yellow-800"><strong>Téléphone en session:</strong> {{ request.session.phone_number|default:"Aucun" }}</p>
                    <p class="text-yellow-800"><strong>Code en session:</strong> {{ request.session.verification_code|default:"Aucun" }}</p>
                    <p class="text-yellow-800"><strong>Email en session:</strong> {{ request.session.signup_email|default:"Aucun" }}</p>
                </div>
            </div>
        </div>

        <!-- Quick Links -->
        <div class="grid md:grid-cols-3 gap-4">
            <a href="{% url 'core:signup' %}"
               class="block bg-white border border-gray-200 rounded-lg p-4 hover:shadow-md transition-all text-center">
                <i class="fas fa-phone text-nestria-red text-2xl mb-2"></i>
                <h4 class="font-semibold text-gray-900">Inscription par Téléphone</h4>
                <p class="text-sm text-gray-600">Page de signup principale</p>
            </a>

            <a href="{% url 'core:modern_login' %}"
               class="block bg-white border border-gray-200 rounded-lg p-4 hover:shadow-md transition-all text-center">
                <i class="fas fa-shield-alt text-blue-500 text-2xl mb-2"></i>
                <h4 class="font-semibold text-gray-900">OAuth Demo</h4>
                <p class="text-sm text-gray-600">Tester Google/Facebook/Apple</p>
            </a>

            <a href="{% url 'core:onboarding' %}" 
               class="block bg-white border border-gray-200 rounded-lg p-4 hover:shadow-md transition-all text-center">
                <i class="fas fa-graduation-cap text-green-500 text-2xl mb-2"></i>
                <h4 class="font-semibold text-gray-900">Onboarding</h4>
                <p class="text-sm text-gray-600">Guide utilisateur</p>
            </a>
        </div>

        <!-- Clear Session Button -->
        <div class="text-center mt-8">
            <button onclick="clearSession()" 
                    class="bg-red-500 text-white px-6 py-2 rounded-lg hover:bg-red-600 transition-all">
                <i class="fas fa-trash mr-2"></i>Nettoyer la Session
            </button>
        </div>
    </div>

    <script>
        function clearSession() {
            if (confirm('Voulez-vous vraiment nettoyer la session ? Cela supprimera toutes les données de test.')) {
                fetch('/clear-session/', {
                    method: 'POST',
                    headers: {
                        'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert('Session nettoyée avec succès !');
                        window.location.reload();
                    } else {
                        alert('Erreur lors du nettoyage de la session.');
                    }
                })
                .catch(error => {
                    alert('Erreur lors du nettoyage de la session.');
                });
            }
        }
    </script>
</body>
</html>
