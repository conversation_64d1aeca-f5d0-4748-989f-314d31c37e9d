<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Nestria Signup Process Demo</title>
    {% load static %}
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'nestria-red': '#FF5A5F',
                    }
                }
            }
        }
    </script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- Header -->
    <header class="bg-white shadow-sm border-b">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <div class="flex items-center">
                    <a href="{% url 'core:home' %}" class="text-2xl font-bold text-nestria-red">
                        <i class="fas fa-home mr-2"></i>Nestria
                    </a>
                </div>
                <div class="flex items-center space-x-4">
                    <a href="{% url 'core:home' %}" class="text-sm text-gray-600 hover:text-gray-900">
                        Back to Home
                    </a>
                </div>
            </div>
        </div>
    </header>

    <!-- Demo Content -->
    <div class="max-w-6xl mx-auto px-4 py-12">
        <div class="text-center mb-12">
            <h1 class="text-4xl font-bold text-gray-900 mb-4">Nestria Signup Process Demo</h1>
            <p class="text-lg text-gray-600 max-w-3xl mx-auto">
                Découvrez le processus d'inscription complet avec vérification par WhatsApp, 
                exactement comme demandé dans vos spécifications.
            </p>
        </div>

        <!-- Process Flow -->
        <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
            <!-- Step 1: Phone Signup -->
            <div class="bg-white rounded-lg shadow-lg p-6 border-l-4 border-nestria-red">
                <div class="flex items-center mb-4">
                    <div class="w-10 h-10 bg-nestria-red text-white rounded-full flex items-center justify-center font-bold mr-3">1</div>
                    <h3 class="text-xl font-semibold text-gray-900">Phone Signup</h3>
                </div>
                <p class="text-gray-600 mb-4">
                    L'utilisateur saisit son numéro de téléphone pour commencer l'inscription.
                </p>
                <a href="{% url 'core:signup' %}"
                   class="inline-block bg-nestria-red text-white px-4 py-2 rounded-lg hover:bg-red-600 transition-all">
                    <i class="fas fa-phone mr-2"></i>Tester l'inscription par téléphone
                </a>
            </div>

            <!-- Step 2: Phone Verification -->
            <div class="bg-white rounded-lg shadow-lg p-6 border-l-4 border-blue-500">
                <div class="flex items-center mb-4">
                    <div class="w-10 h-10 bg-blue-500 text-white rounded-full flex items-center justify-center font-bold mr-3">2</div>
                    <h3 class="text-xl font-semibold text-gray-900">Verification</h3>
                </div>
                <p class="text-gray-600 mb-4">
                    Code de 6 chiffres envoyé automatiquement au WhatsApp du numéro saisi.
                </p>
                <a href="{% url 'core:phone_verification' %}" 
                   class="inline-block bg-blue-500 text-white px-4 py-2 rounded-lg hover:bg-blue-600 transition-all">
                    <i class="fab fa-whatsapp mr-2"></i>Voir la vérification
                </a>
            </div>

            <!-- Step 3: Contact Info -->
            <div class="bg-white rounded-lg shadow-lg p-6 border-l-4 border-green-500">
                <div class="flex items-center mb-4">
                    <div class="w-10 h-10 bg-green-500 text-white rounded-full flex items-center justify-center font-bold mr-3">3</div>
                    <h3 class="text-xl font-semibold text-gray-900">Contact Info</h3>
                </div>
                <p class="text-gray-600 mb-4">
                    Saisie de l'email et acceptation des conditions d'utilisation.
                </p>
                <a href="{% url 'core:contact_info' %}" 
                   class="inline-block bg-green-500 text-white px-4 py-2 rounded-lg hover:bg-green-600 transition-all">
                    <i class="fas fa-envelope mr-2"></i>Voir le formulaire
                </a>
            </div>

            <!-- Step 4: Finish Signup -->
            <div class="bg-white rounded-lg shadow-lg p-6 border-l-4 border-purple-500">
                <div class="flex items-center mb-4">
                    <div class="w-10 h-10 bg-purple-500 text-white rounded-full flex items-center justify-center font-bold mr-3">4</div>
                    <h3 class="text-xl font-semibold text-gray-900">Finish Signup</h3>
                </div>
                <p class="text-gray-600 mb-4">
                    Nom, prénom, date de naissance avec validation d'âge (18+ ans).
                </p>
                <a href="{% url 'core:finish_signup' %}" 
                   class="inline-block bg-purple-500 text-white px-4 py-2 rounded-lg hover:bg-purple-600 transition-all">
                    <i class="fas fa-user mr-2"></i>Finaliser l'inscription
                </a>
            </div>

            <!-- Step 5: Community Commitment -->
            <div class="bg-white rounded-lg shadow-lg p-6 border-l-4 border-yellow-500">
                <div class="flex items-center mb-4">
                    <div class="w-10 h-10 bg-yellow-500 text-white rounded-full flex items-center justify-center font-bold mr-3">5</div>
                    <h3 class="text-xl font-semibold text-gray-900">Community</h3>
                </div>
                <p class="text-gray-600 mb-4">
                    Engagement communautaire et acceptation des valeurs de Nestria.
                </p>
                <a href="{% url 'core:community_commitment' %}" 
                   class="inline-block bg-yellow-500 text-white px-4 py-2 rounded-lg hover:bg-yellow-600 transition-all">
                    <i class="fas fa-users mr-2"></i>Voir l'engagement
                </a>
            </div>

            <!-- Step 6: Onboarding -->
            <div class="bg-white rounded-lg shadow-lg p-6 border-l-4 border-indigo-500">
                <div class="flex items-center mb-4">
                    <div class="w-10 h-10 bg-indigo-500 text-white rounded-full flex items-center justify-center font-bold mr-3">6</div>
                    <h3 class="text-xl font-semibold text-gray-900">Onboarding</h3>
                </div>
                <p class="text-gray-600 mb-4">
                    Guide interactif pour comprendre le fonctionnement de Nestria.
                </p>
                <a href="{% url 'core:onboarding' %}" 
                   class="inline-block bg-indigo-500 text-white px-4 py-2 rounded-lg hover:bg-indigo-600 transition-all">
                    <i class="fas fa-graduation-cap mr-2"></i>Voir l'onboarding
                </a>
            </div>
        </div>

        <!-- Features Showcase -->
        <div class="bg-white rounded-lg shadow-lg p-8 mb-12">
            <h2 class="text-2xl font-bold text-gray-900 mb-6 text-center">Fonctionnalités Implémentées</h2>
            
            <div class="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
                <div class="text-center">
                    <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-3">
                        <i class="fab fa-whatsapp text-green-600 text-2xl"></i>
                    </div>
                    <h3 class="font-semibold text-gray-900 mb-2">Vérification WhatsApp</h3>
                    <p class="text-sm text-gray-600">Code de 6 chiffres envoyé automatiquement</p>
                </div>

                <div class="text-center">
                    <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-3">
                        <i class="fas fa-shield-alt text-blue-600 text-2xl"></i>
                    </div>
                    <h3 class="font-semibold text-gray-900 mb-2">Validation d'âge</h3>
                    <p class="text-sm text-gray-600">Vérification automatique 18+ ans</p>
                </div>

                <div class="text-center">
                    <div class="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-3">
                        <i class="fas fa-heart text-purple-600 text-2xl"></i>
                    </div>
                    <h3 class="font-semibold text-gray-900 mb-2">Wishlist Modal</h3>
                    <p class="text-sm text-gray-600">Création de listes de souhaits</p>
                </div>

                <div class="text-center">
                    <div class="w-16 h-16 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-3">
                        <i class="fas fa-graduation-cap text-orange-600 text-2xl"></i>
                    </div>
                    <h3 class="font-semibold text-gray-900 mb-2">Onboarding</h3>
                    <p class="text-sm text-gray-600">Guide interactif pour nouveaux utilisateurs</p>
                </div>
            </div>
        </div>

        <!-- Demo Instructions -->
        <div class="bg-blue-50 border border-blue-200 rounded-lg p-6">
            <h3 class="text-lg font-semibold text-blue-900 mb-3">
                <i class="fas fa-info-circle mr-2"></i>Instructions de Test
            </h3>
            <div class="space-y-2 text-blue-800">
                <p><strong>1.</strong> Cliquez sur "Tester l'inscription" pour commencer le processus</p>
                <p><strong>2.</strong> Utilisez le numéro +212 603999557 (ou tout autre numéro)</p>
                <p><strong>3.</strong> Le code de vérification sera affiché dans la console du serveur</p>
                <p><strong>4.</strong> Suivez chaque étape pour voir le processus complet</p>
                <p><strong>5.</strong> Pour la date de naissance, utilisez une date qui donne 18+ ans</p>
            </div>
        </div>

        <!-- Wishlist Demo Button -->
        <div class="text-center mt-8">
            <button onclick="openWishlistModal()" 
                    class="bg-nestria-red text-white px-6 py-3 rounded-lg font-semibold hover:bg-red-600 transition-all">
                <i class="fas fa-heart mr-2"></i>Tester la Modal Wishlist
            </button>
        </div>
    </div>

    <!-- Include Wishlist Modal -->
    {% include 'components/create_wishlist_modal.html' %}

    <script>
        // Demo functions
        function testPhoneSignup() {
            window.location.href = '{% url "core:signup" %}';
        }

        function testVerification() {
            window.location.href = '{% url "core:phone_verification" %}';
        }

        function testContactInfo() {
            window.location.href = '{% url "core:contact_info" %}';
        }

        function testFinishSignup() {
            window.location.href = '{% url "core:finish_signup" %}';
        }

        function testCommunityCommitment() {
            window.location.href = '{% url "core:community_commitment" %}';
        }

        function testOnboarding() {
            window.location.href = '{% url "core:onboarding" %}';
        }
    </script>
</body>
</html>
