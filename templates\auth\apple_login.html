<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sign in with Apple ID - Nestria</title>
    {% load static %}
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'apple-gray': '#1d1d1f',
                        'apple-light': '#f5f5f7',
                        'apple-blue': '#007aff',
                    }
                }
            }
        }
    </script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body class="bg-apple-light min-h-screen flex items-center justify-center p-4">
    <div class="bg-white w-full max-w-md rounded-xl shadow-lg p-8">
        <!-- Apple Logo -->
        <div class="text-center mb-6">
            <div class="inline-flex items-center space-x-3">
                <i class="fab fa-apple text-3xl text-apple-gray"></i>
                <span class="text-xl font-medium text-apple-gray">Apple ID</span>
            </div>
        </div>

        <!-- Header -->
        <div class="text-center mb-8">
            <h1 class="text-2xl font-semibold text-apple-gray mb-2">Sign in with your Apple ID</h1>
            <p class="text-sm text-gray-600">to continue to Nestria</p>
        </div>

        <!-- Login Form -->
        <form onsubmit="handleAppleLogin(event)" class="space-y-6">
            <!-- Apple ID Input -->
            <div>
                <label for="apple-id" class="block text-sm font-medium text-apple-gray mb-2">
                    Apple ID
                </label>
                <input type="email" 
                       id="apple-id"
                       placeholder="<EMAIL>"
                       class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-apple-blue focus:border-transparent transition-all text-base"
                       required>
            </div>

            <!-- Password Input -->
            <div>
                <label for="apple-password" class="block text-sm font-medium text-apple-gray mb-2">
                    Password
                </label>
                <div class="relative">
                    <input type="password" 
                           id="apple-password"
                           placeholder="Password"
                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-apple-blue focus:border-transparent transition-all text-base pr-12"
                           required>
                    <button type="button" onclick="toggleApplePassword()" 
                            class="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700">
                        <i class="fas fa-eye" id="apple-password-eye"></i>
                    </button>
                </div>
            </div>

            <!-- Remember Me -->
            <div class="flex items-center">
                <input type="checkbox" 
                       id="remember-me"
                       class="w-4 h-4 text-apple-blue bg-gray-100 border-gray-300 rounded focus:ring-apple-blue">
                <label for="remember-me" class="ml-2 text-sm text-apple-gray">
                    Keep me signed in
                </label>
            </div>

            <!-- Sign In Button -->
            <button type="submit" 
                    class="w-full bg-apple-blue text-white py-3 rounded-lg font-semibold hover:bg-blue-600 transition-all text-base">
                Sign In
            </button>
        </form>

        <!-- Forgot Password -->
        <div class="text-center mt-6">
            <a href="#" onclick="forgotApplePassword()" class="text-apple-blue hover:underline text-sm">
                Forgot Apple ID or password?
            </a>
        </div>

        <!-- Divider -->
        <div class="relative my-8">
            <div class="absolute inset-0 flex items-center">
                <div class="w-full border-t border-gray-300"></div>
            </div>
            <div class="relative flex justify-center text-sm">
                <span class="px-2 bg-white text-gray-500">or</span>
            </div>
        </div>

        <!-- Create Apple ID -->
        <div class="text-center">
            <button onclick="createAppleID()" 
                    class="text-apple-blue hover:underline font-medium">
                Create Your Apple ID
            </button>
        </div>

        <!-- Security Notice -->
        <div class="bg-gray-50 rounded-lg p-4 mt-6">
            <div class="flex items-start space-x-3">
                <i class="fas fa-shield-alt text-apple-blue mt-1"></i>
                <div>
                    <h4 class="text-sm font-medium text-apple-gray mb-1">Your privacy is protected</h4>
                    <p class="text-xs text-gray-600">
                        Apple doesn't track you across apps and websites. Your personal information stays private.
                    </p>
                </div>
            </div>
        </div>

        <!-- Language Selector -->
        <div class="text-center mt-6">
            <select class="text-sm text-gray-600 border-none bg-transparent focus:outline-none">
                <option value="en">English</option>
                <option value="fr">Français</option>
                <option value="es">Español</option>
                <option value="ar">العربية</option>
            </select>
        </div>
    </div>

    <script>
        function handleAppleLogin(event) {
            event.preventDefault();
            
            const appleId = document.getElementById('apple-id').value;
            const password = document.getElementById('apple-password').value;
            const rememberMe = document.getElementById('remember-me').checked;
            
            if (!appleId || !password) {
                alert('Please enter both Apple ID and password');
                return;
            }
            
            // Simuler la connexion Apple
            alert(`Signing in with Apple ID...\n\nApple ID: ${appleId}\nPassword: ${'*'.repeat(password.length)}\nRemember me: ${rememberMe ? 'Yes' : 'No'}\n\nThis would normally authenticate with Apple and redirect to the app.`);
            
            // En production, ceci ferait l'authentification réelle
            // window.location.href = `/auth/apple/callback/`;
            
            // Pour la démo, rediriger vers contact-info
            window.location.href = '/contact-info/';
        }

        function toggleApplePassword() {
            const input = document.getElementById('apple-password');
            const eye = document.getElementById('apple-password-eye');
            
            if (input.type === 'password') {
                input.type = 'text';
                eye.classList.remove('fa-eye');
                eye.classList.add('fa-eye-slash');
            } else {
                input.type = 'password';
                eye.classList.remove('fa-eye-slash');
                eye.classList.add('fa-eye');
            }
        }

        function forgotApplePassword() {
            const appleId = prompt('Enter your Apple ID to reset your password:');
            if (appleId && appleId.includes('@')) {
                alert(`Password reset instructions sent to ${appleId}\n\nCheck your email and follow the instructions to reset your password.`);
            }
        }

        function createAppleID() {
            alert('Create Your Apple ID\n\nThis would redirect to Apple\'s account creation page.');
            // window.location.href = 'https://appleid.apple.com/account';
        }

        // Animation d'entrée
        document.addEventListener('DOMContentLoaded', function() {
            const container = document.querySelector('.bg-white');
            container.style.opacity = '0';
            container.style.transform = 'translateY(20px)';

            setTimeout(() => {
                container.style.transition = 'all 0.6s ease-out';
                container.style.opacity = '1';
                container.style.transform = 'translateY(0)';
            }, 150);
        });
    </script>
</body>
</html>
