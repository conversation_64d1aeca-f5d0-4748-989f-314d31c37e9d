from django.core.management.base import BaseCommand
from django.contrib.auth.models import User
from rooms.models import Room, RoomType, Amenity, RoomImage, Review
from decimal import Decimal
import random


class Command(BaseCommand):
    help = 'Populate the database with sample data'

    def handle(self, *args, **options):
        self.stdout.write('Creating sample data...')
        
        # Create room types
        room_types_data = [
            {'name': 'Entire place', 'description': 'Have a place to yourself'},
            {'name': 'Private room', 'description': 'Your own room in a home or hotel'},
            {'name': 'Shared room', 'description': 'Sleep in a room or common area that may be shared with others'},
        ]
        
        for rt_data in room_types_data:
            room_type, created = RoomType.objects.get_or_create(
                name=rt_data['name'],
                defaults={'description': rt_data['description']}
            )
            if created:
                self.stdout.write(f'Created room type: {room_type.name}')
        
        # Create amenities
        amenities_data = [
            {'name': 'WiFi', 'icon': 'fas fa-wifi'},
            {'name': 'Kitchen', 'icon': 'fas fa-utensils'},
            {'name': 'Air conditioning', 'icon': 'fas fa-snowflake'},
            {'name': 'Pool', 'icon': 'fas fa-swimming-pool'},
            {'name': 'Parking', 'icon': 'fas fa-parking'},
            {'name': 'TV', 'icon': 'fas fa-tv'},
            {'name': 'Washing machine', 'icon': 'fas fa-tshirt'},
            {'name': 'Hot tub', 'icon': 'fas fa-hot-tub'},
            {'name': 'Gym', 'icon': 'fas fa-dumbbell'},
            {'name': 'Pet friendly', 'icon': 'fas fa-paw'},
        ]
        
        for amenity_data in amenities_data:
            amenity, created = Amenity.objects.get_or_create(
                name=amenity_data['name'],
                defaults={'icon': amenity_data['icon']}
            )
            if created:
                self.stdout.write(f'Created amenity: {amenity.name}')
        
        # Create sample users (hosts)
        hosts_data = [
            {'username': 'john_host', 'email': '<EMAIL>', 'first_name': 'John', 'last_name': 'Smith'},
            {'username': 'sarah_host', 'email': '<EMAIL>', 'first_name': 'Sarah', 'last_name': 'Johnson'},
            {'username': 'mike_host', 'email': '<EMAIL>', 'first_name': 'Mike', 'last_name': 'Brown'},
            {'username': 'emma_host', 'email': '<EMAIL>', 'first_name': 'Emma', 'last_name': 'Davis'},
        ]
        
        hosts = []
        for host_data in hosts_data:
            host, created = User.objects.get_or_create(
                username=host_data['username'],
                defaults={
                    'email': host_data['email'],
                    'first_name': host_data['first_name'],
                    'last_name': host_data['last_name'],
                }
            )
            if created:
                host.set_password('password123')
                host.save()
                self.stdout.write(f'Created host: {host.username}')
            hosts.append(host)
        
        # Create sample rooms
        rooms_data = [
            {
                'title': 'Cozy Downtown Apartment',
                'description': 'Beautiful apartment in the heart of the city with amazing views and modern amenities.',
                'city': 'New York',
                'country': 'United States',
                'address': '123 Broadway St',
                'max_guests': 4,
                'bedrooms': 2,
                'beds': 2,
                'bathrooms': 1.5,
                'price_per_night': Decimal('150.00'),
                'cleaning_fee': Decimal('25.00'),
            },
            {
                'title': 'Beachfront Villa',
                'description': 'Stunning villa right on the beach with private access and breathtaking ocean views.',
                'city': 'Miami',
                'country': 'United States',
                'address': '456 Ocean Drive',
                'max_guests': 8,
                'bedrooms': 4,
                'beds': 4,
                'bathrooms': 3.0,
                'price_per_night': Decimal('350.00'),
                'cleaning_fee': Decimal('75.00'),
            },
            {
                'title': 'Mountain Cabin Retreat',
                'description': 'Peaceful cabin in the mountains, perfect for a relaxing getaway surrounded by nature.',
                'city': 'Aspen',
                'country': 'United States',
                'address': '789 Mountain View Rd',
                'max_guests': 6,
                'bedrooms': 3,
                'beds': 3,
                'bathrooms': 2.0,
                'price_per_night': Decimal('200.00'),
                'cleaning_fee': Decimal('40.00'),
            },
            {
                'title': 'Modern Loft in Arts District',
                'description': 'Stylish loft in the trendy arts district with exposed brick and industrial design.',
                'city': 'Los Angeles',
                'country': 'United States',
                'address': '321 Arts District Blvd',
                'max_guests': 2,
                'bedrooms': 1,
                'beds': 1,
                'bathrooms': 1.0,
                'price_per_night': Decimal('120.00'),
                'cleaning_fee': Decimal('20.00'),
            },
            {
                'title': 'Historic Brownstone',
                'description': 'Charming historic brownstone with original details and modern updates.',
                'city': 'Boston',
                'country': 'United States',
                'address': '654 Beacon Hill St',
                'max_guests': 5,
                'bedrooms': 3,
                'beds': 3,
                'bathrooms': 2.5,
                'price_per_night': Decimal('180.00'),
                'cleaning_fee': Decimal('35.00'),
            },
            {
                'title': 'Luxury Penthouse',
                'description': 'Exclusive penthouse with panoramic city views and premium amenities.',
                'city': 'Chicago',
                'country': 'United States',
                'address': '987 Michigan Ave',
                'max_guests': 6,
                'bedrooms': 3,
                'beds': 3,
                'bathrooms': 3.0,
                'price_per_night': Decimal('400.00'),
                'cleaning_fee': Decimal('80.00'),
            },
        ]
        
        room_types = list(RoomType.objects.all())
        amenities = list(Amenity.objects.all())
        
        for i, room_data in enumerate(rooms_data):
            room, created = Room.objects.get_or_create(
                title=room_data['title'],
                defaults={
                    **room_data,
                    'host': hosts[i % len(hosts)],
                    'room_type': random.choice(room_types),
                    'instant_book': random.choice([True, False]),
                    'service_fee': Decimal('15.00'),
                }
            )
            
            if created:
                # Add random amenities
                room_amenities = random.sample(amenities, k=random.randint(3, 7))
                room.amenities.set(room_amenities)
                
                self.stdout.write(f'Created room: {room.title}')
                
                # Create sample reviews
                review_users = random.sample(hosts, k=min(3, len(hosts)))
                for review_user in review_users:
                    if review_user != room.host:  # Don't let hosts review their own rooms
                        Review.objects.get_or_create(
                            room=room,
                            user=review_user,
                            defaults={
                                'rating': random.randint(4, 5),
                                'comment': f'Great stay at {room.title}! Highly recommended.',
                            }
                        )
        
        self.stdout.write(self.style.SUCCESS('Successfully populated database with sample data!'))
        self.stdout.write('You can now log in to the admin at /admin/ with username: admin, password: admin123')
        self.stdout.write('Or create a new account on the website.')
