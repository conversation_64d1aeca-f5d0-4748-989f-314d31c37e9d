"""
Email service module for Nestria
Handles all email sending functionality with proper error handling and templates
"""

import logging
from django.core.mail import EmailMultiAlternatives
from django.template.loader import render_to_string
from django.conf import settings
from django.utils.html import strip_tags
from typing import Optional, List, Dict, Any

logger = logging.getLogger(__name__)


class EmailService:
    """
    Service class for sending emails with HTML templates and error handling
    """
    
    @staticmethod
    def send_mail(
        to: str | List[str],
        subject: str,
        template_name: str = None,
        context: Dict[str, Any] = None,
        text_content: str = None,
        html_content: str = None,
        from_email: str = None,
        fail_silently: bool = False
    ) -> bool:
        """
        Send email with HTML template support
        
        Args:
            to: Email address(es) of recipient(s)
            subject: Email subject
            template_name: Name of HTML template (without .html extension)
            context: Context data for template rendering
            text_content: Plain text content (optional if template provided)
            html_content: HTML content (optional if template provided)
            from_email: Sender email (uses default if not provided)
            fail_silently: Whether to suppress exceptions
            
        Returns:
            bool: True if email sent successfully, False otherwise
        """
        try:
            # Ensure to is a list
            if isinstance(to, str):
                to = [to]
            
            # Use default from_email if not provided
            if not from_email:
                from_email = getattr(settings, 'DEFAULT_FROM_EMAIL', '<EMAIL>')
            
            # Prepare context
            if context is None:
                context = {}
            
            # Add common context variables
            context.update({
                'site_name': 'Nestria',
                'site_url': getattr(settings, 'SITE_URL', 'http://localhost:8000'),
                'support_email': getattr(settings, 'SUPPORT_EMAIL', '<EMAIL>'),
            })
            
            # Generate content from template if provided
            if template_name:
                try:
                    html_content = render_to_string(f'emails/{template_name}.html', context)
                    # Generate text version from HTML if text_content not provided
                    if not text_content:
                        text_content = strip_tags(html_content)
                except Exception as e:
                    logger.error(f"Error rendering email template {template_name}: {e}")
                    if not fail_silently:
                        raise
                    return False
            
            # Ensure we have content
            if not text_content and not html_content:
                logger.error("No email content provided")
                if not fail_silently:
                    raise ValueError("Either text_content, html_content, or template_name must be provided")
                return False
            
            # Create email message
            email = EmailMultiAlternatives(
                subject=subject,
                body=text_content or strip_tags(html_content),
                from_email=from_email,
                to=to
            )
            
            # Attach HTML content if available
            if html_content:
                email.attach_alternative(html_content, "text/html")
            
            # Send email
            email.send()
            
            logger.info(f"Email sent successfully to {', '.join(to)}: {subject}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to send email to {to}: {e}")
            if not fail_silently:
                raise
            return False
    
    @staticmethod
    def send_welcome_email(user_email: str, user_name: str) -> bool:
        """Send welcome email to new users"""
        return EmailService.send_mail(
            to=user_email,
            subject="Welcome to Nestria - Your Adventure Begins!",
            template_name="welcome",
            context={
                'user_name': user_name,
                'login_url': f"{getattr(settings, 'SITE_URL', 'http://localhost:8000')}/login/",
            }
        )
    
    @staticmethod
    def send_booking_confirmation(reservation) -> bool:
        """Send booking confirmation email"""
        return EmailService.send_mail(
            to=reservation.guest_email,
            subject=f"Booking Confirmed - {reservation.room.title}",
            template_name="booking_confirmed",
            context={
                'reservation': reservation,
                'guest_name': f"{reservation.guest_first_name} {reservation.guest_last_name}",
                'property_url': f"{getattr(settings, 'SITE_URL', 'http://localhost:8000')}{reservation.room.get_absolute_url()}",
            }
        )
    
    @staticmethod
    def send_password_reset_email(user_email: str, reset_url: str) -> bool:
        """Send password reset email"""
        return EmailService.send_mail(
            to=user_email,
            subject="Reset Your Nestria Password",
            template_name="password_reset",
            context={
                'reset_url': reset_url,
            }
        )
    
    @staticmethod
    def send_contact_form_email(name: str, email: str, message: str) -> bool:
        """Send contact form submission email"""
        return EmailService.send_mail(
            to=getattr(settings, 'CONTACT_EMAIL', '<EMAIL>'),
            subject=f"Contact Form Submission from {name}",
            template_name="contact_form",
            context={
                'name': name,
                'email': email,
                'message': message,
            }
        )


# Convenience functions for backward compatibility
def send_mail(to: str, subject: str, text: str, html: str = None) -> bool:
    """
    Simple email sending function
    
    Args:
        to: Recipient email address
        subject: Email subject
        text: Plain text content
        html: HTML content (optional)
        
    Returns:
        bool: True if sent successfully, False otherwise
    """
    return EmailService.send_mail(
        to=to,
        subject=subject,
        text_content=text,
        html_content=html
    )


# Example usage and test function
def test_email_service():
    """Test the email service with a sample email"""
    try:
        success = EmailService.send_mail(
            to="<EMAIL>",
            subject="Test Email from Nestria",
            text_content="This is a test email from the Nestria email service.",
            html_content="<h1>Test Email</h1><p>This is a <strong>test email</strong> from the Nestria email service.</p>"
        )
        
        if success:
            print("✅ Test email sent successfully!")
        else:
            print("❌ Failed to send test email")
            
        return success
        
    except Exception as e:
        print(f"❌ Error testing email service: {e}")
        return False


if __name__ == "__main__":
    # Test the email service
    test_email_service()
