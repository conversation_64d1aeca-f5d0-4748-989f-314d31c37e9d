{% extends 'base.html' %}
{% load static %}

{% block title %}Booking Confirmed - Airbnb Clone{% endblock %}

{% block content %}
<div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Success Header -->
    <div class="text-center mb-8">
        <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <i class="fas fa-check text-green-600 text-2xl"></i>
        </div>
        <h1 class="text-3xl font-bold text-gray-900 mb-2">Booking confirmed!</h1>
        <p class="text-lg text-gray-600">Your reservation has been successfully created.</p>
    </div>

    <!-- Reservation Details -->
    <div class="bg-white border border-gray-200 rounded-lg p-6 mb-8">
        <h2 class="text-xl font-semibold text-gray-900 mb-6">Reservation details</h2>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
            <!-- Property Info -->
            <div>
                <div class="flex space-x-4 mb-6">
                    <div class="flex-shrink-0">
                        {% if reservation.room.main_image %}
                            <img src="{{ reservation.room.main_image.url }}" alt="{{ reservation.room.title }}" class="w-24 h-24 object-cover rounded-lg">
                        {% else %}
                            <div class="w-24 h-24 bg-gray-200 rounded-lg flex items-center justify-center">
                                <i class="fas fa-home text-gray-400 text-xl"></i>
                            </div>
                        {% endif %}
                    </div>
                    <div>
                        <h3 class="font-semibold text-gray-900 mb-1">{{ reservation.room.title }}</h3>
                        <p class="text-sm text-gray-600 mb-2">{{ reservation.room.room_type.name }}</p>
                        <p class="text-sm text-gray-600">{{ reservation.room.city }}, {{ reservation.room.country }}</p>
                        {% if reservation.room.average_rating > 0 %}
                            <div class="flex items-center text-sm mt-2">
                                <i class="fas fa-star text-airbnb-red"></i>
                                <span class="ml-1 font-medium">{{ reservation.room.average_rating|floatformat:1 }}</span>
                                <span class="ml-1 text-gray-600">({{ reservation.room.review_count }} reviews)</span>
                            </div>
                        {% endif %}
                    </div>
                </div>
                
                <!-- Host Info -->
                <div class="border-t border-gray-200 pt-4">
                    <h4 class="font-medium text-gray-900 mb-2">Your host</h4>
                    <div class="flex items-center space-x-3">
                        <div class="w-10 h-10 bg-gray-300 rounded-full flex items-center justify-center">
                            <i class="fas fa-user text-gray-600"></i>
                        </div>
                        <div>
                            <p class="font-medium text-gray-900">{{ reservation.room.host.first_name|default:reservation.room.host.username }}</p>
                            <p class="text-sm text-gray-600">Host since {{ reservation.room.host.date_joined|date:"Y" }}</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Trip Details -->
            <div>
                <div class="space-y-4">
                    <div>
                        <h4 class="font-medium text-gray-900 mb-2">Check-in</h4>
                        <p class="text-gray-700">{{ reservation.check_in|date:"l, F d, Y" }}</p>
                        <p class="text-sm text-gray-500">After 3:00 PM</p>
                    </div>
                    
                    <div>
                        <h4 class="font-medium text-gray-900 mb-2">Check-out</h4>
                        <p class="text-gray-700">{{ reservation.check_out|date:"l, F d, Y" }}</p>
                        <p class="text-sm text-gray-500">Before 11:00 AM</p>
                    </div>
                    
                    <div>
                        <h4 class="font-medium text-gray-900 mb-2">Guests</h4>
                        <p class="text-gray-700">{{ reservation.total_guests }} guest{{ reservation.total_guests|pluralize }}</p>
                        {% if reservation.adults > 0 %}
                            <p class="text-sm text-gray-500">{{ reservation.adults }} adult{{ reservation.adults|pluralize }}</p>
                        {% endif %}
                        {% if reservation.children > 0 %}
                            <p class="text-sm text-gray-500">{{ reservation.children }} child{{ reservation.children|pluralize }}</p>
                        {% endif %}
                        {% if reservation.infants > 0 %}
                            <p class="text-sm text-gray-500">{{ reservation.infants }} infant{{ reservation.infants|pluralize }}</p>
                        {% endif %}
                    </div>
                    
                    <div>
                        <h4 class="font-medium text-gray-900 mb-2">Reservation number</h4>
                        <p class="text-gray-700 font-mono">#{{ reservation.id|stringformat:"06d" }}</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Payment Summary -->
    <div class="bg-white border border-gray-200 rounded-lg p-6 mb-8">
        <h2 class="text-xl font-semibold text-gray-900 mb-6">Payment summary</h2>
        
        <div class="space-y-3">
            <div class="flex justify-between">
                <span>${{ reservation.price_per_night|floatformat:0 }} x {{ reservation.nights }} night{{ reservation.nights|pluralize }}</span>
                <span>${{ reservation.base_price|floatformat:0 }}</span>
            </div>
            
            {% if reservation.cleaning_fee > 0 %}
                <div class="flex justify-between">
                    <span>Cleaning fee</span>
                    <span>${{ reservation.cleaning_fee|floatformat:0 }}</span>
                </div>
            {% endif %}
            
            {% if reservation.service_fee > 0 %}
                <div class="flex justify-between">
                    <span>Service fee</span>
                    <span>${{ reservation.service_fee|floatformat:0 }}</span>
                </div>
            {% endif %}
            
            <div class="border-t border-gray-200 pt-3">
                <div class="flex justify-between font-semibold text-lg">
                    <span>Total paid</span>
                    <span>${{ reservation.total_price|floatformat:0 }}</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Special Requests -->
    {% if reservation.special_requests %}
        <div class="bg-white border border-gray-200 rounded-lg p-6 mb-8">
            <h2 class="text-xl font-semibold text-gray-900 mb-4">Special requests</h2>
            <p class="text-gray-700">{{ reservation.special_requests }}</p>
        </div>
    {% endif %}

    <!-- Next Steps -->
    <div class="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-8">
        <h2 class="text-xl font-semibold text-gray-900 mb-4">What's next?</h2>
        <div class="space-y-3 text-gray-700">
            <div class="flex items-start space-x-3">
                <i class="fas fa-envelope text-blue-600 mt-1"></i>
                <div>
                    <p class="font-medium">Confirmation email sent</p>
                    <p class="text-sm text-gray-600">We've sent a confirmation email with all the details to your email address.</p>
                </div>
            </div>
            <div class="flex items-start space-x-3">
                <i class="fas fa-comments text-blue-600 mt-1"></i>
                <div>
                    <p class="font-medium">Contact your host</p>
                    <p class="text-sm text-gray-600">You can message your host about check-in details or any questions.</p>
                </div>
            </div>
            <div class="flex items-start space-x-3">
                <i class="fas fa-calendar-check text-blue-600 mt-1"></i>
                <div>
                    <p class="font-medium">Prepare for your trip</p>
                    <p class="text-sm text-gray-600">Check the house rules and what to bring for your stay.</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Action Buttons -->
    <div class="flex flex-col sm:flex-row gap-4 justify-center">
        <a href="{% url 'reservations:download_pdf' reservation.id %}" class="inline-block bg-gradient-to-r from-blue-600 to-blue-700 text-white px-6 py-3 rounded-lg font-medium hover:from-blue-700 hover:to-blue-800 transition-all transform hover:scale-105 shadow-lg text-center">
            <i class="fas fa-download mr-2"></i>
            Download PDF Confirmation
        </a>
        <a href="{% url 'reservations:my_trips' %}" class="inline-block bg-airbnb-red text-white px-6 py-3 rounded-lg font-medium hover:bg-red-600 transition-colors btn-primary text-center">
            <i class="fas fa-suitcase mr-2"></i>
            View your trips
        </a>
        <a href="{% url 'core:home' %}" class="inline-block border border-gray-300 text-gray-700 px-6 py-3 rounded-lg font-medium hover:bg-gray-50 transition-colors text-center">
            <i class="fas fa-search mr-2"></i>
            Continue exploring
        </a>
    </div>
</div>
{% endblock %}
