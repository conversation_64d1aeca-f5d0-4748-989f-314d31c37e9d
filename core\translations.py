# Simple translation system for Nestria
# This provides basic translations for the main interface elements

TRANSLATIONS = {
    'en': {
        # Navigation
        'home': 'Home',
        'destinations': 'Destinations',
        'experiences': 'Experiences',
        'online': 'Online',
        'settings': 'Settings',
        'login': 'Login',
        'signup': 'Sign Up',
        'logout': 'Logout',
        
        # Home page
        'welcome_title': 'Find your next adventure',
        'welcome_subtitle': 'Discover unique places to stay and experiences around the world',
        'search_placeholder': 'Where are you going?',
        'search_button': 'Search',
        'featured_stays': 'Featured stays',
        'popular_destinations': 'Discover our most popular destinations',
        'explore_destinations': 'Explore amazing destinations',
        'destinations_subtitle': 'Discover the world\'s most beautiful and historic places',
        
        # Experiences
        'experience_categories': 'Experience Categories',
        'featured_experiences': 'Featured Experiences',
        'food_drink': 'Food & Drink',
        'adventure': 'Adventure',
        'arts_culture': 'Arts & Culture',
        'photography': 'Photography',
        'book_now': 'Book Now',
        'per_person': 'per person',
        
        # Online
        'online_experiences': 'Online Experiences',
        'virtual_experiences': 'Virtual Experiences',
        'join_live': 'Join Live',
        'book_tour': 'Book Tour',
        'join_session': 'Join Session',
        
        # Settings
        'profile_information': 'Profile Information',
        'notification_preferences': 'Notification Preferences',
        'language_region': 'Language & Region',
        'privacy_security': 'Privacy & Security',
        'save_changes': 'Save Changes',
        'apply': 'Apply',
        
        # Forms
        'first_name': 'First Name',
        'last_name': 'Last Name',
        'email': 'Email Address',
        'username': 'Username',
        'password': 'Password',
        'confirm_password': 'Confirm Password',
        'age': 'Age',
        'phone_number': 'Phone Number',
        'city': 'City',
        'country': 'Country',
        
        # Messages
        'welcome_message': 'Welcome to Nestria!',
        'booking_confirmed': 'Booking confirmed!',
        'language_changed': 'Language changed successfully',
        'profile_updated': 'Profile updated successfully',
        'error_occurred': 'An error occurred',
    },
    
    'fr': {
        # Navigation
        'home': 'Accueil',
        'destinations': 'Destinations',
        'experiences': 'Expériences',
        'online': 'En ligne',
        'settings': 'Paramètres',
        'login': 'Connexion',
        'signup': 'Inscription',
        'logout': 'Déconnexion',
        
        # Home page
        'welcome_title': 'Trouvez votre prochaine aventure',
        'welcome_subtitle': 'Découvrez des lieux uniques où séjourner et des expériences dans le monde entier',
        'search_placeholder': 'Où allez-vous ?',
        'search_button': 'Rechercher',
        'featured_stays': 'Séjours en vedette',
        'popular_destinations': 'Découvrez nos destinations les plus populaires',
        'explore_destinations': 'Explorez des destinations incroyables',
        'destinations_subtitle': 'Découvrez les plus beaux lieux historiques du monde',
        
        # Experiences
        'experience_categories': 'Catégories d\'expériences',
        'featured_experiences': 'Expériences en vedette',
        'food_drink': 'Gastronomie',
        'adventure': 'Aventure',
        'arts_culture': 'Arts et Culture',
        'photography': 'Photographie',
        'book_now': 'Réserver',
        'per_person': 'par personne',
        
        # Online
        'online_experiences': 'Expériences en ligne',
        'virtual_experiences': 'Expériences virtuelles',
        'join_live': 'Rejoindre en direct',
        'book_tour': 'Réserver la visite',
        'join_session': 'Rejoindre la session',
        
        # Settings
        'profile_information': 'Informations du profil',
        'notification_preferences': 'Préférences de notification',
        'language_region': 'Langue et région',
        'privacy_security': 'Confidentialité et sécurité',
        'save_changes': 'Enregistrer les modifications',
        'apply': 'Appliquer',
        
        # Forms
        'first_name': 'Prénom',
        'last_name': 'Nom',
        'email': 'Adresse e-mail',
        'username': 'Nom d\'utilisateur',
        'password': 'Mot de passe',
        'confirm_password': 'Confirmer le mot de passe',
        'age': 'Âge',
        'phone_number': 'Numéro de téléphone',
        'city': 'Ville',
        'country': 'Pays',
        
        # Messages
        'welcome_message': 'Bienvenue sur Nestria !',
        'booking_confirmed': 'Réservation confirmée !',
        'language_changed': 'Langue modifiée avec succès',
        'profile_updated': 'Profil mis à jour avec succès',
        'error_occurred': 'Une erreur s\'est produite',
    },
    
    'es': {
        # Navigation
        'home': 'Inicio',
        'destinations': 'Destinos',
        'experiences': 'Experiencias',
        'online': 'En línea',
        'settings': 'Configuración',
        'login': 'Iniciar sesión',
        'signup': 'Registrarse',
        'logout': 'Cerrar sesión',
        
        # Home page
        'welcome_title': 'Encuentra tu próxima aventura',
        'welcome_subtitle': 'Descubre lugares únicos para quedarte y experiencias alrededor del mundo',
        'search_placeholder': '¿A dónde vas?',
        'search_button': 'Buscar',
        'featured_stays': 'Estancias destacadas',
        'popular_destinations': 'Descubre nuestros destinos más populares',
        'explore_destinations': 'Explora destinos increíbles',
        'destinations_subtitle': 'Descubre los lugares más hermosos e históricos del mundo',
        
        # Experiences
        'experience_categories': 'Categorías de experiencias',
        'featured_experiences': 'Experiencias destacadas',
        'food_drink': 'Comida y Bebida',
        'adventure': 'Aventura',
        'arts_culture': 'Arte y Cultura',
        'photography': 'Fotografía',
        'book_now': 'Reservar ahora',
        'per_person': 'por persona',
        
        # Online
        'online_experiences': 'Experiencias en línea',
        'virtual_experiences': 'Experiencias virtuales',
        'join_live': 'Unirse en vivo',
        'book_tour': 'Reservar tour',
        'join_session': 'Unirse a la sesión',
        
        # Settings
        'profile_information': 'Información del perfil',
        'notification_preferences': 'Preferencias de notificación',
        'language_region': 'Idioma y región',
        'privacy_security': 'Privacidad y seguridad',
        'save_changes': 'Guardar cambios',
        'apply': 'Aplicar',
        
        # Forms
        'first_name': 'Nombre',
        'last_name': 'Apellido',
        'email': 'Dirección de correo',
        'username': 'Nombre de usuario',
        'password': 'Contraseña',
        'confirm_password': 'Confirmar contraseña',
        'age': 'Edad',
        'phone_number': 'Número de teléfono',
        'city': 'Ciudad',
        'country': 'País',
        
        # Messages
        'welcome_message': '¡Bienvenido a Nestria!',
        'booking_confirmed': '¡Reserva confirmada!',
        'language_changed': 'Idioma cambiado exitosamente',
        'profile_updated': 'Perfil actualizado exitosamente',
        'error_occurred': 'Ocurrió un error',
    },

    'pt': {
        # Navigation
        'home': 'Início',
        'destinations': 'Destinos',
        'experiences': 'Experiências',
        'online': 'Online',
        'settings': 'Configurações',
        'login': 'Entrar',
        'signup': 'Cadastrar',
        'logout': 'Sair',

        # Home page
        'welcome_title': 'Encontre sua próxima aventura',
        'welcome_subtitle': 'Descubra lugares únicos para ficar e experiências ao redor do mundo',
        'search_placeholder': 'Para onde você vai?',
        'search_button': 'Buscar',
        'featured_stays': 'Estadias em destaque',
        'popular_destinations': 'Descubra nossos destinos mais populares',
        'explore_destinations': 'Explore destinos incríveis',
        'destinations_subtitle': 'Descubra os lugares mais belos e históricos do mundo',

        # Experiences
        'experience_categories': 'Categorias de experiências',
        'featured_experiences': 'Experiências em destaque',
        'food_drink': 'Comida e Bebida',
        'adventure': 'Aventura',
        'arts_culture': 'Arte e Cultura',
        'photography': 'Fotografia',
        'book_now': 'Reservar agora',
        'per_person': 'por pessoa',

        # Online
        'online_experiences': 'Experiências online',
        'virtual_experiences': 'Experiências virtuais',
        'join_live': 'Participar ao vivo',
        'book_tour': 'Reservar tour',
        'join_session': 'Participar da sessão',

        # Settings
        'profile_information': 'Informações do perfil',
        'notification_preferences': 'Preferências de notificação',
        'language_region': 'Idioma e região',
        'privacy_security': 'Privacidade e segurança',
        'save_changes': 'Salvar alterações',
        'apply': 'Aplicar',

        # Forms
        'first_name': 'Nome',
        'last_name': 'Sobrenome',
        'email': 'Endereço de email',
        'username': 'Nome de usuário',
        'password': 'Senha',
        'confirm_password': 'Confirmar senha',
        'age': 'Idade',
        'phone_number': 'Número de telefone',
        'city': 'Cidade',
        'country': 'País',

        # Messages
        'welcome_message': 'Bem-vindo ao Nestria!',
        'booking_confirmed': 'Reserva confirmada!',
        'language_changed': 'Idioma alterado com sucesso',
        'profile_updated': 'Perfil atualizado com sucesso',
        'error_occurred': 'Ocorreu um erro',
    }
}

def get_translation(key, language='en'):
    """Get translation for a key in the specified language"""
    return TRANSLATIONS.get(language, TRANSLATIONS['en']).get(key, key)

def get_all_translations(language='en'):
    """Get all translations for a language"""
    return TRANSLATIONS.get(language, TRANSLATIONS['en'])
