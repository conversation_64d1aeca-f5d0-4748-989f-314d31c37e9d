#!/usr/bin/env python
"""
Démonstration du système de changement de numéro de téléphone
"""
import os
import django
import time

# Configuration Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'airbnb_clone.settings')
django.setup()

from core.services.whatsapp_free_service import send_whatsapp_verification_free
from core.views import generate_verification_code

def demo_changement_numero():
    """Démonstration complète du système de changement de numéro"""
    
    print("🎭 DÉMONSTRATION - Système de Changement de Numéro")
    print("=" * 60)
    
    # Simulation du flux complet
    print("\n📱 Scénario : Utilisateur veut changer son numéro de téléphone")
    print("-" * 50)
    
    # Étape 1 : Numéro initial
    numero_initial = "+212603999557"
    print(f"\n1️⃣ Numéro initial : {numero_initial}")
    
    # Génération du premier code
    code_initial = generate_verification_code()
    print(f"🔐 Code initial généré : {code_initial}")
    
    # Simulation envoi initial
    print("📤 Envoi du code initial...")
    result_initial = send_whatsapp_verification_free(numero_initial, code_initial)
    if result_initial:
        print("✅ Code envoyé avec succès au numéro initial")
    else:
        print("❌ Échec envoi au numéro initial")
    
    time.sleep(1)
    
    # Étape 2 : Utilisateur veut changer de numéro
    nouveau_numero = "+212603999558"
    print(f"\n2️⃣ Utilisateur veut changer pour : {nouveau_numero}")
    
    # Génération du nouveau code
    nouveau_code = generate_verification_code()
    print(f"🔐 Nouveau code généré : {nouveau_code}")
    
    # Simulation envoi au nouveau numéro
    print("📤 Envoi du code au nouveau numéro...")
    result_nouveau = send_whatsapp_verification_free(nouveau_numero, nouveau_code)
    if result_nouveau:
        print("✅ Code envoyé avec succès au nouveau numéro")
    else:
        print("❌ Échec envoi au nouveau numéro")
    
    time.sleep(1)
    
    # Étape 3 : Resend code sur le nouveau numéro
    print(f"\n3️⃣ Resend code sur le nouveau numéro : {nouveau_numero}")
    
    # Génération d'un autre code
    code_resend = generate_verification_code()
    print(f"🔐 Code resend généré : {code_resend}")
    
    # Simulation resend
    print("📤 Resend du code...")
    result_resend = send_whatsapp_verification_free(nouveau_numero, code_resend)
    if result_resend:
        print("✅ Code resend avec succès")
    else:
        print("❌ Échec resend")
    
    # Résumé des fonctionnalités
    print("\n🎯 FONCTIONNALITÉS DÉMONTRÉES :")
    print("=" * 40)
    print("✅ Génération de codes de vérification")
    print("✅ Envoi via service WhatsApp gratuit")
    print("✅ Changement de numéro de téléphone")
    print("✅ Resend de codes")
    print("✅ Validation et nettoyage des numéros")
    
    print("\n🌐 INTERFACE WEB DISPONIBLE :")
    print("=" * 35)
    print("📍 Inscription : http://127.0.0.1:8001/phone-signup/")
    print("📍 Vérification : http://127.0.0.1:8001/phone-verification/")
    print("📍 API Resend : POST /resend-verification-code/")
    print("📍 API Change : POST /change-phone-number/")
    
    print("\n🎮 COMMENT TESTER :")
    print("=" * 20)
    print("1. Aller sur la page d'inscription")
    print("2. Saisir un numéro de téléphone")
    print("3. Sur la page de vérification :")
    print("   • Cliquer 'Resend code' pour renvoyer")
    print("   • Cliquer 'Use different number' pour changer")
    print("4. Dans le modal de changement :")
    print("   • Saisir nouveau numéro")
    print("   • Cliquer 'Send code'")
    print("5. Vérifier que l'affichage se met à jour")
    print("6. Voir le nouveau code en mode debug")
    
    print("\n🚀 SYSTÈME OPÉRATIONNEL À 100% !")
    
    return {
        'numero_initial': numero_initial,
        'code_initial': code_initial,
        'nouveau_numero': nouveau_numero,
        'nouveau_code': nouveau_code,
        'code_resend': code_resend,
        'tous_envois_reussis': result_initial and result_nouveau and result_resend
    }

if __name__ == '__main__':
    resultat = demo_changement_numero()
    
    print(f"\n📊 RÉSULTAT DE LA DÉMONSTRATION :")
    print(f"Numéro initial : {resultat['numero_initial']}")
    print(f"Nouveau numéro : {resultat['nouveau_numero']}")
    print(f"Tous les envois réussis : {'✅ OUI' if resultat['tous_envois_reussis'] else '❌ NON'}")
    
    if resultat['tous_envois_reussis']:
        print("\n🎉 DÉMONSTRATION RÉUSSIE - SYSTÈME FONCTIONNEL !")
    else:
        print("\n⚠️  Certains envois ont échoué - Vérifier la configuration WhatsApp")
