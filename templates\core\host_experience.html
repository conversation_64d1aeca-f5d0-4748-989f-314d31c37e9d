{% extends 'base.html' %}
{% load static %}

{% block title %}Host an Experience - Nestria{% endblock %}

{% block content %}
<div class="min-h-screen bg-gray-50 dark:bg-gray-900 py-8">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="text-center mb-12">
            <h1 class="text-4xl font-bold text-gray-900 dark:text-white mb-4">
                Host an Experience
            </h1>
            <p class="text-xl text-gray-600 dark:text-gray-400 max-w-3xl mx-auto">
                Share your passion and expertise with travelers from around the world. Create memorable experiences and earn money doing what you love.
            </p>
        </div>

        <!-- Benefits Section -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-8 mb-12">
            <div class="text-center">
                <div class="w-16 h-16 bg-blue-100 dark:bg-blue-900/20 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-dollar-sign text-blue-600 dark:text-blue-400 text-2xl"></i>
                </div>
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">Earn Money</h3>
                <p class="text-gray-600 dark:text-gray-400">Set your own prices and earn up to $500+ per experience</p>
            </div>
            
            <div class="text-center">
                <div class="w-16 h-16 bg-green-100 dark:bg-green-900/20 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-users text-green-600 dark:text-green-400 text-2xl"></i>
                </div>
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">Meet People</h3>
                <p class="text-gray-600 dark:text-gray-400">Connect with travelers and share your local culture</p>
            </div>
            
            <div class="text-center">
                <div class="w-16 h-16 bg-purple-100 dark:bg-purple-900/20 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-star text-purple-600 dark:text-purple-400 text-2xl"></i>
                </div>
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">Share Passion</h3>
                <p class="text-gray-600 dark:text-gray-400">Turn your hobbies and skills into amazing experiences</p>
            </div>
        </div>

        <!-- Experience Form -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-8 mb-8">
            <h2 class="text-2xl font-bold text-gray-900 dark:text-white mb-6">Create Your Experience</h2>
            
            <form class="space-y-6">
                {% csrf_token %}
                
                <!-- Basic Information -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Experience Title *
                        </label>
                        <input type="text" 
                               placeholder="e.g., Authentic Italian Cooking Class"
                               class="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white">
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Category *
                        </label>
                        <select class="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white">
                            <option>Select a category</option>
                            <option>Food & Drink</option>
                            <option>Adventure</option>
                            <option>Arts & Culture</option>
                            <option>Entertainment</option>
                            <option>Sports & Fitness</option>
                            <option>Nature & Outdoors</option>
                        </select>
                    </div>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Description *
                    </label>
                    <textarea rows="4" 
                              placeholder="Describe what guests will do, what they'll learn, and what makes your experience special..."
                              class="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"></textarea>
                </div>

                <!-- Location and Duration -->
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Location *
                        </label>
                        <input type="text" 
                               placeholder="City, Country"
                               class="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white">
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Duration *
                        </label>
                        <select class="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white">
                            <option>1 hour</option>
                            <option>2 hours</option>
                            <option>3 hours</option>
                            <option>4 hours</option>
                            <option>Half day (5-6 hours)</option>
                            <option>Full day (7+ hours)</option>
                        </select>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Group Size *
                        </label>
                        <select class="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white">
                            <option>1-2 guests</option>
                            <option>1-4 guests</option>
                            <option>1-6 guests</option>
                            <option>1-8 guests</option>
                            <option>1-10 guests</option>
                            <option>10+ guests</option>
                        </select>
                    </div>
                </div>

                <!-- Pricing -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Price per Person *
                        </label>
                        <div class="relative">
                            <span class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500">$</span>
                            <input type="number" 
                                   placeholder="50"
                                   class="w-full pl-8 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white">
                        </div>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Language *
                        </label>
                        <select class="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white">
                            <option>English</option>
                            <option>Spanish</option>
                            <option>French</option>
                            <option>German</option>
                            <option>Italian</option>
                            <option>Portuguese</option>
                            <option>Other</option>
                        </select>
                    </div>
                </div>

                <!-- What's Included -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        What's Included
                    </label>
                    <div class="space-y-2">
                        <div class="flex items-center">
                            <input type="checkbox" class="w-4 h-4 text-blue-600 rounded">
                            <label class="ml-2 text-sm text-gray-700 dark:text-gray-300">Equipment</label>
                        </div>
                        <div class="flex items-center">
                            <input type="checkbox" class="w-4 h-4 text-blue-600 rounded">
                            <label class="ml-2 text-sm text-gray-700 dark:text-gray-300">Food & Drinks</label>
                        </div>
                        <div class="flex items-center">
                            <input type="checkbox" class="w-4 h-4 text-blue-600 rounded">
                            <label class="ml-2 text-sm text-gray-700 dark:text-gray-300">Transportation</label>
                        </div>
                        <div class="flex items-center">
                            <input type="checkbox" class="w-4 h-4 text-blue-600 rounded">
                            <label class="ml-2 text-sm text-gray-700 dark:text-gray-300">Materials</label>
                        </div>
                    </div>
                </div>

                <!-- Photos -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Photos
                    </label>
                    <div class="border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg p-6 text-center">
                        <i class="fas fa-cloud-upload-alt text-gray-400 text-3xl mb-4"></i>
                        <p class="text-gray-600 dark:text-gray-400 mb-2">Upload photos of your experience</p>
                        <button type="button" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm">
                            Choose Files
                        </button>
                    </div>
                </div>

                <!-- Submit Button -->
                <div class="flex space-x-4">
                    <button type="button" 
                            onclick="submitExperience()"
                            class="flex-1 bg-blue-600 hover:bg-blue-700 text-white py-3 px-6 rounded-lg font-medium transition-colors">
                        <i class="fas fa-plus mr-2"></i>
                        Create Experience
                    </button>
                    <button type="button" 
                            onclick="saveDraft()"
                            class="bg-gray-300 hover:bg-gray-400 text-gray-700 py-3 px-6 rounded-lg font-medium transition-colors">
                        Save Draft
                    </button>
                </div>
            </form>
        </div>

        <!-- Support Section -->
        <div class="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-6 text-center">
            <h3 class="text-lg font-semibold text-blue-900 dark:text-blue-200 mb-2">
                Need Help Getting Started?
            </h3>
            <p class="text-blue-700 dark:text-blue-300 mb-4">
                Our team is here to help you create an amazing experience that guests will love.
            </p>
            <button onclick="contactSupport()" class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg font-medium">
                Contact Support
            </button>
        </div>
    </div>
</div>

<script>
function submitExperience() {
    if (confirm('Submit your experience for review?\n\nOur team will review your experience and get back to you within 24-48 hours.')) {
        alert('Experience submitted successfully! You will receive an email confirmation shortly.\n\nOur team will review your experience and contact you within 24-48 hours.');
    }
}

function saveDraft() {
    alert('Draft saved successfully! You can continue editing your experience later from your host dashboard.');
}

function contactSupport() {
    alert('Support contact options:\n\n📧 Email: <EMAIL>\n📞 Phone: +1 (555) 123-HOST\n💬 Live Chat: Available 24/7\n\nOur team typically responds within 2 hours.');
}
</script>
{% endblock %}
