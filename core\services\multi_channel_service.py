"""
Service multi-canal intelligent
Essaie WhatsApp, puis SMS, puis Email automatiquement
"""

import logging
from django.conf import settings
from .whatsapp_service import send_whatsapp_verification
from .whatsapp_meta_service import send_whatsapp_verification_meta
from .sms_service import send_sms_verification
from .email_verification_service import send_email_verification

logger = logging.getLogger(__name__)


class MultiChannelVerificationService:
    """Service intelligent qui essaie plusieurs canaux"""
    
    def __init__(self):
        self.preferred_order = getattr(settings, 'VERIFICATION_CHANNEL_ORDER', [
            'whatsapp_twilio',
            'whatsapp_meta', 
            'sms',
            'email'
        ])
    
    def send_verification_code(self, phone_number, email, verification_code, user_name=""):
        """
        Envoie un code de vérification en essayant plusieurs canaux
        
        Args:
            phone_number (str): Numéro de téléphone
            email (str): Adresse email
            verification_code (str): Code à 6 chiffres
            user_name (str): Nom de l'utilisateur
            
        Returns:
            dict: Résultat de l'envoi avec le canal utilisé
        """
        
        results = []
        
        for channel in self.preferred_order:
            try:
                if channel == 'whatsapp_twilio' and phone_number:
                    logger.info(f"🔄 Trying WhatsApp (Twilio) for {phone_number}")
                    success = send_whatsapp_verification(phone_number, verification_code)
                    
                    if success:
                        logger.info(f"✅ WhatsApp (Twilio) successful for {phone_number}")
                        return {
                            'success': True,
                            'channel': 'whatsapp_twilio',
                            'target': phone_number,
                            'message': 'Code sent via WhatsApp (Twilio)'
                        }
                    else:
                        results.append(f"❌ WhatsApp (Twilio) failed")
                
                elif channel == 'whatsapp_meta' and phone_number:
                    logger.info(f"🔄 Trying WhatsApp (Meta) for {phone_number}")
                    success = send_whatsapp_verification_meta(phone_number, verification_code)
                    
                    if success:
                        logger.info(f"✅ WhatsApp (Meta) successful for {phone_number}")
                        return {
                            'success': True,
                            'channel': 'whatsapp_meta',
                            'target': phone_number,
                            'message': 'Code sent via WhatsApp (Meta)'
                        }
                    else:
                        results.append(f"❌ WhatsApp (Meta) failed")
                
                elif channel == 'sms' and phone_number:
                    logger.info(f"🔄 Trying SMS for {phone_number}")
                    success = send_sms_verification(phone_number, verification_code)
                    
                    if success:
                        logger.info(f"✅ SMS successful for {phone_number}")
                        return {
                            'success': True,
                            'channel': 'sms',
                            'target': phone_number,
                            'message': 'Code sent via SMS'
                        }
                    else:
                        results.append(f"❌ SMS failed")
                
                elif channel == 'email' and email:
                    logger.info(f"🔄 Trying Email for {email}")
                    success = send_email_verification(email, verification_code, user_name)
                    
                    if success:
                        logger.info(f"✅ Email successful for {email}")
                        return {
                            'success': True,
                            'channel': 'email',
                            'target': email,
                            'message': 'Code sent via Email'
                        }
                    else:
                        results.append(f"❌ Email failed")
                        
            except Exception as e:
                logger.error(f"❌ Error with {channel}: {str(e)}")
                results.append(f"❌ {channel} error: {str(e)}")
        
        # Tous les canaux ont échoué
        logger.error(f"❌ All verification channels failed. Results: {results}")
        return {
            'success': False,
            'error': 'All verification channels failed',
            'attempts': results
        }


# Instance globale
multi_channel_service = MultiChannelVerificationService()


def send_verification_smart(phone_number, email, verification_code, user_name=""):
    """
    Fonction helper pour envoi intelligent multi-canal
    """
    result = multi_channel_service.send_verification_code(
        phone_number, email, verification_code, user_name
    )
    return result
