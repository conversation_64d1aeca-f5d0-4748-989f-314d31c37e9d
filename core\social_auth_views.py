"""
Vues pour l'authentification sociale
"""

import json
import requests
from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_POST
from django.contrib.auth import login
from django.contrib.auth.models import User
from django.conf import settings
from django.utils.decorators import method_decorator
from django.views import View
from .social_auth_config import (
    GOOGLE_OAUTH_CONFIG, FACEBOOK_CONFIG, APPLE_CONFIG,
    SOCIAL_AUTH_URLS, ERROR_MESSAGES, USER_FIELD_MAPPING
)


class SocialAuthMixin:
    """Mixin pour l'authentification sociale"""
    
    def create_or_get_user(self, provider, user_data):
        """Crée ou récupère un utilisateur basé sur les données du provider"""
        email = user_data.get('email')
        if not email:
            return None, 'Email non fourni par le provider'
        
        # Vérifier si l'utilisateur existe déjà
        try:
            user = User.objects.get(email=email)
            return user, None
        except User.DoesNotExist:
            pass
        
        # Créer un nouveau utilisateur
        try:
            mapping = USER_FIELD_MAPPING[provider]
            user_fields = {
                'username': email,  # Utiliser l'email comme username
                'email': email,
                'first_name': self.get_nested_value(user_data, mapping.get('first_name', '')),
                'last_name': self.get_nested_value(user_data, mapping.get('last_name', '')),
            }
            
            user = User.objects.create_user(**user_fields)
            return user, None
            
        except Exception as e:
            return None, f'Erreur lors de la création de l\'utilisateur: {str(e)}'
    
    def get_nested_value(self, data, key_path):
        """Récupère une valeur imbriquée dans un dictionnaire"""
        if not key_path:
            return ''
        
        keys = key_path.split('.')
        value = data
        
        try:
            for key in keys:
                value = value[key]
            return value or ''
        except (KeyError, TypeError):
            return ''


@method_decorator(csrf_exempt, name='dispatch')
class GoogleAuthView(View, SocialAuthMixin):
    """Vue pour l'authentification Google"""

    def get(self, request):
        """Redirection vers la page d'authentification Google"""
        from django.shortcuts import redirect
        from django.contrib import messages
        messages.success(request, 'Google login will be implemented')
        # Simuler une connexion réussie en redirigeant vers contact-info
        request.session['social_auth_provider'] = 'google'
        return redirect('core:contact_info')

    def post(self, request):
        try:
            data = json.loads(request.body)
            credential = data.get('credential')
            
            if not credential:
                return JsonResponse({
                    'success': False,
                    'error': 'Token Google manquant'
                })
            
            # Vérifier le token avec Google
            user_data = self.verify_google_token(credential)
            if not user_data:
                return JsonResponse({
                    'success': False,
                    'error': ERROR_MESSAGES['invalid_token']
                })
            
            # Créer ou récupérer l'utilisateur
            user, error = self.create_or_get_user('google', user_data)
            if error:
                return JsonResponse({
                    'success': False,
                    'error': error
                })
            
            # Connecter l'utilisateur
            login(request, user)
            
            return JsonResponse({
                'success': True,
                'redirect_url': '/',
                'user': {
                    'id': user.id,
                    'email': user.email,
                    'name': user.get_full_name()
                }
            })
            
        except Exception as e:
            return JsonResponse({
                'success': False,
                'error': f'Erreur serveur: {str(e)}'
            })
    
    def verify_google_token(self, token):
        """Vérifie le token Google et récupère les informations utilisateur"""
        try:
            # Vérifier le token avec l'API Google
            response = requests.get(
                f'https://oauth2.googleapis.com/tokeninfo?id_token={token}',
                timeout=10
            )
            
            if response.status_code == 200:
                return response.json()
            return None
            
        except Exception:
            return None


@method_decorator(csrf_exempt, name='dispatch')
class FacebookAuthView(View, SocialAuthMixin):
    """Vue pour l'authentification Facebook"""

    def get(self, request):
        """Redirection vers la page d'authentification Facebook"""
        from django.shortcuts import redirect
        from django.contrib import messages
        messages.success(request, 'Facebook login will be implemented')
        # Simuler une connexion réussie en redirigeant vers contact-info
        request.session['social_auth_provider'] = 'facebook'
        return redirect('core:contact_info')

    def post(self, request):
        try:
            data = json.loads(request.body)
            access_token = data.get('accessToken')
            user_info = data.get('userInfo')
            
            if not access_token or not user_info:
                return JsonResponse({
                    'success': False,
                    'error': 'Données Facebook manquantes'
                })
            
            # Vérifier le token avec Facebook
            if not self.verify_facebook_token(access_token):
                return JsonResponse({
                    'success': False,
                    'error': ERROR_MESSAGES['invalid_token']
                })
            
            # Créer ou récupérer l'utilisateur
            user, error = self.create_or_get_user('facebook', user_info)
            if error:
                return JsonResponse({
                    'success': False,
                    'error': error
                })
            
            # Connecter l'utilisateur
            login(request, user)
            
            return JsonResponse({
                'success': True,
                'redirect_url': '/',
                'user': {
                    'id': user.id,
                    'email': user.email,
                    'name': user.get_full_name()
                }
            })
            
        except Exception as e:
            return JsonResponse({
                'success': False,
                'error': f'Erreur serveur: {str(e)}'
            })
    
    def verify_facebook_token(self, access_token):
        """Vérifie le token Facebook"""
        try:
            app_id = FACEBOOK_CONFIG['app_id']
            app_secret = FACEBOOK_CONFIG['app_secret']
            
            response = requests.get(
                f'https://graph.facebook.com/debug_token?input_token={access_token}&access_token={app_id}|{app_secret}',
                timeout=10
            )
            
            if response.status_code == 200:
                data = response.json()
                return data.get('data', {}).get('is_valid', False)
            return False
            
        except Exception:
            return False


@method_decorator(csrf_exempt, name='dispatch')
class AppleAuthView(View, SocialAuthMixin):
    """Vue pour l'authentification Apple"""

    def get(self, request):
        """Redirection vers la page d'authentification Apple"""
        from django.shortcuts import redirect
        from django.contrib import messages
        messages.success(request, 'Apple login will be implemented')
        # Simuler une connexion réussie en redirigeant vers contact-info
        request.session['social_auth_provider'] = 'apple'
        return redirect('core:contact_info')

    def post(self, request):
        try:
            data = json.loads(request.body)
            authorization = data.get('authorization')
            user_data = data.get('user')
            
            if not authorization:
                return JsonResponse({
                    'success': False,
                    'error': 'Données Apple manquantes'
                })
            
            # Pour Apple, les données utilisateur ne sont fournies qu'à la première connexion
            # Nous devons décoder le JWT pour obtenir l'email
            user_info = self.decode_apple_token(authorization.get('id_token'))
            if user_data:
                user_info.update({'user': user_data})
            
            if not user_info:
                return JsonResponse({
                    'success': False,
                    'error': ERROR_MESSAGES['invalid_token']
                })
            
            # Créer ou récupérer l'utilisateur
            user, error = self.create_or_get_user('apple', user_info)
            if error:
                return JsonResponse({
                    'success': False,
                    'error': error
                })
            
            # Connecter l'utilisateur
            login(request, user)
            
            return JsonResponse({
                'success': True,
                'redirect_url': '/',
                'user': {
                    'id': user.id,
                    'email': user.email,
                    'name': user.get_full_name()
                }
            })
            
        except Exception as e:
            return JsonResponse({
                'success': False,
                'error': f'Erreur serveur: {str(e)}'
            })
    
    def decode_apple_token(self, id_token):
        """Décode le token Apple JWT (version simplifiée)"""
        try:
            import base64
            
            # Décoder la partie payload du JWT (sans vérification de signature pour la démo)
            parts = id_token.split('.')
            if len(parts) != 3:
                return None
            
            payload = parts[1]
            # Ajouter le padding si nécessaire
            payload += '=' * (4 - len(payload) % 4)
            
            decoded = base64.urlsafe_b64decode(payload)
            return json.loads(decoded)
            
        except Exception:
            return None
