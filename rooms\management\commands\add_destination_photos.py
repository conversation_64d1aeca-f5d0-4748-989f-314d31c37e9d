from django.core.management.base import BaseCommand
from django.core.files.base import ContentFile
from rooms.models import Destination, DestinationImage
import requests
import os


class Command(BaseCommand):
    help = 'Add professional photos to destinations'

    def handle(self, *args, **options):
        self.stdout.write('Adding professional photos to destinations...')
        
        # Professional photos for each destination
        destination_photos = {
            'paris-france': [
                {
                    'url': 'https://images.unsplash.com/photo-1502602898536-47ad22581b52?w=1200&h=800&fit=crop',
                    'caption': 'Eiffel Tower at sunset with Seine River',
                    'order': 1
                },
                {
                    'url': 'https://images.unsplash.com/photo-1549144511-f099e773c147?w=1200&h=800&fit=crop',
                    'caption': 'Louvre Museum and glass pyramid',
                    'order': 2
                },
                {
                    'url': 'https://images.unsplash.com/photo-1431274172761-fca41d930114?w=1200&h=800&fit=crop',
                    'caption': 'Notre-Dame Cathedral architecture',
                    'order': 3
                },
                {
                    'url': 'https://images.unsplash.com/photo-1522093007474-d86e9bf7ba6f?w=1200&h=800&fit=crop',
                    'caption': 'Champs-Élysées and Arc de Triomphe',
                    'order': 4
                },
                {
                    'url': 'https://images.unsplash.com/photo-1499856871958-5b9627545d1a?w=1200&h=800&fit=crop',
                    'caption': 'Montmartre and Sacré-Cœur Basilica',
                    'order': 5
                }
            ],
            'tokyo-japan': [
                {
                    'url': 'https://images.unsplash.com/photo-1540959733332-eab4deabeeaf?w=1200&h=800&fit=crop',
                    'caption': 'Tokyo skyline with Mount Fuji',
                    'order': 1
                },
                {
                    'url': 'https://images.unsplash.com/photo-1513407030348-c983a97b98d8?w=1200&h=800&fit=crop',
                    'caption': 'Sensoji Temple in Asakusa',
                    'order': 2
                },
                {
                    'url': 'https://images.unsplash.com/photo-1542051841857-5f90071e7989?w=1200&h=800&fit=crop',
                    'caption': 'Shibuya Crossing at night',
                    'order': 3
                },
                {
                    'url': 'https://images.unsplash.com/photo-1503899036084-c55cdd92da26?w=1200&h=800&fit=crop',
                    'caption': 'Tokyo Tower illuminated',
                    'order': 4
                },
                {
                    'url': 'https://images.unsplash.com/photo-1526481280693-3bfa7568e0f3?w=1200&h=800&fit=crop',
                    'caption': 'Cherry blossoms in Ueno Park',
                    'order': 5
                }
            ],
            'new-york-usa': [
                {
                    'url': 'https://images.unsplash.com/photo-1496442226666-8d4d0e62e6e9?w=1200&h=800&fit=crop',
                    'caption': 'Manhattan skyline from Brooklyn Bridge',
                    'order': 1
                },
                {
                    'url': 'https://images.unsplash.com/photo-1485871981521-5b1fd3805eee?w=1200&h=800&fit=crop',
                    'caption': 'Times Square neon lights',
                    'order': 2
                },
                {
                    'url': 'https://images.unsplash.com/photo-1518391846015-55a9cc003b25?w=1200&h=800&fit=crop',
                    'caption': 'Central Park in autumn',
                    'order': 3
                },
                {
                    'url': 'https://images.unsplash.com/photo-1500916434205-0c77489c6cf7?w=1200&h=800&fit=crop',
                    'caption': 'Statue of Liberty and harbor',
                    'order': 4
                },
                {
                    'url': 'https://images.unsplash.com/photo-1541336032412-2048a678540d?w=1200&h=800&fit=crop',
                    'caption': 'Empire State Building at dusk',
                    'order': 5
                }
            ],
            'rome-italy': [
                {
                    'url': 'https://images.unsplash.com/photo-1552832230-c0197dd311b5?w=1200&h=800&fit=crop',
                    'caption': 'Colosseum ancient amphitheater',
                    'order': 1
                },
                {
                    'url': 'https://images.unsplash.com/photo-1515542622106-78bda8ba0e5b?w=1200&h=800&fit=crop',
                    'caption': 'Vatican City and St. Peter\'s Basilica',
                    'order': 2
                },
                {
                    'url': 'https://images.unsplash.com/photo-1531572753322-ad063cecc140?w=1200&h=800&fit=crop',
                    'caption': 'Trevi Fountain baroque masterpiece',
                    'order': 3
                },
                {
                    'url': 'https://images.unsplash.com/photo-1539650116574-75c0c6d73f6e?w=1200&h=800&fit=crop',
                    'caption': 'Roman Forum ancient ruins',
                    'order': 4
                },
                {
                    'url': 'https://images.unsplash.com/photo-1525874684015-58379d421a52?w=1200&h=800&fit=crop',
                    'caption': 'Pantheon classical architecture',
                    'order': 5
                }
            ],
            'barcelona-spain': [
                {
                    'url': 'https://images.unsplash.com/photo-1539037116277-4db20889f2d4?w=1200&h=800&fit=crop',
                    'caption': 'Sagrada Familia Gaudí masterpiece',
                    'order': 1
                },
                {
                    'url': 'https://images.unsplash.com/photo-1511527661048-7fe73d85e9a4?w=1200&h=800&fit=crop',
                    'caption': 'Park Güell colorful mosaics',
                    'order': 2
                },
                {
                    'url': 'https://images.unsplash.com/photo-1558642452-9d2a7deb7f62?w=1200&h=800&fit=crop',
                    'caption': 'Casa Batlló modernist facade',
                    'order': 3
                },
                {
                    'url': 'https://images.unsplash.com/photo-1523531294919-4bcd7c65e216?w=1200&h=800&fit=crop',
                    'caption': 'Gothic Quarter medieval streets',
                    'order': 4
                },
                {
                    'url': 'https://images.unsplash.com/photo-1564221710304-0b37c8b9d729?w=1200&h=800&fit=crop',
                    'caption': 'Barceloneta Beach Mediterranean coast',
                    'order': 5
                }
            ],
            'london-uk': [
                {
                    'url': 'https://images.unsplash.com/photo-1513635269975-59663e0ac1ad?w=1200&h=800&fit=crop',
                    'caption': 'Big Ben and Houses of Parliament',
                    'order': 1
                },
                {
                    'url': 'https://images.unsplash.com/photo-1520986606214-8b456906c813?w=1200&h=800&fit=crop',
                    'caption': 'Tower Bridge over Thames River',
                    'order': 2
                },
                {
                    'url': 'https://images.unsplash.com/photo-1529655683826-aba9b3e77383?w=1200&h=800&fit=crop',
                    'caption': 'Buckingham Palace royal residence',
                    'order': 3
                },
                {
                    'url': 'https://images.unsplash.com/photo-1533929736458-ca588d08c8be?w=1200&h=800&fit=crop',
                    'caption': 'London Eye observation wheel',
                    'order': 4
                },
                {
                    'url': 'https://images.unsplash.com/photo-1505761671935-60b3a7427bad?w=1200&h=800&fit=crop',
                    'caption': 'Hyde Park autumn landscape',
                    'order': 5
                }
            ]
        }
        
        for destination_slug, photos in destination_photos.items():
            try:
                destination = Destination.objects.get(slug=destination_slug)
                self.stdout.write(f'Processing {destination.name}...')
                
                for photo_data in photos:
                    # Check if image already exists
                    if DestinationImage.objects.filter(
                        destination=destination, 
                        order=photo_data['order']
                    ).exists():
                        self.stdout.write(f'  Photo {photo_data["order"]} already exists, skipping...')
                        continue
                    
                    try:
                        # Download image
                        response = requests.get(photo_data['url'], timeout=30)
                        if response.status_code == 200:
                            # Create filename
                            filename = f"{destination_slug}_{photo_data['order']}.jpg"
                            
                            # Create DestinationImage
                            dest_image = DestinationImage(
                                destination=destination,
                                caption=photo_data['caption'],
                                order=photo_data['order']
                            )
                            
                            # Save image
                            dest_image.image.save(
                                filename,
                                ContentFile(response.content),
                                save=True
                            )
                            
                            self.stdout.write(f'  ✅ Added photo: {photo_data["caption"]}')
                        else:
                            self.stdout.write(f'  ❌ Failed to download photo {photo_data["order"]}')
                    
                    except Exception as e:
                        self.stdout.write(f'  ❌ Error processing photo {photo_data["order"]}: {str(e)}')
                
            except Destination.DoesNotExist:
                self.stdout.write(f'❌ Destination {destination_slug} not found')
        
        # Update main images for destinations
        self.stdout.write('\nUpdating main destination images...')
        main_images = {
            'paris-france': 'https://images.unsplash.com/photo-1502602898536-47ad22581b52?w=1200&h=800&fit=crop',
            'tokyo-japan': 'https://images.unsplash.com/photo-1540959733332-eab4deabeeaf?w=1200&h=800&fit=crop',
            'new-york-usa': 'https://images.unsplash.com/photo-1496442226666-8d4d0e62e6e9?w=1200&h=800&fit=crop',
            'rome-italy': 'https://images.unsplash.com/photo-1552832230-c0197dd311b5?w=1200&h=800&fit=crop',
            'barcelona-spain': 'https://images.unsplash.com/photo-1539037116277-4db20889f2d4?w=1200&h=800&fit=crop',
            'london-uk': 'https://images.unsplash.com/photo-1513635269975-59663e0ac1ad?w=1200&h=800&fit=crop'
        }
        
        for destination_slug, image_url in main_images.items():
            try:
                destination = Destination.objects.get(slug=destination_slug)
                if not destination.main_image:
                    response = requests.get(image_url, timeout=30)
                    if response.status_code == 200:
                        filename = f"{destination_slug}_main.jpg"
                        destination.main_image.save(
                            filename,
                            ContentFile(response.content),
                            save=True
                        )
                        self.stdout.write(f'✅ Updated main image for {destination.name}')
                else:
                    self.stdout.write(f'Main image already exists for {destination.name}')
            except Exception as e:
                self.stdout.write(f'❌ Error updating main image for {destination_slug}: {str(e)}')
        
        self.stdout.write(self.style.SUCCESS('\n🎉 Professional photos added successfully!'))
        self.stdout.write('You can view the destinations at /rooms/destinations/')
