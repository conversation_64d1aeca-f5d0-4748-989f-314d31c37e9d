# 🏠 Airbnb Clone - Professional Web Application

A comprehensive, production-ready Airbnb clone built with Django, featuring beautiful destinations, property listings, booking system, and modern responsive design.

![Airbnb Clone](https://img.shields.io/badge/Django-5.2.5-green)
![Python](https://img.shields.io/badge/Python-3.11+-blue)
![License](https://img.shields.io/badge/License-MIT-yellow)

## ✨ Features

### 🏛️ **Historic Destinations**
- **6 Featured Destinations**: Paris, Tokyo, New York, Rome, Barcelona, London
- **Professional Photography**: High-quality images from Unsplash for each destination
- **Interactive Maps**: Leaflet-powered maps with points of interest and custom markers
- **Advanced Gallery**: Professional photo gallery with lightbox, navigation, and thumbnails
- **Rich Historical Content**: Detailed historical information spanning centuries
- **Geographic Data**: Precise coordinates and location-based features

### 🏠 **Property Management**
- **Property Listings**: Comprehensive room details with images and amenities
- **Advanced Search**: Filter by location, dates, guests, price, and amenities
- **Booking System**: Complete reservation flow with price calculations
- **Reviews & Ratings**: User review system with star ratings

### 👤 **User Experience**
- **Authentication**: Secure login/signup with Django's built-in system
- **User Profiles**: Personal dashboard with trips and favorites
- **Favorites System**: Save and manage favorite properties
- **Responsive Design**: Mobile-first design that works on all devices

### 🎨 **Modern UI/UX**
- **Airbnb-Inspired Design**: Clean, professional interface
- **Tailwind CSS**: Utility-first CSS framework for rapid development
- **Alpine.js**: Lightweight JavaScript for interactive components
- **Leaflet Maps**: Interactive maps with custom markers and points of interest
- **Professional Gallery**: Advanced photo gallery with keyboard navigation
- **Font Awesome**: Beautiful icons throughout the application
- **Responsive Design**: Optimized for desktop, tablet, and mobile devices

## 🚀 Quick Start

### One-Command Setup

For the complete experience with all features:

```bash
python setup_complete_app.py
python manage.py runserver
```

### Manual Development Setup

1. **Clone the repository**
```bash
git clone <repository-url>
cd airbnb-clone
```

2. **Create virtual environment**
```bash
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
```

3. **Install dependencies**
```bash
pip install -r requirements.txt
```

4. **Run migrations**
```bash
python manage.py migrate
```

5. **Create sample data with photos and maps**
```bash
python manage.py populate_data
python manage.py populate_destinations
python manage.py add_destination_photos
python manage.py add_property_photos
python manage.py add_property_coordinates
```

6. **Create superuser**
```bash
python manage.py createsuperuser
```

7. **Start development server**
```bash
python manage.py runserver
```

Visit `http://127.0.0.1:8000` to see the application!

## 🌐 Production Deployment

### Option 1: Traditional Server Deployment

1. **Install production dependencies**
```bash
pip install -r requirements.txt
```

2. **Configure environment variables**
```bash
cp .env.example .env
# Edit .env with your production settings
```

3. **Run deployment script**
```bash
python deploy.py
```

4. **Start with Gunicorn (WSGI)**
```bash
gunicorn --bind 0.0.0.0:8000 --workers 3 wsgi_production:application
```

5. **Or start with Uvicorn (ASGI)**
```bash
uvicorn asgi_production:application --host 0.0.0.0 --port 8000
```

### Option 2: Docker Deployment

1. **Build and run with Docker Compose**
```bash
docker-compose up -d
```

2. **Access the application**
- Application: `http://localhost:8000`
- Admin: `http://localhost:8000/admin`

## 📁 Project Structure

```
airbnb-clone/
├── airbnb_clone/           # Django project settings
├── core/                   # Authentication and home page
├── rooms/                  # Property listings and destinations
├── reservations/           # Booking system
├── search/                 # Search functionality
├── templates/              # HTML templates
├── static/                 # CSS, JS, images
├── media/                  # User uploaded files
├── requirements.txt        # Python dependencies
├── docker-compose.yml      # Docker configuration
├── nginx.conf             # Nginx configuration
└── deploy.py              # Deployment script
```

## 🗺️ Featured Destinations

### 🇫🇷 Paris, France
*The City of Light* - Iconic landmarks, world-class museums, and romantic atmosphere.

### 🇯🇵 Tokyo, Japan  
*Modern Metropolis* - Blend of traditional culture and cutting-edge technology.

### 🇺🇸 New York, USA
*The Big Apple* - Vibrant city life, Broadway shows, and diverse neighborhoods.

### 🇮🇹 Rome, Italy
*The Eternal City* - Ancient history, Vatican City, and archaeological treasures.

### 🇪🇸 Barcelona, Spain
*Mediterranean Jewel* - Gaudí's architecture, beaches, and vibrant culture.

### 🇬🇧 London, UK
*Historic Capital* - Royal heritage, world-class museums, and modern innovation.

## 🛠️ Technology Stack

- **Backend**: Django 5.2.5, Django REST Framework
- **Frontend**: HTML5, Tailwind CSS, Alpine.js
- **Database**: SQLite (development), PostgreSQL (production)
- **Cache**: Redis
- **Server**: Gunicorn (WSGI), Uvicorn (ASGI)
- **Proxy**: Nginx
- **Containerization**: Docker & Docker Compose

## 📱 API Endpoints

- `GET /` - Home page with featured properties
- `GET /rooms/destinations/` - List all destinations
- `GET /rooms/destinations/<slug>/` - Destination details
- `GET /rooms/<id>/` - Property details
- `GET /search/` - Search properties
- `POST /api/properties/<id>/toggle-favorite/` - Toggle favorites
- `GET /reservations/my-trips/` - User's bookings

## 🔧 Configuration

### Environment Variables

Create a `.env` file based on `.env.example`:

```env
SECRET_KEY=your-secret-key
DEBUG=False
DB_NAME=airbnb_clone
DB_USER=postgres
DB_PASSWORD=your-password
REDIS_URL=redis://localhost:6379/1
```

### Production Settings

The application includes production-ready settings:
- Security headers and HTTPS configuration
- Database connection pooling
- Redis caching
- Logging configuration
- Static file optimization

## 🚦 Testing

Run the test suite:
```bash
python manage.py test
```

## 📊 Admin Interface

Access the Django admin at `/admin/` to:
- Manage properties and destinations
- View bookings and reservations
- Moderate reviews and ratings
- Manage users and permissions

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🙏 Acknowledgments

- Inspired by Airbnb's beautiful design and user experience
- Built with Django's robust framework
- Styled with Tailwind CSS for rapid development
- Enhanced with Alpine.js for interactive components

---

**Ready to explore the world? Start your journey with our Airbnb clone! 🌍✈️**
