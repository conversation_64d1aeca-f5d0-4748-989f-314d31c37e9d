// Simple Page Transitions for Nestria

class PageTransitions {
    constructor() {
        this.init();
    }

    init() {
        // Initialize simple page animations
        this.initPageAnimations();
    }

    initPageAnimations() {
        // Add simple fade animation to main content
        const mainContent = document.querySelector('main') || document.querySelector('.main-content') || document.body;
        if (mainContent) {
            mainContent.classList.add('page-transition');
        }

        // Add simple hover effects to cards
        const cards = document.querySelectorAll('.bg-white, .bg-gray-800');
        cards.forEach(card => {
            card.style.transition = 'transform 0.2s ease, box-shadow 0.2s ease';
            card.addEventListener('mouseenter', () => {
                card.style.transform = 'translateY(-2px)';
                card.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.1)';
            });
            card.addEventListener('mouseleave', () => {
                card.style.transform = 'translateY(0)';
                card.style.boxShadow = '';
            });
        });
    }

    // Utility function for notifications
    static showNotification(message, type = 'info', duration = 3000) {
        const notification = document.createElement('div');
        notification.className = `fixed top-4 right-4 z-50 px-6 py-3 rounded-lg text-white font-medium max-w-sm`;

        const bgColor = {
            success: 'bg-green-500',
            error: 'bg-red-500',
            warning: 'bg-yellow-500',
            info: 'bg-blue-500'
        }[type] || 'bg-blue-500';

        notification.classList.add(bgColor);
        notification.textContent = message;
        notification.style.transform = 'translateX(100%)';
        notification.style.transition = 'transform 0.3s ease';

        document.body.appendChild(notification);

        // Animate in
        setTimeout(() => {
            notification.style.transform = 'translateX(0)';
        }, 100);

        // Auto remove
        setTimeout(() => {
            notification.style.transform = 'translateX(100%)';
            setTimeout(() => {
                if (document.body.contains(notification)) {
                    document.body.removeChild(notification);
                }
            }, 300);
        }, duration);

        return notification;
    }

}

// Initialize simple page transitions when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    new PageTransitions();
});

// Simple notification function
function showNotification(message, type = 'info', duration = 3000) {
    const notification = document.createElement('div');
    notification.className = `fixed top-4 right-4 z-50 px-6 py-3 rounded-lg text-white font-medium max-w-sm`;

    const bgColor = {
        success: 'bg-green-500',
        error: 'bg-red-500',
        warning: 'bg-yellow-500',
        info: 'bg-blue-500'
    }[type] || 'bg-blue-500';

    notification.classList.add(bgColor);
    notification.textContent = message;
    notification.style.transform = 'translateX(100%)';
    notification.style.transition = 'transform 0.3s ease';

    document.body.appendChild(notification);

    // Animate in
    setTimeout(() => {
        notification.style.transform = 'translateX(0)';
    }, 100);

    // Auto remove
    setTimeout(() => {
        notification.style.transform = 'translateX(100%)';
        setTimeout(() => {
            if (document.body.contains(notification)) {
                document.body.removeChild(notification);
            }
        }, 300);
    }, duration);

    return notification;
}

// Export for global use
window.showNotification = showNotification;
