#!/usr/bin/env python
"""
Script pour créer les profils manquants pour tous les utilisateurs
"""
import os
import django

# Configuration Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'airbnb_clone.settings')
django.setup()

from django.contrib.auth.models import User
from core.models import UserProfile

def fix_user_profiles():
    """Créer les profils manquants pour tous les utilisateurs"""
    
    print("🔧 Correction des profils utilisateur manquants...\n")
    
    users_without_profile = []
    users_with_profile = []
    
    # Vérifier tous les utilisateurs
    for user in User.objects.all():
        try:
            # Essayer d'accéder au profil
            profile = user.profile
            users_with_profile.append(user)
        except UserProfile.DoesNotExist:
            users_without_profile.append(user)
    
    print(f"📊 Statistiques:")
    print(f"   - Utilisateurs avec profil: {len(users_with_profile)}")
    print(f"   - Utilisateurs sans profil: {len(users_without_profile)}")
    
    if users_without_profile:
        print(f"\n🔨 Création des profils manquants...")
        
        for user in users_without_profile:
            try:
                profile = UserProfile.objects.create(user=user)
                print(f"   ✅ Profil créé pour: {user.email or user.username}")
            except Exception as e:
                print(f"   ❌ Erreur pour {user.email or user.username}: {str(e)}")
        
        print(f"\n🎉 {len(users_without_profile)} profils créés avec succès !")
    else:
        print(f"\n✅ Tous les utilisateurs ont déjà un profil !")
    
    # Vérification finale
    print(f"\n🔍 Vérification finale...")
    users_still_without_profile = []
    for user in User.objects.all():
        try:
            profile = user.profile
        except UserProfile.DoesNotExist:
            users_still_without_profile.append(user)
    
    if users_still_without_profile:
        print(f"❌ {len(users_still_without_profile)} utilisateurs n'ont toujours pas de profil")
        for user in users_still_without_profile:
            print(f"   - {user.email or user.username}")
    else:
        print(f"✅ Tous les utilisateurs ont maintenant un profil !")

if __name__ == '__main__':
    fix_user_profiles()
