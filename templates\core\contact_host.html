{% extends 'base.html' %}
{% load static %}

{% block title %}Contact Host - {{ room.title }} - Nestria{% endblock %}

{% block content %}
<div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Breadcrumb -->
    <nav class="breadcrumb mb-6">
        <a href="{% url 'core:home' %}">Home</a>
        <span class="mx-2">/</span>
        <a href="{% url 'core:your_trips' %}">Your Trips</a>
        <span class="mx-2">/</span>
        <span>Contact Host</span>
    </nav>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <!-- Property Info -->
        <div class="lg:col-span-1">
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 sticky top-8">
                {% if room.images.first %}
                    <img src="{{ room.images.first.image.url }}" alt="{{ room.title }}" class="w-full h-48 object-cover rounded-lg mb-4">
                {% endif %}
                
                <h3 class="text-lg font-semibold text-gray-900 mb-2">{{ room.title }}</h3>
                <p class="text-gray-600 mb-4">{{ room.city }}, {{ room.country }}</p>
                
                <div class="border-t border-gray-200 pt-4">
                    <h4 class="text-sm font-semibold text-gray-900 mb-2">Host Information</h4>
                    <div class="flex items-center space-x-3">
                        <div class="w-10 h-10 bg-gray-300 rounded-full flex items-center justify-center">
                            <i class="fas fa-user text-gray-600"></i>
                        </div>
                        <div>
                            <p class="font-medium text-gray-900">{{ room.host.first_name|default:room.host.username }}</p>
                            <p class="text-sm text-gray-500">Host since {{ room.host.date_joined|date:"Y" }}</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Contact Form -->
        <div class="lg:col-span-2">
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h1 class="text-2xl font-bold text-gray-900 mb-2">Contact Host</h1>
                <p class="text-gray-600 mb-6">Send a message to {{ room.host.first_name|default:room.host.username }} about {{ room.title }}</p>

                <form id="contactForm" method="post" action="{% url 'core:contact_host' room.id %}">
                    {% csrf_token %}
                    
                    <div class="mb-6">
                        <label for="subject" class="block text-sm font-medium text-gray-700 mb-2">Subject</label>
                        <input type="text" 
                               id="subject" 
                               name="subject" 
                               required
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                               placeholder="Question about your property">
                    </div>

                    <div class="mb-6">
                        <label for="message" class="block text-sm font-medium text-gray-700 mb-2">Message</label>
                        <textarea id="message" 
                                  name="message" 
                                  rows="6" 
                                  required
                                  class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                  placeholder="Hi {{ room.host.first_name|default:room.host.username }}, I'm interested in your property..."></textarea>
                    </div>

                    <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
                        <div class="flex">
                            <i class="fas fa-info-circle text-blue-600 mt-1 mr-3"></i>
                            <div>
                                <h4 class="text-sm font-semibold text-blue-900 mb-1">Tips for contacting hosts</h4>
                                <ul class="text-sm text-blue-800 space-y-1">
                                    <li>• Be specific about your travel dates and group size</li>
                                    <li>• Ask any questions about the property or amenities</li>
                                    <li>• Mention what you're looking forward to about the stay</li>
                                    <li>• Be polite and respectful in your message</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <div class="flex space-x-4">
                        <a href="{% url 'core:your_trips' %}" 
                           class="flex-1 bg-gray-300 hover:bg-gray-400 text-gray-700 py-3 px-4 rounded-lg transition-colors text-center font-medium">
                            Cancel
                        </a>
                        <button type="submit" 
                                id="submitBtn"
                                class="flex-1 bg-blue-600 hover:bg-blue-700 text-white py-3 px-4 rounded-lg transition-colors font-medium">
                            <i class="fas fa-paper-plane mr-2"></i>
                            Send Message
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
document.getElementById('contactForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const form = this;
    const formData = new FormData(form);
    const submitBtn = document.getElementById('submitBtn');
    const originalText = submitBtn.innerHTML;
    
    // Show loading state
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Sending...';
    submitBtn.disabled = true;
    
    fetch(form.action, {
        method: 'POST',
        body: formData,
        headers: {
            'X-CSRFToken': formData.get('csrfmiddlewaretoken')
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Show success message
            document.querySelector('.lg\\:col-span-2').innerHTML = `
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 text-center">
                    <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-check text-green-600 text-2xl"></i>
                    </div>
                    <h2 class="text-2xl font-bold text-gray-900 mb-2">Message Sent!</h2>
                    <p class="text-gray-600 mb-6">${data.message}</p>
                    <div class="space-y-3">
                        <a href="{% url 'core:your_trips' %}" class="inline-block bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium transition-colors">
                            Back to Your Trips
                        </a>
                        <br>
                        <a href="{{ room.get_absolute_url }}" class="inline-block text-blue-600 hover:text-blue-800 transition-colors">
                            View Property
                        </a>
                    </div>
                </div>
            `;
        } else {
            alert('Error: ' + data.message);
            submitBtn.innerHTML = originalText;
            submitBtn.disabled = false;
        }
    })
    .catch(error => {
        alert('An error occurred while sending your message.');
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
    });
});
</script>
{% endblock %}
