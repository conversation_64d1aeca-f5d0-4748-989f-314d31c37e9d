/* Styles personnalisés pour le logo Nestria */

/* Styles pour l'image logo Nestria avec background transparent */
.nestria-logo-img {
    transition: all 0.3s ease;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
}

.nestria-logo-img:hover {
    transform: scale(1.05);
    filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.15));
}

/* Animation de rotation douce pour le logo */
.nestria-logo-rotate {
    animation: gentle-rotate 20s linear infinite;
}

@keyframes gentle-rotate {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

/* Animation de pulsation pour le logo hero */
.nestria-logo-pulse {
    animation: logo-pulse 3s ease-in-out infinite;
}

@keyframes logo-pulse {
    0%, 100% {
        transform: scale(1);
        filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.1));
    }
    50% {
        transform: scale(1.02);
        filter: drop-shadow(0 8px 16px rgba(0, 0, 0, 0.2));
    }
}

/* Style pour le texte NESTRIA avec effet arc-en-ciel */
.nestria-text {
    background: linear-gradient(45deg, 
        #2563eb, /* blue-600 */
        #0d9488, /* teal-600 */
        #1d4ed8, /* blue-700 */
        #0f766e, /* teal-700 */
        #1e40af, /* blue-800 */
        #115e59, /* teal-800 */
        #1e3a8a  /* blue-900 */
    );
    background-size: 200% 200%;
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    animation: rainbow 3s ease-in-out infinite;
}

@keyframes rainbow {
    0%, 100% {
        background-position: 0% 50%;
    }
    50% {
        background-position: 100% 50%;
    }
}

/* Style pour le texte en arc autour du logo */
.nestria-arc-text {
    font-family: 'Inter', sans-serif;
    font-weight: 700;
    letter-spacing: 0.1em;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* Effet de survol pour le logo complet */
.nestria-logo-container:hover .nestria-houses {
    animation-duration: 1s;
}

.nestria-logo-container:hover .nestria-sun {
    animation-duration: 1s;
}

/* Responsive adjustments */
@media (max-width: 640px) {
    .nestria-logo {
        width: 3rem;
        height: 3rem;
    }
    
    .nestria-text {
        font-size: 1.5rem;
    }
}

/* Dark mode adjustments */
@media (prefers-color-scheme: dark) {
    .nestria-arc-text {
        color: rgba(255, 255, 255, 0.8);
    }
}

/* Effet de pulsation pour le logo principal sur la page d'accueil */
.nestria-hero-logo {
    animation: pulse-glow 4s ease-in-out infinite;
}

@keyframes pulse-glow {
    0%, 100% {
        box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
    }
    50% {
        box-shadow: 0 0 40px rgba(59, 130, 246, 0.6);
    }
}

/* Style pour les bordures du logo */
.nestria-border {
    border: 2px solid rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
}

/* Animation d'entrée pour le logo */
.nestria-fade-in {
    animation: fadeInUp 1s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}
