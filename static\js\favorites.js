// Favorites functionality for Nestria

class FavoritesManager {
    constructor() {
        this.init();
    }

    init() {
        this.bindEvents();
        this.loadFavoriteStates();
    }

    bindEvents() {
        // Handle all favorite buttons
        document.addEventListener('click', (e) => {
            if (e.target.closest('.favorite-btn')) {
                e.preventDefault();
                e.stopPropagation();
                this.toggleFavorite(e.target.closest('.favorite-btn'));
            }
        });
    }

    async toggleFavorite(button) {
        const propertyId = button.dataset.propertyId;
        const icon = button.querySelector('i');
        
        // Check if user is authenticated by checking if we can find a CSRF token
        const csrfToken = this.getCSRFToken();
        if (!csrfToken) {
            this.showLoginPrompt();
            return;
        }

        // Disable button during request
        button.disabled = true;
        const originalClass = icon.className;

        try {
            const response = await fetch(`/toggle-favorite/${propertyId}/`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': csrfToken,
                }
            });

            const data = await response.json();

            if (data.success) {
                if (data.is_favorited) {
                    icon.className = 'fas fa-heart text-red-500 text-xl';
                    this.showNotification('Added to favorites!', 'success');
                } else {
                    icon.className = 'far fa-heart text-white text-xl';
                    this.showNotification('Removed from favorites', 'info');
                    
                    // If on favorites page, remove the card
                    if (window.location.pathname.includes('favorites')) {
                        this.removeFromFavoritesPage(button);
                    }
                }
            } else {
                icon.className = originalClass;
                this.showNotification(data.message || 'Error updating favorites', 'error');
            }
        } catch (error) {
            console.error('Error toggling favorite:', error);
            icon.className = originalClass;
            this.showNotification('Network error. Please try again.', 'error');
        } finally {
            button.disabled = false;
        }
    }

    removeFromFavoritesPage(button) {
        const card = button.closest('.property-card');
        if (card) {
            card.style.transition = 'opacity 0.3s ease';
            card.style.opacity = '0';
            
            setTimeout(() => {
                card.remove();
                
                // Check if no more favorites
                const remainingCards = document.querySelectorAll('.property-card');
                if (remainingCards.length === 0) {
                    location.reload(); // Show empty state
                }
            }, 300);
        }
    }

    async loadFavoriteStates() {
        try {
            const response = await fetch('/user-favorites/');
            const data = await response.json();
            
            if (data.success) {
                data.favorites.forEach(roomId => {
                    const button = document.querySelector(`[data-property-id="${roomId}"]`);
                    if (button) {
                        const icon = button.querySelector('i');
                        icon.className = 'fas fa-heart text-red-500';
                    }
                });
            }
        } catch (error) {
            console.error('Error loading favorite states:', error);
        }
    }

    showLoginPrompt() {
        if (confirm('Please log in to add favorites. Would you like to go to the login page?')) {
            window.location.href = '/login/';
        }
    }

    showNotification(message, type = 'info') {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `fixed top-4 right-4 px-6 py-3 rounded-lg text-white z-50 transition-all duration-300 ${
            type === 'success' ? 'bg-green-500' : 
            type === 'error' ? 'bg-red-500' : 'bg-blue-500'
        }`;
        notification.textContent = message;
        
        document.body.appendChild(notification);
        
        // Animate in
        setTimeout(() => notification.style.transform = 'translateX(0)', 10);
        
        // Remove after 3 seconds
        setTimeout(() => {
            notification.style.opacity = '0';
            setTimeout(() => notification.remove(), 300);
        }, 3000);
    }

    getCSRFToken() {
        const token = document.querySelector('[name=csrfmiddlewaretoken]');
        return token ? token.value : '';
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new FavoritesManager();
});


