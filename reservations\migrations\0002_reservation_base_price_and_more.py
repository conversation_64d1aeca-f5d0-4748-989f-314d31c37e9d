# Generated by Django 5.2.5 on 2025-08-17 14:28

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('reservations', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='reservation',
            name='base_price',
            field=models.DecimalField(decimal_places=2, default=0, max_digits=10),
        ),
        migrations.AddField(
            model_name='reservation',
            name='confirmation_number',
            field=models.CharField(blank=True, max_length=20, unique=True),
        ),
        migrations.AddField(
            model_name='reservation',
            name='guest_email',
            field=models.EmailField(default='<EMAIL>', max_length=254),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='reservation',
            name='guest_first_name',
            field=models.CharField(default='Guest', max_length=100),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='reservation',
            name='guest_last_name',
            field=models.Char<PERSON>ield(default='User', max_length=100),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='reservation',
            name='guest_phone',
            field=models.CharField(blank=True, max_length=20),
        ),
        migrations.AddField(
            model_name='reservation',
            name='payment_method',
            field=models.CharField(default='card', max_length=20),
        ),
        migrations.AlterField(
            model_name='reservation',
            name='nights',
            field=models.PositiveIntegerField(default=1),
        ),
        migrations.AlterField(
            model_name='reservation',
            name='price_per_night',
            field=models.DecimalField(decimal_places=2, default=0, max_digits=10),
        ),
        migrations.AlterField(
            model_name='reservation',
            name='status',
            field=models.CharField(choices=[('pending', 'Pending'), ('confirmed', 'Confirmed'), ('cancelled', 'Cancelled'), ('completed', 'Completed')], default='confirmed', max_length=20),
        ),
    ]
