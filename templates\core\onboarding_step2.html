<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>How to Search - Step 2</title>
    {% load static %}
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'nestria-red': '#FF5A5F',
                    }
                }
            }
        }
    </script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body class="bg-gray-50 min-h-screen flex items-center justify-center p-4">
    <div class="bg-white rounded-2xl shadow-xl w-full max-w-lg p-8 relative">
        <!-- Progress Bar -->
        <div class="mb-8">
            <div class="flex justify-between items-center mb-2">
                <span class="text-sm text-gray-600">Step 2 of 3</span>
                <span class="text-sm text-gray-600">67%</span>
            </div>
            <div class="w-full bg-gray-200 rounded-full h-2">
                <div class="bg-nestria-red h-2 rounded-full" style="width: 67%"></div>
            </div>
        </div>

        <!-- Content -->
        <div class="text-center mb-8">
            <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <i class="fas fa-search text-blue-600 text-2xl"></i>
            </div>
            <h1 class="text-2xl font-bold text-gray-900 mb-4">How to Search</h1>
            <p class="text-gray-600 mb-6">
                Finding your perfect stay is easy with our powerful search tools.
            </p>
        </div>

        <!-- Search Steps -->
        <div class="space-y-6 mb-8">
            <div class="flex items-start space-x-4">
                <div class="w-8 h-8 bg-nestria-red text-white rounded-full flex items-center justify-center font-bold text-sm">
                    1
                </div>
                <div>
                    <h3 class="font-semibold text-gray-900 mb-1">Choose Your Destination</h3>
                    <p class="text-sm text-gray-600">Enter a city, neighborhood, or landmark in the search bar</p>
                </div>
            </div>
            
            <div class="flex items-start space-x-4">
                <div class="w-8 h-8 bg-nestria-red text-white rounded-full flex items-center justify-center font-bold text-sm">
                    2
                </div>
                <div>
                    <h3 class="font-semibold text-gray-900 mb-1">Select Your Dates</h3>
                    <p class="text-sm text-gray-600">Pick your check-in and check-out dates</p>
                </div>
            </div>
            
            <div class="flex items-start space-x-4">
                <div class="w-8 h-8 bg-nestria-red text-white rounded-full flex items-center justify-center font-bold text-sm">
                    3
                </div>
                <div>
                    <h3 class="font-semibold text-gray-900 mb-1">Add Guests</h3>
                    <p class="text-sm text-gray-600">Specify the number of adults, children, and infants</p>
                </div>
            </div>
            
            <div class="flex items-start space-x-4">
                <div class="w-8 h-8 bg-nestria-red text-white rounded-full flex items-center justify-center font-bold text-sm">
                    4
                </div>
                <div>
                    <h3 class="font-semibold text-gray-900 mb-1">Filter & Browse</h3>
                    <p class="text-sm text-gray-600">Use filters to find exactly what you're looking for</p>
                </div>
            </div>
        </div>

        <!-- Navigation -->
        <div class="flex justify-between">
            <a href="{% url 'core:onboarding_step1' %}" class="px-6 py-3 text-gray-600 hover:text-gray-800 font-medium">
                Back
            </a>
            <a href="{% url 'core:onboarding_step3' %}" class="px-8 py-3 bg-nestria-red text-white rounded-lg font-semibold hover:bg-red-600 transition-all">
                Next
            </a>
        </div>
    </div>
</body>
</html>
