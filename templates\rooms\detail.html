{% extends 'base.html' %}
{% load static %}

{% block title %}{{ room.title }} - Airbnb Clone{% endblock %}

{% block content %}
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Breadcrumb -->
    <nav class="breadcrumb mb-6">
        <a href="{% url 'core:home' %}">Home</a>
        <span class="mx-2">/</span>
        <a href="{% url 'search:results' %}?location={{ room.city }}">{{ room.city }}</a>
        <span class="mx-2">/</span>
        <span>{{ room.title }}</span>
    </nav>

    <!-- Title and Actions -->
    <div class="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
        <div>
            <h1 class="text-2xl md:text-3xl font-bold text-gray-900 mb-2">{{ room.title }}</h1>
            <div class="flex items-center space-x-4 text-sm text-gray-600">
                {% if room.average_rating > 0 %}
                    <div class="flex items-center">
                        <i class="fas fa-star text-airbnb-red"></i>
                        <span class="ml-1 font-medium">{{ room.average_rating|floatformat:1 }}</span>
                        <span class="ml-1">({{ room.review_count }} review{{ room.review_count|pluralize }})</span>
                    </div>
                {% endif %}
                <span>{{ room.city }}, {{ room.country }}</span>
            </div>
        </div>
        <div class="flex items-center space-x-4 mt-4 md:mt-0">
            <button class="favorite-btn flex items-center space-x-2 text-gray-600 hover:text-airbnb-red transition-colors" data-property-id="{{ room.id }}">
                <i class="far fa-heart"></i>
                <span>Save</span>
            </button>
            <button class="share-btn flex items-center space-x-2 text-gray-600 hover:text-airbnb-red transition-colors" onclick="shareProperty()">
                <i class="fas fa-share"></i>
                <span>Share</span>
            </button>
        </div>
    </div>

    <!-- Image Gallery -->
    <div class="mb-8">
        {% if room.images.all %}
            <div class="grid grid-cols-1 md:grid-cols-2 gap-2 rounded-lg overflow-hidden" x-data="imageGallery()">
                <!-- Main Image -->
                <div class="md:row-span-2">
                    <img src="{{ room.images.first.image.url }}" alt="{{ room.title }}" class="w-full h-64 md:h-full object-cover cursor-pointer" @click="openModal(0)">
                </div>
                
                <!-- Thumbnail Images -->
                {% for image in room.images.all|slice:"1:5" %}
                    <div class="{% if forloop.last and room.images.count > 5 %}relative{% endif %}">
                        <img src="{{ image.image.url }}" alt="{{ room.title }}" class="w-full h-32 object-cover cursor-pointer" @click="openModal({{ forloop.counter }})">
                        {% if forloop.last and room.images.count > 5 %}
                            <div class="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center cursor-pointer" @click="openModal({{ forloop.counter }})">
                                <span class="text-white font-medium">+{{ room.images.count|add:"-5" }} more</span>
                            </div>
                        {% endif %}
                    </div>
                {% endfor %}
                
                <!-- Modal -->
                <div x-show="modalOpen" @click.away="closeModal()" x-transition class="fixed inset-0 bg-black bg-opacity-75 z-50 flex items-center justify-center p-4">
                    <div class="relative max-w-4xl w-full">
                        <button @click="closeModal()" class="absolute top-4 right-4 text-white text-2xl z-10">
                            <i class="fas fa-times"></i>
                        </button>
                        <img :src="currentImage" alt="{{ room.title }}" class="w-full h-auto max-h-screen object-contain">
                        <button @click="prevImage()" class="absolute left-4 top-1/2 transform -translate-y-1/2 text-white text-2xl">
                            <i class="fas fa-chevron-left"></i>
                        </button>
                        <button @click="nextImage()" class="absolute right-4 top-1/2 transform -translate-y-1/2 text-white text-2xl">
                            <i class="fas fa-chevron-right"></i>
                        </button>
                    </div>
                </div>
            </div>
        {% else %}
            <div class="w-full h-64 bg-gray-200 rounded-lg flex items-center justify-center">
                <i class="fas fa-home text-gray-400 text-6xl"></i>
            </div>
        {% endif %}
    </div>

    <!-- Main Content -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <!-- Left Column - Property Details -->
        <div class="lg:col-span-2">
            <!-- Host Info -->
            <div class="border-b border-gray-200 pb-6 mb-6">
                <div class="flex items-center justify-between">
                    <div>
                        <h2 class="text-xl font-semibold text-gray-900 mb-2">
                            {{ room.room_type.name }} hosted by {{ room.host.first_name|default:room.host.username }}
                        </h2>
                        <div class="flex items-center space-x-4 text-gray-600">
                            <span>{{ room.max_guests }} guest{{ room.max_guests|pluralize }}</span>
                            <span>{{ room.bedrooms }} bedroom{{ room.bedrooms|pluralize }}</span>
                            <span>{{ room.beds }} bed{{ room.beds|pluralize }}</span>
                            <span>{{ room.bathrooms }} bathroom{{ room.bathrooms|pluralize }}</span>
                        </div>
                    </div>
                    <div class="host-avatar">
                        <div class="w-12 h-12 bg-gray-300 rounded-full flex items-center justify-center">
                            <i class="fas fa-user text-gray-600"></i>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Description -->
            <div class="border-b border-gray-200 pb-6 mb-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">About this place</h3>
                <p class="text-gray-700 leading-relaxed">{{ room.description }}</p>
            </div>

            <!-- Property Contact Information -->
            {% if room.phone or room.email or room.website %}
            <div class="border-b border-gray-200 pb-6 mb-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Property Contact</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {% if room.phone %}
                    <div class="flex items-center space-x-3">
                        <i class="fas fa-phone text-blue-600"></i>
                        <div>
                            <span class="text-gray-700 font-medium">Phone</span>
                            <div>
                                <a href="tel:{{ room.phone }}" class="text-blue-600 hover:text-blue-800 transition-colors">{{ room.phone }}</a>
                            </div>
                        </div>
                    </div>
                    {% endif %}
                    {% if room.email %}
                    <div class="flex items-center space-x-3">
                        <i class="fas fa-envelope text-blue-600"></i>
                        <div>
                            <span class="text-gray-700 font-medium">Email</span>
                            <div>
                                <a href="mailto:{{ room.email }}" class="text-blue-600 hover:text-blue-800 transition-colors">{{ room.email }}</a>
                            </div>
                        </div>
                    </div>
                    {% endif %}
                    {% if room.website %}
                    <div class="flex items-center space-x-3">
                        <i class="fas fa-globe text-blue-600"></i>
                        <div>
                            <span class="text-gray-700 font-medium">Website</span>
                            <div>
                                {% if room.website|slice:":4" == "http" %}
                                    <a href="{{ room.website }}" target="_blank" class="text-blue-600 hover:text-blue-800 transition-colors">Visit Website <i class="fas fa-external-link-alt text-xs ml-1"></i></a>
                                {% else %}
                                    <a href="https://{{ room.website }}" target="_blank" class="text-blue-600 hover:text-blue-800 transition-colors">Visit Website <i class="fas fa-external-link-alt text-xs ml-1"></i></a>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
            {% endif %}

            <!-- Amenities -->
            {% if room.amenities.all %}
                <div class="border-b border-gray-200 pb-6 mb-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">What this place offers</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        {% for amenity in room.amenities.all %}
                            <div class="flex items-center space-x-3">
                                <i class="{{ amenity.icon }} amenity-icon"></i>
                                <span class="text-gray-700">{{ amenity.name }}</span>
                            </div>
                        {% endfor %}
                    </div>
                </div>
            {% endif %}

            <!-- Reviews -->
            {% if room.reviews.all %}
                <div class="border-b border-gray-200 pb-6 mb-6">
                    <div class="flex items-center space-x-4 mb-6">
                        <i class="fas fa-star text-airbnb-red"></i>
                        <h3 class="text-lg font-semibold text-gray-900">
                            {{ room.average_rating|floatformat:1 }} · {{ room.review_count }} review{{ room.review_count|pluralize }}
                        </h3>
                    </div>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        {% for review in room.reviews.all|slice:":6" %}
                            <div class="review-card">
                                <div class="flex items-center space-x-3 mb-3">
                                    <div class="w-10 h-10 bg-gray-300 rounded-full flex items-center justify-center">
                                        <i class="fas fa-user text-gray-600 text-sm"></i>
                                    </div>
                                    <div>
                                        <div class="font-medium text-gray-900">{{ review.user.first_name|default:review.user.username }}</div>
                                        <div class="text-sm text-gray-500">{{ review.created_at|date:"F Y" }}</div>
                                    </div>
                                </div>
                                <div class="flex items-center mb-2">
                                    {% for i in "12345" %}
                                        <i class="fas fa-star text-sm {% if forloop.counter <= review.rating %}text-airbnb-red{% else %}text-gray-300{% endif %}"></i>
                                    {% endfor %}
                                </div>
                                <p class="text-gray-700 text-sm">{{ review.comment }}</p>
                            </div>
                        {% endfor %}
                    </div>
                    
                    {% if room.review_count > 6 %}
                        <button class="mt-6 border border-gray-900 text-gray-900 px-6 py-3 rounded-lg font-medium hover:bg-gray-50 transition-colors">
                            Show all {{ room.review_count }} reviews
                        </button>
                    {% endif %}
                </div>
            {% endif %}

            <!-- Location -->
            <div class="mb-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Where you'll be</h3>
                <p class="text-gray-700 mb-4">{{ room.address }}, {{ room.city }}, {{ room.country }}</p>

                <!-- Interactive Map -->
                {% if room.latitude and room.longitude %}
                    <div id="property-map" class="w-full h-64 rounded-lg mb-4"></div>
                    <div class="text-sm text-gray-500 mt-2">
                        <p><i class="fas fa-info-circle mr-1"></i> Exact location will be provided after booking confirmation</p>
                    </div>
                {% else %}
                    <div class="map-container bg-gray-200 rounded-lg h-64 flex items-center justify-center">
                        <div class="text-center">
                            <i class="fas fa-map-marker-alt text-gray-400 text-3xl mb-2"></i>
                            <p class="text-gray-500">Location map not available</p>
                        </div>
                    </div>
                {% endif %}
            </div>
        </div>

        <!-- Right Column - Booking Form -->
        <div class="lg:col-span-1">
            <div class="booking-form">
                <div class="flex items-center justify-between mb-4">
                    <div>
                        <span class="text-2xl font-bold text-gray-900 price-display">${{ room.price_per_night|floatformat:0 }}</span>
                        <span class="text-gray-600">/ night</span>
                    </div>
                    {% if room.average_rating > 0 %}
                        <div class="flex items-center text-sm">
                            <i class="fas fa-star text-airbnb-red"></i>
                            <span class="ml-1 font-medium">{{ room.average_rating|floatformat:1 }}</span>
                            <span class="ml-1 text-gray-600">({{ room.review_count }})</span>
                        </div>
                    {% endif %}
                </div>

                <form action="{% url 'reservations:checkout' room.id %}" method="GET" x-data="bookingForm()">
                    <div class="grid grid-cols-2 gap-0 border border-gray-300 rounded-lg mb-4">
                        <div class="p-3 border-r border-gray-300">
                            <label class="block text-xs font-semibold text-gray-600 uppercase tracking-wide mb-1">Check-in</label>
                            <input type="date" name="check_in" required class="w-full border-0 p-0 text-sm focus:ring-0" x-model="checkIn" :min="today">
                        </div>
                        <div class="p-3">
                            <label class="block text-xs font-semibold text-gray-600 uppercase tracking-wide mb-1">Check-out</label>
                            <input type="date" name="check_out" required class="w-full border-0 p-0 text-sm focus:ring-0" x-model="checkOut" :min="checkIn || today">
                        </div>
                    </div>

                    <div class="border border-gray-300 rounded-lg p-3 mb-4" x-data="{ open: false }">
                        <button type="button" @click="open = !open" class="w-full text-left">
                            <label class="block text-xs font-semibold text-gray-600 uppercase tracking-wide mb-1">Guests</label>
                            <span class="text-sm" x-text="guestDisplay"></span>
                        </button>
                        
                        <div x-show="open" @click.away="open = false" x-transition class="mt-4 pt-4 border-t border-gray-200">
                            <div class="space-y-4">
                                <div class="flex items-center justify-between">
                                    <div>
                                        <div class="font-medium">Adults</div>
                                        <div class="text-sm text-gray-500">Ages 13 or above</div>
                                    </div>
                                    <div class="flex items-center space-x-3">
                                        <button type="button" @click="decrementGuests('adults')" class="w-8 h-8 rounded-full border border-gray-300 flex items-center justify-center">-</button>
                                        <span x-text="guests.adults" class="w-8 text-center"></span>
                                        <button type="button" @click="incrementGuests('adults')" class="w-8 h-8 rounded-full border border-gray-300 flex items-center justify-center">+</button>
                                    </div>
                                </div>
                                <div class="flex items-center justify-between">
                                    <div>
                                        <div class="font-medium">Children</div>
                                        <div class="text-sm text-gray-500">Ages 2-12</div>
                                    </div>
                                    <div class="flex items-center space-x-3">
                                        <button type="button" @click="decrementGuests('children')" class="w-8 h-8 rounded-full border border-gray-300 flex items-center justify-center">-</button>
                                        <span x-text="guests.children" class="w-8 text-center"></span>
                                        <button type="button" @click="incrementGuests('children')" class="w-8 h-8 rounded-full border border-gray-300 flex items-center justify-center">+</button>
                                    </div>
                                </div>
                                <div class="flex items-center justify-between">
                                    <div>
                                        <div class="font-medium">Infants</div>
                                        <div class="text-sm text-gray-500">Under 2</div>
                                    </div>
                                    <div class="flex items-center space-x-3">
                                        <button type="button" @click="decrementGuests('infants')" class="w-8 h-8 rounded-full border border-gray-300 flex items-center justify-center">-</button>
                                        <span x-text="guests.infants" class="w-8 text-center"></span>
                                        <button type="button" @click="incrementGuests('infants')" class="w-8 h-8 rounded-full border border-gray-300 flex items-center justify-center">+</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <input type="hidden" name="adults" :value="guests.adults">
                        <input type="hidden" name="children" :value="guests.children">
                        <input type="hidden" name="infants" :value="guests.infants">
                    </div>

                    <button type="submit" class="w-full bg-airbnb-red text-white py-3 rounded-lg font-medium hover:bg-red-600 transition-colors btn-primary mb-4">
                        Reserve
                    </button>

                    <!-- Contact Host Button -->
                    {% if user.is_authenticated %}
                        <button type="button"
                                onclick="openContactHostModal()"
                                class="w-full bg-gray-100 text-gray-700 py-3 rounded-lg font-medium hover:bg-gray-200 transition-colors border border-gray-300 mb-4">
                            <i class="fas fa-envelope mr-2"></i>
                            Contact Host
                        </button>
                    {% else %}
                        <a href="{% url 'core:login' %}"
                           class="block w-full bg-gray-100 text-gray-700 py-3 rounded-lg font-medium hover:bg-gray-200 transition-colors border border-gray-300 mb-4 text-center">
                            <i class="fas fa-envelope mr-2"></i>
                            Contact Host
                        </a>
                    {% endif %}

                    <p class="text-center text-sm text-gray-600 mb-4">You won't be charged yet</p>

                    <!-- Price Breakdown -->
                    <div x-show="checkIn && checkOut" class="space-y-3 pt-4 border-t border-gray-200">
                        <div class="flex justify-between text-sm">
                            <span>${{ room.price_per_night|floatformat:0 }} x <span x-text="nights"></span> nights</span>
                            <span x-text="'$' + ({{ room.price_per_night }} * nights).toFixed(0)"></span>
                        </div>
                        {% if room.cleaning_fee > 0 %}
                            <div class="flex justify-between text-sm">
                                <span>Cleaning fee</span>
                                <span>${{ room.cleaning_fee|floatformat:0 }}</span>
                            </div>
                        {% endif %}
                        {% if room.service_fee > 0 %}
                            <div class="flex justify-between text-sm">
                                <span>Service fee</span>
                                <span>${{ room.service_fee|floatformat:0 }}</span>
                            </div>
                        {% endif %}
                        <div class="flex justify-between font-semibold pt-3 border-t border-gray-200">
                            <span>Total</span>
                            <span x-text="'$' + totalPrice.toFixed(0)"></span>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function imageGallery() {
    return {
        modalOpen: false,
        currentImageIndex: 0,
        images: [
            {% for image in room.images.all %}
                '{{ image.image.url }}'{% if not forloop.last %},{% endif %}
            {% endfor %}
        ],
        
        get currentImage() {
            return this.images[this.currentImageIndex] || '';
        },
        
        openModal(index) {
            this.currentImageIndex = index;
            this.modalOpen = true;
        },
        
        closeModal() {
            this.modalOpen = false;
        },
        
        nextImage() {
            this.currentImageIndex = (this.currentImageIndex + 1) % this.images.length;
        },
        
        prevImage() {
            this.currentImageIndex = this.currentImageIndex > 0 ? this.currentImageIndex - 1 : this.images.length - 1;
        }
    }
}

function bookingForm() {
    return {
        checkIn: '',
        checkOut: '',
        guests: {
            adults: 1,
            children: 0,
            infants: 0
        },
        today: new Date().toISOString().split('T')[0],
        
        get guestDisplay() {
            const total = this.guests.adults + this.guests.children + this.guests.infants;
            return total === 1 ? '1 guest' : `${total} guests`;
        },
        
        get nights() {
            if (this.checkIn && this.checkOut) {
                const start = new Date(this.checkIn);
                const end = new Date(this.checkOut);
                return Math.max(0, Math.ceil((end - start) / (1000 * 60 * 60 * 24)));
            }
            return 0;
        },
        
        get totalPrice() {
            const basePrice = {{ room.price_per_night }} * this.nights;
            const cleaningFee = {{ room.cleaning_fee }};
            const serviceFee = {{ room.service_fee }};
            return basePrice + cleaningFee + serviceFee;
        },
        
        incrementGuests(type) {
            const maxGuests = {{ room.max_guests }};
            const currentTotal = this.guests.adults + this.guests.children + this.guests.infants;
            
            if (currentTotal < maxGuests) {
                if (type === 'adults' && this.guests.adults < 16) {
                    this.guests.adults++;
                } else if (type === 'children' && this.guests.children < 5) {
                    this.guests.children++;
                } else if (type === 'infants' && this.guests.infants < 5) {
                    this.guests.infants++;
                }
            }
        },
        
        decrementGuests(type) {
            if (type === 'adults' && this.guests.adults > 1) {
                this.guests.adults--;
            } else if (type === 'children' && this.guests.children > 0) {
                this.guests.children--;
            } else if (type === 'infants' && this.guests.infants > 0) {
                this.guests.infants--;
            }
        }
    }
}

// Initialize property map
document.addEventListener('DOMContentLoaded', function() {
    {% if room.latitude and room.longitude %}
        const lat = {{ room.latitude }};
        const lng = {{ room.longitude }};

        // Create map
        const map = L.map('property-map').setView([lat, lng], 15);

        // Add tile layer
        L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
            attribution: '© <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors',
            maxZoom: 18
        }).addTo(map);

        // Custom marker icon
        const customIcon = L.divIcon({
            html: '<div class="bg-airbnb-red text-white rounded-full w-10 h-10 flex items-center justify-center shadow-lg border-2 border-white"><i class="fas fa-home"></i></div>',
            className: 'custom-property-marker',
            iconSize: [40, 40],
            iconAnchor: [20, 40]
        });

        // Add marker for the property
        const marker = L.marker([lat, lng], { icon: customIcon }).addTo(map);

        // Add popup with property info
        marker.bindPopup(`
            <div class="text-center p-3">
                <h3 class="font-bold text-lg mb-2">{{ room.title }}</h3>
                <p class="text-gray-600 mb-2">{{ room.address }}</p>
                <p class="text-sm text-gray-500">{{ room.city }}, {{ room.country }}</p>
                <div class="mt-2 text-sm">
                    <span class="font-semibold text-airbnb-red">${{ room.price_per_night|floatformat:0 }}</span>
                    <span class="text-gray-600">/ night</span>
                </div>
            </div>
        `);

        // Add circle to show approximate area (for privacy)
        L.circle([lat, lng], {
            color: '#FF5A5F',
            fillColor: '#FF5A5F',
            fillOpacity: 0.1,
            radius: 200
        }).addTo(map);

    {% endif %}
});

// Contact Host Modal Functions
function openContactHostModal() {
    document.getElementById('contactHostModal').classList.remove('hidden');
    document.body.style.overflow = 'hidden';
}

function closeContactHostModal() {
    document.getElementById('contactHostModal').classList.add('hidden');
    document.body.style.overflow = 'auto';
    // Reset form
    document.getElementById('contactHostForm').reset();
}

function sendMessageToHost() {
    const form = document.getElementById('contactHostForm');
    const formData = new FormData(form);

    // Show loading state
    const submitBtn = document.getElementById('sendMessageBtn');
    const originalText = submitBtn.innerHTML;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Sending...';
    submitBtn.disabled = true;

    fetch(`{% url 'core:contact_host' room.id %}`, {
        method: 'POST',
        body: formData,
        headers: {
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Show success message
            document.getElementById('contactHostModal').innerHTML = `
                <div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
                    <div class="bg-white rounded-lg max-w-md w-full p-6">
                        <div class="text-center">
                            <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                                <i class="fas fa-check text-green-600 text-2xl"></i>
                            </div>
                            <h3 class="text-lg font-semibold text-gray-900 mb-2">Message Sent!</h3>
                            <p class="text-gray-600 mb-4">${data.message}</p>
                            <button onclick="closeContactHostModal()" class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg">
                                Close
                            </button>
                        </div>
                    </div>
                </div>
            `;
        } else {
            alert('Error: ' + data.message);
            submitBtn.innerHTML = originalText;
            submitBtn.disabled = false;
        }
    })
    .catch(error => {
        alert('An error occurred while sending your message.');
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
    });
}
</script>

<!-- Contact Host Modal -->
{% if user.is_authenticated %}
<div id="contactHostModal" class="hidden fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
    <div class="bg-white rounded-lg max-w-md w-full p-6">
        <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-semibold text-gray-900">Contact Host</h3>
            <button onclick="closeContactHostModal()" class="text-gray-400 hover:text-gray-600">
                <i class="fas fa-times text-xl"></i>
            </button>
        </div>

        <div class="mb-4">
            <div class="flex items-center space-x-3">
                <div class="w-12 h-12 bg-gray-300 rounded-full flex items-center justify-center">
                    <i class="fas fa-user text-gray-600"></i>
                </div>
                <div>
                    <h4 class="font-medium text-gray-900">{{ room.host.get_full_name|default:room.host.username }}</h4>
                    <p class="text-sm text-gray-600">Host of {{ room.title }}</p>
                </div>
            </div>
        </div>

        <form id="contactHostForm" onsubmit="event.preventDefault(); sendMessageToHost();">
            {% csrf_token %}
            <div class="mb-4">
                <label for="subject" class="block text-sm font-medium text-gray-700 mb-2">Subject</label>
                <input type="text"
                       id="subject"
                       name="subject"
                       required
                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                       placeholder="Question about your property">
            </div>

            <div class="mb-6">
                <label for="message" class="block text-sm font-medium text-gray-700 mb-2">Message</label>
                <textarea id="message"
                          name="message"
                          rows="4"
                          required
                          class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                          placeholder="Hi! I'm interested in your property and have a few questions..."></textarea>
            </div>

            <div class="flex space-x-3">
                <button type="button"
                        onclick="closeContactHostModal()"
                        class="flex-1 bg-gray-300 hover:bg-gray-400 text-gray-700 py-2 px-4 rounded-lg transition-colors">
                    Cancel
                </button>
                <button type="submit"
                        id="sendMessageBtn"
                        class="flex-1 bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-lg transition-colors">
                    <i class="fas fa-paper-plane mr-2"></i>
                    Send Message
                </button>
            </div>
        </form>
    </div>
</div>
{% endif %}

<!-- Favorites Script -->
<script src="{% static 'js/favorites.js' %}"></script>
<script>
// Initialize favorites manager
document.addEventListener('DOMContentLoaded', function() {
    new FavoritesManager();
});

// Share functionality
function shareProperty() {
    if (navigator.share) {
        navigator.share({
            title: '{{ room.title }}',
            text: 'Check out this amazing property: {{ room.title }} in {{ room.city }}, {{ room.country }}',
            url: window.location.href
        }).catch(console.error);
    } else {
        // Fallback for browsers that don't support Web Share API
        copyToClipboard(window.location.href);
        showNotification('Link copied to clipboard!', 'success');
    }
}

function copyToClipboard(text) {
    if (navigator.clipboard) {
        navigator.clipboard.writeText(text);
    } else {
        // Fallback for older browsers
        const textArea = document.createElement('textarea');
        textArea.value = text;
        document.body.appendChild(textArea);
        textArea.select();
        document.execCommand('copy');
        document.body.removeChild(textArea);
    }
}

function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `favorite-notification ${type} show`;
    notification.innerHTML = `
        <i class="fas ${type === 'success' ? 'fa-check-circle' : type === 'error' ? 'fa-exclamation-circle' : 'fa-info-circle'}"></i>
        <span>${message}</span>
    `;

    document.body.appendChild(notification);

    // Auto remove after 3 seconds
    setTimeout(() => {
        notification.classList.remove('show');
        setTimeout(() => {
            if (document.body.contains(notification)) {
                document.body.removeChild(notification);
            }
        }, 300);
    }, 3000);
}
</script>
{% endblock %}
