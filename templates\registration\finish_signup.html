<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Finish signing up - Nestria</title>
    {% load static %}
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'nestria-red': '#FF5A5F',
                        'nestria-pink': '#E91E63',
                        'nestria-blue': '#4F46E5',
                    }
                }
            }
        }
    </script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .glass-card {
            backdrop-filter: blur(16px);
            background: rgba(255, 255, 255, 0.95);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .input-modern {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }
        .input-modern:focus {
            transform: translateY(-2px);
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }
        .btn-modern {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }
        .btn-modern:hover {
            transform: translateY(-3px);
            box-shadow: 0 20px 25px -5px rgba(255, 90, 95, 0.4), 0 10px 10px -5px rgba(255, 90, 95, 0.2);
        }
        .floating-label {
            transition: all 0.3s ease;
        }
        .input-group:focus-within .floating-label {
            transform: translateY(-1.5rem) scale(0.85);
            color: #FF5A5F;
        }
        .input-group.has-value .floating-label {
            transform: translateY(-1.5rem) scale(0.85);
            color: #6B7280;
        }
    </style>
</head>
<body class="gradient-bg min-h-screen flex items-center justify-center p-4">
    <div class="glass-card w-full max-w-lg mx-auto rounded-3xl shadow-2xl p-8">
        <!-- Header avec animation -->
        <div class="text-center mb-8">
            <div class="w-16 h-16 bg-gradient-to-r from-nestria-red to-nestria-pink rounded-full flex items-center justify-center mx-auto mb-4 shadow-lg">
                <i class="fas fa-user-check text-white text-2xl"></i>
            </div>
            <button onclick="window.history.back()" class="absolute top-6 left-6 p-3 hover:bg-white/20 rounded-full transition-all">
                <i class="fas fa-arrow-left text-xl text-gray-600"></i>
            </button>
        </div>

        <!-- Title Section -->
        <div class="text-center mb-10">
            <h1 class="text-3xl font-bold text-gray-900 mb-3">Finish signing up</h1>
            <p class="text-gray-600 text-lg">Complete your profile to join Nestria</p>
        </div>

        <!-- Form -->
        <form method="post" class="space-y-8">
            {% csrf_token %}

            <!-- Legal Name Section -->
            <div class="space-y-6">
                <h2 class="text-xl font-semibold text-gray-900 flex items-center">
                    <i class="fas fa-id-card text-nestria-red mr-3"></i>
                    Legal name
                </h2>

                <!-- First Name -->
                <div class="input-group relative">
                    <input type="text"
                           name="first_name"
                           id="first_name"
                           value="{{ request.session.signup_first_name|default:'aymen' }}"
                           class="input-modern w-full px-4 py-4 text-lg border-2 border-gray-200 rounded-xl focus:border-nestria-red focus:ring-0 focus:outline-none bg-white/80"
                           required>
                    <label for="first_name" class="floating-label absolute left-4 top-4 text-gray-500 pointer-events-none">
                        First name on ID
                    </label>
                </div>

                <!-- Last Name -->
                <div class="input-group relative">
                    <input type="text"
                           name="last_name"
                           id="last_name"
                           value="{{ request.session.signup_last_name|default:'malek' }}"
                           class="input-modern w-full px-4 py-4 text-lg border-2 border-gray-200 rounded-xl focus:border-nestria-red focus:ring-0 focus:outline-none bg-white/80"
                           required>
                    <label for="last_name" class="floating-label absolute left-4 top-4 text-gray-500 pointer-events-none">
                        Last name on ID
                    </label>
                </div>

                <div class="bg-blue-50 border-l-4 border-blue-400 p-4 rounded-r-lg">
                    <div class="flex items-start">
                        <i class="fas fa-info-circle text-blue-400 mt-1 mr-3"></i>
                        <p class="text-sm text-blue-800 leading-relaxed">
                            Make sure this matches the name on your government ID. If you go by another name, you can add a
                            <a href="#" class="text-blue-600 underline hover:text-blue-800 font-medium">preferred first name</a>.
                        </p>
                    </div>
                </div>
            </div>

            <!-- Date of Birth Section -->
            <div class="space-y-6">
                <h2 class="text-xl font-semibold text-gray-900 flex items-center">
                    <i class="fas fa-birthday-cake text-nestria-red mr-3"></i>
                    Date of birth
                </h2>

                <!-- Date picker principal -->
                <div id="date-picker-main" class="relative">
                    <input type="date"
                           name="birthdate"
                           id="birthdate"
                           value="1998-10-13"
                           max="2006-12-31"
                           min="1900-01-01"
                           class="input-modern w-full px-4 py-4 text-lg border-2 border-gray-200 rounded-xl focus:border-nestria-red focus:ring-0 focus:outline-none bg-white/80 cursor-pointer"
                           required>
                    <i class="fas fa-calendar-alt absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400 pointer-events-none"></i>
                </div>

                <!-- Fallback avec sélecteurs séparés (caché par défaut) -->
                <div id="date-picker-fallback" class="hidden space-y-3">
                    <div class="grid grid-cols-3 gap-3">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Month</label>
                            <select id="birth-month" class="w-full border border-gray-300 rounded-lg p-3 focus:ring-2 focus:ring-airbnb-pink focus:border-transparent">
                                <option value="">Month</option>
                                <option value="01">January</option>
                                <option value="02">February</option>
                                <option value="03">March</option>
                                <option value="04">April</option>
                                <option value="05">May</option>
                                <option value="06">June</option>
                                <option value="07">July</option>
                                <option value="08">August</option>
                                <option value="09">September</option>
                                <option value="10">October</option>
                                <option value="11">November</option>
                                <option value="12">December</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Day</label>
                            <select id="birth-day" class="w-full border border-gray-300 rounded-lg p-3 focus:ring-2 focus:ring-airbnb-pink focus:border-transparent">
                                <option value="">Day</option>
                                <!-- Days will be populated by JavaScript -->
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Year</label>
                            <select id="birth-year" class="w-full border border-gray-300 rounded-lg p-3 focus:ring-2 focus:ring-airbnb-pink focus:border-transparent">
                                <option value="">Year</option>
                                <!-- Years will be populated by JavaScript -->
                            </select>
                        </div>
                    </div>
                    <button type="button" onclick="switchToDateInput()" class="text-sm text-blue-600 hover:text-blue-800">
                        Use calendar picker instead
                    </button>
                </div>

                <!-- Bouton pour basculer vers les sélecteurs -->
                <button type="button" id="switch-to-selectors" onclick="switchToSelectors()" class="text-sm text-blue-600 hover:text-blue-800 mt-2">
                    Having trouble with the calendar? Use dropdown selectors instead
                </button>

                <!-- Age Validation Message -->
                <div id="age-warning" class="hidden">
                    <div class="flex items-start space-x-2 text-red-600 bg-red-50 border border-red-200 rounded-lg p-3">
                        <i class="fas fa-exclamation-circle mt-0.5"></i>
                        <span class="text-sm">You must be 18 or older to use Nestria. Other people won't see your birthday.</span>
                    </div>
                </div>

                <p class="text-sm text-gray-600 leading-relaxed">
                    To sign up, you need to be at least 18. Your birthday won't be shared with other people who use Nestria.
                </p>
            </div>

            <!-- Contact Info Section -->
            <div class="space-y-4">
                <h2 class="text-lg font-semibold text-gray-900">Contact info</h2>

                <div class="border border-gray-300 rounded-lg p-4 focus-within:border-gray-900 transition-colors">
                    <input type="email"
                           name="email"
                           placeholder="Email"
                           value="{{ request.session.signup_email|default:'<EMAIL>' }}"
                           class="w-full text-base text-gray-900 placeholder-gray-500 border-0 focus:ring-0 focus:outline-none p-0"
                           required>
                </div>

                <p class="text-sm text-gray-600 leading-relaxed">
                    We'll email you trip confirmations and receipts.
                </p>
            </div>

            <!-- Terms and Conditions -->
            <div class="bg-gradient-to-r from-gray-50 to-blue-50 rounded-2xl p-6 border border-gray-200">
                <div class="flex items-start space-x-3">
                    <i class="fas fa-shield-check text-green-500 text-xl mt-1"></i>
                    <div>
                        <h3 class="font-semibold text-gray-900 mb-2">Privacy & Terms</h3>
                        <p class="text-sm text-gray-700 leading-relaxed">
                            By selecting <span class="font-semibold text-nestria-red">Complete Registration</span>, I agree to Nestria's
                            <a href="#" class="text-nestria-red underline hover:text-nestria-pink font-medium">Terms of Service</a>,
                            <a href="#" class="text-nestria-red underline hover:text-nestria-pink font-medium">Privacy Policy</a>,
                            and <a href="#" class="text-nestria-red underline hover:text-nestria-pink font-medium">Community Guidelines</a>.
                        </p>
                    </div>
                </div>
            </div>

            <!-- Continue Button -->
            <button type="submit" id="submit-btn" class="btn-modern w-full bg-gradient-to-r from-nestria-red to-nestria-pink text-white py-5 rounded-2xl font-bold text-lg shadow-lg">
                <i class="fas fa-check-circle mr-3"></i>
                Complete Registration
            </button>
        </form>
    </div>

    <script>
        const birthdateInput = document.getElementById('birthdate');
        const ageWarning = document.getElementById('age-warning');
        const submitBtn = document.getElementById('submit-btn');

        birthdateInput.addEventListener('change', function() {
            validateAge();
        });

        // Améliorer l'interaction avec le calendrier
        birthdateInput.addEventListener('click', function() {
            // Forcer l'ouverture du calendrier sur les navigateurs qui le supportent
            if (this.showPicker) {
                this.showPicker();
            }
        });

        // Définir la date maximale (18 ans avant aujourd'hui)
        const today = new Date();
        const maxDate = new Date(today.getFullYear() - 18, today.getMonth(), today.getDate());
        const maxDateString = maxDate.toISOString().split('T')[0];
        birthdateInput.setAttribute('max', maxDateString);

        // Initialiser les sélecteurs de fallback
        initializeFallbackSelectors();

        function initializeFallbackSelectors() {
            const currentYear = new Date().getFullYear();
            const yearSelect = document.getElementById('birth-year');
            const daySelect = document.getElementById('birth-day');

            // Remplir les années (de 1900 à année actuelle - 18)
            for (let year = currentYear - 18; year >= 1900; year--) {
                const option = document.createElement('option');
                option.value = year;
                option.textContent = year;
                yearSelect.appendChild(option);
            }

            // Remplir les jours (1-31)
            for (let day = 1; day <= 31; day++) {
                const option = document.createElement('option');
                option.value = day.toString().padStart(2, '0');
                option.textContent = day;
                daySelect.appendChild(option);
            }

            // Écouter les changements des sélecteurs
            document.getElementById('birth-month').addEventListener('change', updateDateFromSelectors);
            document.getElementById('birth-day').addEventListener('change', updateDateFromSelectors);
            document.getElementById('birth-year').addEventListener('change', updateDateFromSelectors);
        }

        function updateDateFromSelectors() {
            const month = document.getElementById('birth-month').value;
            const day = document.getElementById('birth-day').value;
            const year = document.getElementById('birth-year').value;

            if (month && day && year) {
                const dateString = `${year}-${month}-${day}`;
                birthdateInput.value = dateString;
                validateAge();
            }
        }

        function switchToSelectors() {
            document.getElementById('date-picker-main').classList.add('hidden');
            document.getElementById('date-picker-fallback').classList.remove('hidden');
            document.getElementById('switch-to-selectors').classList.add('hidden');
        }

        function switchToDateInput() {
            document.getElementById('date-picker-main').classList.remove('hidden');
            document.getElementById('date-picker-fallback').classList.add('hidden');
            document.getElementById('switch-to-selectors').classList.remove('hidden');
        }

        function validateAge() {
            const birthdate = new Date(birthdateInput.value);
            const today = new Date();
            const age = today.getFullYear() - birthdate.getFullYear();
            const monthDiff = today.getMonth() - birthdate.getMonth();
            
            // Ajuster l'âge si l'anniversaire n'est pas encore passé cette année
            const actualAge = (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthdate.getDate())) 
                ? age - 1 
                : age;

            const birthdateContainer = birthdateInput.closest('.border');

            if (actualAge < 18) {
                ageWarning.classList.remove('hidden');
                birthdateContainer.classList.add('border-red-500');
                birthdateContainer.classList.remove('border-gray-300');
                submitBtn.disabled = true;
                submitBtn.classList.add('bg-gray-300', 'cursor-not-allowed');
                submitBtn.classList.remove('bg-airbnb-pink', 'hover:bg-pink-600');
            } else {
                ageWarning.classList.add('hidden');
                birthdateContainer.classList.remove('border-red-500');
                birthdateContainer.classList.add('border-gray-300');
                submitBtn.disabled = false;
                submitBtn.classList.remove('bg-gray-300', 'cursor-not-allowed');
                submitBtn.classList.add('bg-airbnb-pink', 'hover:bg-pink-600');
            }
        }

        // Validation du formulaire
        const form = document.querySelector('form');
        
        form.addEventListener('submit', function(e) {
            const firstName = document.querySelector('input[name="first_name"]').value.trim();
            const lastName = document.querySelector('input[name="last_name"]').value.trim();
            const email = document.querySelector('input[name="email"]').value.trim();
            const birthdate = birthdateInput.value;

            if (!firstName || !lastName || !email || !birthdate) {
                e.preventDefault();
                alert('Please fill in all required fields.');
                return;
            }

            // Vérifier l'âge une dernière fois
            const birth = new Date(birthdate);
            const today = new Date();
            const age = today.getFullYear() - birth.getFullYear();
            const monthDiff = today.getMonth() - birth.getMonth();
            const actualAge = (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) 
                ? age - 1 
                : age;

            if (actualAge < 18) {
                e.preventDefault();
                alert('You must be 18 or older to sign up.');
                return;
            }
        });

        // Animation du focus sur tous les inputs
        const inputContainers = document.querySelectorAll('.border');
        const inputs = document.querySelectorAll('input');

        inputs.forEach((input, index) => {
            const container = inputContainers[index];
            if (container) {
                input.addEventListener('focus', function() {
                    container.classList.add('border-gray-900');
                    container.classList.remove('border-gray-300');
                });

                input.addEventListener('blur', function() {
                    container.classList.remove('border-gray-900');
                    container.classList.add('border-gray-300');
                });
            }
        });

        // Gestion des labels flottants
        function initFloatingLabels() {
            const inputGroups = document.querySelectorAll('.input-group');

            inputGroups.forEach(group => {
                const input = group.querySelector('input');
                const label = group.querySelector('.floating-label');

                if (input && label) {
                    // Vérifier si l'input a une valeur au chargement
                    function checkValue() {
                        if (input.value.trim() !== '') {
                            group.classList.add('has-value');
                        } else {
                            group.classList.remove('has-value');
                        }
                    }

                    // Vérifier au chargement
                    checkValue();

                    // Écouter les changements
                    input.addEventListener('input', checkValue);
                    input.addEventListener('blur', checkValue);
                }
            });
        }

        // Animation d'entrée de la carte
        function animateCard() {
            const card = document.querySelector('.glass-card');
            card.style.opacity = '0';
            card.style.transform = 'translateY(30px) scale(0.95)';

            setTimeout(() => {
                card.style.transition = 'all 0.8s cubic-bezier(0.4, 0, 0.2, 1)';
                card.style.opacity = '1';
                card.style.transform = 'translateY(0) scale(1)';
            }, 100);
        }

        // Initialiser au chargement
        document.addEventListener('DOMContentLoaded', function() {
            initFloatingLabels();
            animateCard();
        });
    </script>
</body>
</html>
