#!/usr/bin/env python
"""
Script pour tester le système d'authentification moderne
"""
import requests
import sys

def test_auth_system():
    """Tester toutes les pages d'authentification"""
    base_url = "http://127.0.0.1:8000"
    
    # Pages à tester
    pages = [
        ("/", "Page d'accueil"),
        ("/login/", "Page de login"),
        ("/signup/", "Page de signup"),
        ("/modern-login/", "Page moderne login/signup"),
        ("/auth/google/", "Authentification Google (démo)"),
        ("/auth/facebook/", "Authentification Facebook (démo)"),
        ("/auth/apple/", "Authentification Apple (démo)"),
    ]
    
    print("🧪 Test du système d'authentification moderne\n")
    
    success_count = 0
    total_count = len(pages)
    
    for url, description in pages:
        try:
            response = requests.get(f"{base_url}{url}", allow_redirects=True)
            
            if response.status_code == 200:
                print(f"✅ {description}: OK")
                success_count += 1
            else:
                print(f"❌ {description}: Erreur {response.status_code}")
                
        except Exception as e:
            print(f"❌ {description}: Erreur de connexion - {e}")
    
    print(f"\n📊 Résultats: {success_count}/{total_count} pages fonctionnelles")
    
    if success_count == total_count:
        print("🎉 Tous les tests sont passés avec succès !")
    else:
        print("⚠️  Certaines pages ont des problèmes.")
    
    # Test des comptes utilisateur
    print("\n👥 Comptes de test disponibles:")
    print("- <EMAIL> / password123")
    print("- <EMAIL> / admin123") 
    print("- <EMAIL> / demo123")
    
    print("\n🚀 Fonctionnalités disponibles:")
    print("- Login/Signup moderne avec toggle")
    print("- Authentification OAuth (Google, Facebook, Apple) - Démo")
    print("- Création de compte avec email/mot de passe")
    print("- Validation des formulaires")
    print("- Messages de succès/erreur")
    print("- Redirection automatique après connexion")

if __name__ == '__main__':
    test_auth_system()
